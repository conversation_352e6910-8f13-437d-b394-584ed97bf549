export interface TrafficDataPoint {
  time: string;
  timestamp: string;
  traffic: number;
  anomaly: number;
  packet_rate: number;
  bandwidth_utilization: number;
  total_packets: number;
}

export interface ProtocolData {
  name: string;
  value: number;
  percent: number;
}

export interface PacketDistribution {
  tcp_percent: number;
  udp_percent: number;
  icmp_percent: number;
  other_percent: number;
}

export interface PacketStatistics {
  avg_packet_rate: number;
  peak_packet_rate: number;
  total_packets_period: number;
  avg_bandwidth_util: number;
  peak_bandwidth_util: number;
  packet_distribution?: PacketDistribution;
}

export interface TrafficResponse {
  traffic_data: TrafficDataPoint[];
  protocol_data: ProtocolData[];
  packet_stats: PacketStatistics;
  timeframe: string;
  total_points: number;
}

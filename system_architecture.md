# Flow Sentinel IDS - High-Level System Architecture

## Mermaid Diagram Code

```mermaid
graph LR
    %% External Entities
    ATTACKER[🔴 Attacker<br/>DDoS/DoS/PortScan<br/>Malicious Traffic]
    USERS[👥 Legitimate Users<br/>Normal Traffic]
    SERVER[🖥️ Protected Server<br/>Target System]

    %% Flow Sentinel IDS System
    subgraph IDS["🛡️ FLOW SENTINEL IDS"]
        direction TB

        %% Network Capture
        NETWORK[📡 Network Monitoring<br/>Packet Capture<br/>Interface: wlan0/eth0]

        %% Processing Pipeline
        CAPTURE[📦 Packet Processing<br/>• Scapy Real-time Capture<br/>• Traffic Filtering<br/>• Flow Extraction]

        FEATURES[⚙️ Feature Engineering<br/>• 13+ Network Features<br/>• Flow Duration Analysis<br/>• Connection Rate Calculation]

        ML[🧠 Machine Learning Engine<br/>• Decision Tree Classification<br/>• Multi-source Correlation<br/>• DDoS Pattern Detection]

        ALERTS[🚨 Alert System<br/>• Real-time Notifications<br/>• Threat Classification<br/>• Alert Deduplication]

        DASHBOARD[📊 Web Dashboard<br/>• Live Monitoring<br/>• Analytics & Reports<br/>• System Control]
    end

    %% Data Flow
    ATTACKER -.->|Malicious Traffic| NETWORK
    USERS -.->|Normal Traffic| NETWORK
    NETWORK --> CAPTURE
    CAPTURE --> FEATURES
    FEATURES --> ML
    ML --> ALERTS
    ALERTS --> DASHBOARD

    %% Protection Flow
    NETWORK -.->|Monitored Traffic| SERVER
    ALERTS -.->|🚨 Security Alerts| SERVER
    DASHBOARD -.->|📋 Security Reports| SERVER

    %% Styling
    classDef attacker fill:#ffcdd2,stroke:#d32f2f,stroke-width:3px
    classDef users fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef server fill:#e1f5fe,stroke:#1976d2,stroke-width:3px
    classDef idsComponent fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef idsBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:4px

    class ATTACKER attacker
    class USERS users
    class SERVER server
    class NETWORK,CAPTURE,FEATURES,ML,ALERTS,DASHBOARD idsComponent
    class IDS idsBox
```

## High-Level Architecture Overview

### System Flow:

**Attacker/Users → Network → IDS Processing Pipeline → Protected Server**

### IDS Internal Components:

1. **📡 Network Monitoring**: Real-time packet capture from network interfaces
2. **📦 Packet Processing**: Traffic filtering and flow extraction using Scapy
3. **⚙️ Feature Engineering**: Extraction of 13+ critical network features
4. **🧠 ML Engine**: Decision Tree classification with multi-source correlation
5. **🚨 Alert System**: Real-time threat notifications and classification
6. **📊 Web Dashboard**: Live monitoring interface with analytics

### Key Protection Flow:

1. **Traffic Monitoring**: All network traffic is continuously monitored
2. **Threat Detection**: ML algorithms analyze traffic patterns in real-time
3. **Alert Generation**: Suspicious activities trigger immediate alerts
4. **Dashboard Notification**: Security team receives instant notifications
5. **Server Protection**: Protected systems are informed of threats

### Technology Highlights:

- **Real-time Processing**: <2 second threat detection response
- **Multi-source Analysis**: Detects coordinated attacks from multiple IPs
- **Web-based Interface**: Modern React dashboard for easy monitoring
- **Machine Learning**: Advanced Decision Tree classification
- **Comprehensive Coverage**: DDoS, DoS, Port Scanning, and intrusion detection

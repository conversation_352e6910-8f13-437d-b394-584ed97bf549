
#!/usr/bin/env python
"""
Setup script to create model files directories and placeholder files
for the Flow Sentinel IDS Django backend.
"""
import os
import numpy as np
import joblib
from sklearn.tree import DecisionTreeClassifier

def main():
    print("Setting up model files for Flow Sentinel IDS...")
    
    # Create model files directory
    model_dir = os.path.join("ids", "model_files")
    os.makedirs(model_dir, exist_ok=True)
    
    # Create placeholder model
    print("Creating placeholder Decision Tree model...")
    dt_model = DecisionTreeClassifier()
    dt_model.fit([[0, 0], [1, 1]], [0, 1])  # Dummy training
    model_path = os.path.join(model_dir, "DecisionTree_model.pkl")
    joblib.dump(dt_model, model_path)
    
    # Create placeholder label array
    print("Creating placeholder label array...")
    label_array = np.array(['Benign', 'DoS', 'DDoS', 'Botnet', 'Brute Force'])
    label_path = os.path.join(model_dir, "label_array.npy")
    np.save(label_path, label_array)
    
    # Create placeholder feature columns
    print("Creating placeholder feature columns file...")
    feature_columns = [
        'Conn_Rate',
        'Flow Duration',
        'Flow IAT Mean',
        'Flow IAT Max',
        'Fwd Seg Size Min',
        'Bwd Pkt Len Mean',
        'TotLen Fwd Pkts',
        'Init Fwd Win Byts',
        'Init Bwd Win Byts'
    ]
    feature_path = os.path.join(model_dir, "DecisionTree_feature_columns.txt")
    with open(feature_path, 'w') as f:
        f.write('\n'.join(feature_columns))
    
    print("Setup complete! Replace placeholder files with your actual model files.")

if __name__ == "__main__":
    main()

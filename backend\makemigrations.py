
#!/usr/bin/env python
"""Script to simplify database migration."""
import os
import django
import sys

def main():
    """Run migrations for the IDS app."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'flow_sentinel.settings')
    django.setup()
    
    # Import Django's command module
    from django.core.management import call_command
    
    try:
        # Make migrations for the ids app
        print("Making migrations for the IDS app...")
        call_command('makemigrations', 'ids', interactive=False)
        
        # Apply migrations
        print("Applying migrations...")
        call_command('migrate')
        
        print("Database migration completed successfully!")
    except KeyboardInterrupt:
        print("\nMigration process was interrupted.")
        sys.exit(1)
    except Exception as e:
        print(f"Error during migration: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()

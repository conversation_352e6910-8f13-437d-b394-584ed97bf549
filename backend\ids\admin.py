
from django.contrib import admin
from .models import IDSAlert, IDSLog

@admin.register(IDSAlert)
class IDSAlertAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'src_ip', 'dst_ip', 'attack_type', 'status')
    list_filter = ('status', 'attack_type')
    search_fields = ('src_ip', 'dst_ip', 'attack_type')

@admin.register(IDSLog)
class IDSLogAdmin(admin.ModelAdmin):
    list_display = ('timestamp', 'level', 'message')
    list_filter = ('level',)
    search_fields = ('message',)

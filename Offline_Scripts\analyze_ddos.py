#!/usr/bin/env python3

import pandas as pd
import sys

def analyze_ddos_detection():
    """Analyze why flows were detected as DDoS attacks"""
    
    try:
        # Load the enhanced results
        df = pd.read_csv('DDoS horrayyyyyyy/results_enhanced.csv')
        
        # Filter for DDoS attacks
        ddos_flows = df[df['Prediction'] == 'DDoS attack']
        
        print('=== DDoS ATTACK ANALYSIS ===')
        print(f'Total flows in dataset: {len(df)}')
        print(f'Total DDoS flows detected: {len(ddos_flows)}')
        print(f'Percentage of flows classified as DDoS: {len(ddos_flows)/len(df)*100:.2f}%')
        
        # Analyze by target IP
        target_analysis = ddos_flows.groupby('Dst IP').agg({
            'Src IP': lambda x: x.nunique(),
            'Prediction': 'count'
        }).rename(columns={'Src IP': 'unique_sources', 'Prediction': 'total_flows'})
        
        print(f'\n=== TARGET IP ANALYSIS ===')
        for target_ip, stats in target_analysis.iterrows():
            print(f'Target: {target_ip}')
            print(f'  - Unique attacking sources: {stats["unique_sources"]}')
            print(f'  - Total DDoS flows: {stats["total_flows"]}')
            
            # Show the attacking source IPs
            attacking_sources = ddos_flows[ddos_flows['Dst IP'] == target_ip]['Src IP'].unique()
            print(f'  - Source IPs: {list(attacking_sources)}')
            print()
        
        print(f'=== SUMMARY ===')
        print(f'Total unique source IPs involved in DDoS: {ddos_flows["Src IP"].nunique()}')
        print(f'Unique source IPs: {list(ddos_flows["Src IP"].unique())}')
        
        # Check if this meets the DDoS criteria (3+ sources)
        unique_sources = ddos_flows['Src IP'].nunique()
        print(f'\n=== DDoS DETECTION CRITERIA ANALYSIS ===')
        print(f'Number of unique malicious source IPs: {unique_sources}')
        print(f'DDoS threshold (minimum sources): 3')
        print(f'Meets DDoS criteria: {"YES" if unique_sources >= 3 else "NO"}')
        
        if unique_sources >= 3:
            print(f'\n✅ LEGITIMATE DDoS DETECTION')
            print(f'The system correctly detected a DDoS attack because:')
            print(f'  - {unique_sources} different source IPs were attacking the same target')
            print(f'  - This exceeds the minimum threshold of 3 sources required for DDoS classification')
            print(f'  - All attacking sources were first classified as malicious by the ML model')
            print(f'  - The system then upgraded the classification from "DoS attack" to "DDoS attack"')
        else:
            print(f'\n❌ POTENTIAL FALSE POSITIVE')
            print(f'Only {unique_sources} source IPs detected - this should be classified as DoS, not DDoS')
            
    except Exception as e:
        print(f"Error analyzing DDoS detection: {e}")
        return False
    
    return True

if __name__ == "__main__":
    analyze_ddos_detection()

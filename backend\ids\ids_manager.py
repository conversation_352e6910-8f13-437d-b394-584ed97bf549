
import threading
import json
import asyncio
import logging
import time
import os
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from .models import ID<PERSON>lert, IDSLog
from .realtime_ids import RealTimeIDS

logger = logging.getLogger("django")


class IDSManager:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(IDSManager, cls).__new__(cls)
            cls._instance.ids = None
            cls._instance.is_running = False
            cls._instance.thread = None
            cls._instance.channel_layer = get_channel_layer()
            cls._instance._last_stats = None  # Store last stats for change detection
        return cls._instance

    def start_ids_with_settings(self, settings, interface=None, interval=None):
        """Start IDS with settings from the database"""
        if self.is_running:
            return {"status": "error", "message": "IDS is already running"}

        try:
            # Use provided interface or get from settings
            if interface is None:
                interface = settings.capture_interface

            # Use provided interval or get from settings
            if interval is None:
                interval = settings.capture_interval

            # Use default model paths
            model_path = "./ids/model_files/DecisionTree_model.pkl"
            label_array_path = "./ids/model_files/label_array.npy"
            feature_columns_path = "./ids/model_files/DecisionTree_feature_columns.txt"
            scaler_path = "./ids/model_files/scaler.pkl"

            # Initialize the IDS instance with settings
            self.ids = RealTimeIDS(
                model_path=model_path,
                label_array_path=label_array_path,
                feature_columns_path=feature_columns_path,
                scaler_path=scaler_path,
                capture_interval=interval,
                max_idle_time=settings.max_idle_time,
                output_dir=settings.output_dir,
                conn_rate_window=5.0,  # Default value
                alert_threshold=0.7,   # Default value
                historical_window=settings.historical_window,
                pcap_capture=settings.pcap_capture,
                pcap_save_interval=600.0,  # 10 minutes
                consolidated_csv=settings.consolidated_csv,
                csv_save_interval=settings.csv_save_interval
            )

            # Hook into the IDS log and alert system
            self._hook_logging()
            self._hook_alerts()
            self._hook_stats_monitor()  # Add stats monitoring for WebSocket updates

            # Start the IDS in a separate thread
            self.thread = threading.Thread(target=self._run_ids, args=(interface,))
            self.thread.daemon = True
            self.thread.start()

            self.is_running = True

            # Start periodic cleanup
            self._start_cleanup_task()

            return {"status": "success", "message": "IDS started successfully with custom settings"}

        except Exception as e:
            logger.error(f"Error starting IDS with settings: {str(e)}")
            return {"status": "error", "message": f"Failed to start IDS: {str(e)}"}

    def start_ids(self, interface="wlan0", interval=2.0):
        """Legacy method for backward compatibility"""
        if self.is_running:
            return {"status": "error", "message": "IDS is already running"}

        try:
            # Initialize the IDS instance with paths to model files
            # In production, these paths should be configured properly
            self.ids = RealTimeIDS(
                model_path="./ids/model_files/DecisionTree_model.pkl",
                label_array_path="./ids/model_files/label_array.npy",
                feature_columns_path="./ids/model_files/DecisionTree_feature_columns.txt",
                scaler_path="./ids/model_files/scaler.pkl",
                capture_interval=interval,
                pcap_save_interval=600.0  # 10 minutes
            )

            # Hook into the IDS log and alert system
            self._hook_logging()
            self._hook_alerts()
            self._hook_stats_monitor()  # Add stats monitoring for WebSocket updates

            # Start the IDS in a separate thread
            self.thread = threading.Thread(target=self._run_ids, args=(interface,))
            self.thread.daemon = True
            self.thread.start()

            self.is_running = True
            return {"status": "success", "message": "IDS started successfully"}

        except Exception as e:
            logger.error(f"Error starting IDS: {str(e)}")
            return {"status": "error", "message": f"Failed to start IDS: {str(e)}"}

    def stop_ids(self):
        if not self.is_running:
            return {"status": "error", "message": "IDS is not running"}

        try:
            if self.ids:
                self.ids.stop()

            self.is_running = False
            self.ids = None

            # Broadcast status update that system is stopped
            self._broadcast_status_update({"running": False})

            return {"status": "success", "message": "IDS stopped successfully"}

        except Exception as e:
            logger.error(f"Error stopping IDS: {str(e)}")
            return {"status": "error", "message": f"Failed to stop IDS: {str(e)}"}



    def get_status(self):
        status_data = {
            "running": self.is_running,
            "stats": self.ids.stats if self.ids else None,
        }

        # Only broadcast status updates when there are actual changes
        # This prevents unnecessary WebSocket spam on every API call
        if self.is_running and self.ids:
            # Check if stats have changed since last broadcast
            current_stats_hash = hash(str(self.ids.stats)) if self.ids.stats else 0
            if not hasattr(self, '_last_stats_hash') or self._last_stats_hash != current_stats_hash:
                self._broadcast_status_update(status_data)
                self._last_stats_hash = current_stats_hash

        return status_data

    def _run_ids(self, interface):
        try:
            self.ids.start(interface)
        except Exception as e:
            logger.error(f"Error in IDS thread: {str(e)}")
            self.is_running = False

    def _hook_logging(self):
        # Replace the standard logger handler with one that sends logs to WebSocket
        original_warning = logging.Logger.warning
        original_info = logging.Logger.info
        original_error = logging.Logger.error

        def custom_warning(self, msg, *args, **kwargs):
            # Call original method
            original_warning(self, msg, *args, **kwargs)

            # Save to DB and send via WebSocket
            IDSManager._instance._handle_log("WARNING", msg)

        def custom_info(self, msg, *args, **kwargs):
            # Call original method
            original_info(self, msg, *args, **kwargs)

            # Save to DB and send via WebSocket
            IDSManager._instance._handle_log("INFO", msg)

        def custom_error(self, msg, *args, **kwargs):
            # Call original method
            original_error(self, msg, *args, **kwargs)

            # Save to DB and send via WebSocket
            IDSManager._instance._handle_log("ERROR", msg)

        # Replace the methods
        logging.Logger.warning = custom_warning
        logging.Logger.info = custom_info
        logging.Logger.error = custom_error

    def _hook_alerts(self):
        # Patch the process_alerts method to send alerts to WebSocket
        original_process_alerts = self.ids.process_alerts

        def custom_process_alerts(results_df):
            # Call original method
            original_process_alerts(results_df)

            # Process the alerts we just generated
            if hasattr(self.ids, "alert_history") and self.ids.alert_history:
                for alert in list(self.ids.alert_history)[
                    -5:
                ]:  # Get the 5 most recent alerts
                    self._handle_alert(alert)

            # Also broadcast stats update after processing alerts
            self._check_and_broadcast_stats()

        # Replace the method
        self.ids.process_alerts = custom_process_alerts

    def _hook_stats_monitor(self):
        """Set up periodic stats monitoring and broadcasting"""
        threading.Thread(target=self._stats_monitor_thread, daemon=True).start()

    def _stats_monitor_thread(self):
        """Thread that periodically checks for stats changes and broadcasts updates"""
        while self.is_running:
            try:
                if self.ids:
                    self._check_and_broadcast_stats()
            except Exception as e:
                logger.error(f"Error in stats monitor: {str(e)}")
            time.sleep(2)  # Check every 2 seconds

    def _check_and_broadcast_stats(self):
        """Check if stats have changed and broadcast updates if they have"""
        if not self.ids or not hasattr(self.ids, "stats"):
            return

        current_stats = self.ids.stats

        # Only send update if stats have changed
        if current_stats != self._last_stats:
            self._last_stats = current_stats.copy() if current_stats else None
            self._broadcast_status_update({
                "running": self.is_running,
                "stats": current_stats
            })

    def _broadcast_status_update(self, status_data):
        """Broadcast status updates to all WebSocket clients"""
        try:
            # Add type field for WebSocket message handling
            message = {
                "type": "status_update",
                "data": status_data
            }

            async_to_sync(self.channel_layer.group_send)(
                "ids_logs",  # Use existing group for simplicity
                {
                    "type": "send_status",
                    "message": message
                }
            )
        except Exception as e:
            logger.error(f"Error broadcasting status update: {str(e)}")

    def _handle_log(self, level, message):
        # Create log entry in DB
        log = IDSLog.objects.create(level=level, message=message)

        # Send via WebSocket
        try:
            async_to_sync(self.channel_layer.group_send)(
                "ids_logs",
                {
                    "type": "send_log",  # Use a type that matches a handler in your consumer
                    "message": message,
                    "level": level,
                },
            )

        except Exception as e:
            logger.error(f"Error sending log to WebSocket: {str(e)}")

    def _handle_alert(self, alert):
        # Create alert entry in DB with all required fields
        from django.utils import timezone

        db_alert = IDSAlert.objects.create(
            timestamp=timezone.datetime.fromtimestamp(alert.get("timestamp", time.time())),
            src_ip=alert["src_ip"],
            dst_ip=alert["dst_ip"],
            src_port=alert["src_port"],
            dst_port=alert["dst_port"],
            protocol=alert.get("protocol", "TCP"),  # Default to TCP if not specified
            attack_type=alert["attack_type"],
            status='new',  # Default status
            description=f"{alert['attack_type']} attack detected from {alert['src_ip']}:{alert['src_port']} to {alert['dst_ip']}:{alert['dst_port']}"
        )

        # Format target string
        target = f"{alert['dst_ip']}:{alert['dst_port']}"

        # Send via WebSocket
        try:
            async_to_sync(self.channel_layer.group_send)(
                "ids_alerts",
                {
                    "type": "send_alert",  # Use a type that matches your consumer's alert handler
                    "id": db_alert.id,
                    "timestamp": db_alert.timestamp.strftime("%H:%M:%S"),
                    "attack_type": alert["attack_type"],
                    "source": f"{alert['src_ip']}:{alert['src_port']}",
                    "target": target,
                },
            )

        except Exception as e:
            logger.error(f"Error sending alert to WebSocket: {str(e)}")

    def _start_cleanup_task(self):
        """Start periodic cleanup task"""
        threading.Thread(target=self._cleanup_thread, daemon=True).start()

    def _cleanup_thread(self):
        """Thread that periodically cleans up old alerts"""
        while self.is_running:
            try:
                # Sleep for 10 minutes between cleanups
                time.sleep(600)
                if self.is_running:
                    self._cleanup_old_alerts()
            except Exception as e:
                logger.error(f"Error in cleanup thread: {str(e)}")

    def _cleanup_old_alerts(self):
        """Clean up old alerts to prevent database bloat"""
        try:
            from django.utils import timezone
            from datetime import timedelta

            # Keep only the latest 100 alerts
            total_alerts = IDSAlert.objects.count()
            if total_alerts > 100:
                # Get IDs of alerts to keep (latest 100)
                keep_ids = list(IDSAlert.objects.order_by('-timestamp')[:100].values_list('id', flat=True))
                excess_alerts = IDSAlert.objects.exclude(id__in=keep_ids)
                deleted_count = excess_alerts.count()
                excess_alerts.delete()

                logger.info(f"Cleaned up {deleted_count} old alerts, keeping latest 100")

            # Also clean up very old alerts (older than 7 days)
            cutoff_date = timezone.now() - timedelta(days=7)
            old_alerts = IDSAlert.objects.filter(timestamp__lt=cutoff_date)
            old_count = old_alerts.count()
            if old_count > 0:
                old_alerts.delete()
                logger.info(f"Cleaned up {old_count} alerts older than 7 days")

        except Exception as e:
            logger.error(f"Error during alert cleanup: {str(e)}")

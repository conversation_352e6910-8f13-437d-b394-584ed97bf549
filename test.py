from scapy.all import get_windows_if_list
# get the name of the network interface
def get_interface_name():
    # ImportError: cannot import name 'get_windows_if_list' from 'scapy.all' 

    # get the list of network interfaces
    interfaces = get_windows_if_list()
    # print the list of network interfaces
    print("List of network interfaces:")
    for i, interface in enumerate(interfaces):
        print(f"{i}: {interface['name']}")
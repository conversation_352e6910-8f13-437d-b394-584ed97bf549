/**
 * Notification Service for IDS Alerts
 * Handles both browser notifications and toast notifications
 * Respects user settings for notification preferences
 */

export interface NotificationSettings {
  enableNotifications: boolean;
  enableBrowserNotifications: boolean;
  enableSounds: boolean;
}

export interface AlertNotification {
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  source?: string;
  attackType?: string;
}

interface AttackSession {
  isActive: boolean;
  startTime: number;
  lastActivityTime: number;
  attackTypes: Set<string>;
}

class NotificationService {
  private settings: NotificationSettings = {
    enableNotifications: true,
    enableBrowserNotifications: true,
    enableSounds: true,
  };

  private permissionGranted = false;
  private toastFunction: ((notification: any) => void) | null = null;

  // Attack session tracking
  private attackSession: AttackSession = {
    isActive: false,
    startTime: 0,
    lastActivityTime: 0,
    attackTypes: new Set()
  };

  // Session timeout (30 seconds of no activity = session ended)
  private readonly SESSION_TIMEOUT = 30000;

  constructor() {
    this.loadSettings();
    this.requestBrowserPermission();
  }

  /**
   * Load notification settings from localStorage
   */
  private loadSettings(): void {
    try {
      const saved = localStorage.getItem('notificationSettings');
      if (saved) {
        this.settings = { ...this.settings, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('Failed to load notification settings:', error);
    }
  }

  /**
   * Save notification settings to localStorage
   */
  private saveSettings(): void {
    try {
      localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    } catch (error) {
      console.warn('Failed to save notification settings:', error);
    }
  }

  /**
   * Request browser notification permission
   */
  private async requestBrowserPermission(): Promise<void> {
    if ('Notification' in window) {
      try {
        const permission = await Notification.requestPermission();
        this.permissionGranted = permission === 'granted';
      } catch (error) {
        console.warn('Failed to request notification permission:', error);
        this.permissionGranted = false;
      }
    }
  }

  /**
   * Set the toast function for in-app notifications
   */
  setToastFunction(toastFn: (notification: any) => void): void {
    this.toastFunction = toastFn;
  }

  /**
   * Update notification settings
   */
  updateSettings(newSettings: Partial<NotificationSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();

    // Request permission if browser notifications are enabled
    if (newSettings.enableBrowserNotifications && !this.permissionGranted) {
      this.requestBrowserPermission();
    }
  }

  /**
   * Get current notification settings
   */
  getSettings(): NotificationSettings {
    return { ...this.settings };
  }

  /**
   * Show a notification (both browser and toast based on settings)
   */
  async showNotification(notification: AlertNotification): Promise<void> {
    if (!this.settings.enableNotifications) {
      return;
    }

    // Show toast notification
    this.showToastNotification(notification);

    // Show browser notification if enabled and permission granted
    if (this.settings.enableBrowserNotifications && this.permissionGranted) {
      this.showBrowserNotification(notification);
    }

    // Play sound if enabled
    if (this.settings.enableSounds) {
      this.playNotificationSound(notification.type);
    }
  }

  /**
   * Show toast notification
   */
  private showToastNotification(notification: AlertNotification): void {
    if (!this.toastFunction) {
      console.warn('Toast function not set');
      return;
    }

    const variant = this.getToastVariant(notification.type);
    
    this.toastFunction({
      title: notification.title,
      description: notification.message,
      variant,
    });
  }

  /**
   * Show browser notification
   */
  private showBrowserNotification(notification: AlertNotification): void {
    if (!('Notification' in window) || !this.permissionGranted) {
      return;
    }

    try {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'ids-alert', // This prevents duplicate notifications
        requireInteraction: notification.type === 'error', // Keep error notifications visible
        silent: !this.settings.enableSounds,
      });

      // Auto-close after 5 seconds for non-error notifications
      if (notification.type !== 'error') {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }

      // Handle click events
      browserNotification.onclick = () => {
        window.focus();
        browserNotification.close();
        
        // Navigate to alerts page if it's a security alert
        if (notification.type === 'error' || notification.attackType) {
          window.location.hash = '/alerts';
        }
      };
    } catch (error) {
      console.warn('Failed to show browser notification:', error);
    }
  }

  /**
   * Play notification sound
   */
  private playNotificationSound(type: string): void {
    try {
      // Create audio context for sound
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Different frequencies for different alert types
      const frequencies = {
        error: [800, 600], // High-low for alerts
        warning: [600, 500],
        info: [400],
        success: [500, 600],
      };

      const freq = frequencies[type as keyof typeof frequencies] || frequencies.info;
      
      freq.forEach((frequency, index) => {
        setTimeout(() => {
          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();
          
          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);
          
          oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
          oscillator.type = 'sine';
          
          gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
          
          oscillator.start(audioContext.currentTime);
          oscillator.stop(audioContext.currentTime + 0.2);
        }, index * 150);
      });
    } catch (error) {
      console.warn('Failed to play notification sound:', error);
    }
  }

  /**
   * Get toast variant based on notification type
   */
  private getToastVariant(type: string): 'default' | 'destructive' {
    switch (type) {
      case 'error':
        return 'destructive';
      case 'warning':
        return 'destructive';
      default:
        return 'default';
    }
  }

  /**
   * Process new attack alert and handle session tracking
   */
  async processAttackAlert(attackType: string, source: string, target?: string): Promise<void> {
    const currentTime = Date.now();
    const wasActive = this.attackSession.isActive;

    // Update attack session
    this.attackSession.lastActivityTime = currentTime;
    this.attackSession.attackTypes.add(attackType);

    // Check if this is a new attack session
    if (!this.attackSession.isActive) {
      this.attackSession.isActive = true;
      this.attackSession.startTime = currentTime;

      // Show attack started notification
      await this.showAttackStartedNotification(attackType, source, target);
    }

    // Start session timeout check
    this.checkSessionTimeout();
  }

  /**
   * Check if attack session has timed out
   */
  private checkSessionTimeout(): void {
    setTimeout(() => {
      const currentTime = Date.now();
      const timeSinceLastActivity = currentTime - this.attackSession.lastActivityTime;

      if (this.attackSession.isActive && timeSinceLastActivity >= this.SESSION_TIMEOUT) {
        this.endAttackSession();
      }
    }, this.SESSION_TIMEOUT + 1000); // Check 1 second after timeout
  }

  /**
   * End the current attack session
   */
  private async endAttackSession(): Promise<void> {
    if (!this.attackSession.isActive) {
      return;
    }

    const attackTypes = Array.from(this.attackSession.attackTypes);
    this.attackSession.isActive = false;
    this.attackSession.attackTypes.clear();

    // Show attack ended notification
    await this.showAttackEndedNotification(attackTypes);
  }

  /**
   * Show attack started notification
   */
  private async showAttackStartedNotification(attackType: string, source: string, target?: string): Promise<void> {
    const notification: AlertNotification = {
      title: 'Security Alert - Attack Detected',
      message: `${attackType} attack detected from ${source}${target ? ` targeting ${target}` : ''}. Monitoring for additional threats...`,
      type: 'error',
      source,
      attackType,
    };

    await this.showNotification(notification);
  }

  /**
   * Show attack ended notification
   */
  private async showAttackEndedNotification(attackTypes: string[]): Promise<void> {
    const attackTypesList = attackTypes.length > 1
      ? `${attackTypes.slice(0, -1).join(', ')} and ${attackTypes[attackTypes.length - 1]}`
      : attackTypes[0];

    const notification: AlertNotification = {
      title: 'Security Alert - Attack Session Ended',
      message: `${attackTypesList} attack session has ended. Network appears secure.`,
      type: 'info',
    };

    await this.showNotification(notification);
  }

  /**
   * Get current attack session status
   */
  getAttackSessionStatus(): { isActive: boolean; attackTypes: string[]; duration?: number } {
    return {
      isActive: this.attackSession.isActive,
      attackTypes: Array.from(this.attackSession.attackTypes),
      duration: this.attackSession.isActive
        ? Date.now() - this.attackSession.startTime
        : undefined
    };
  }

  /**
   * Show security alert notification (legacy method for backward compatibility)
   */
  async showSecurityAlert(attackType: string, source: string, target?: string): Promise<void> {
    // Use the new session-aware method
    await this.processAttackAlert(attackType, source, target);
  }

  /**
   * Show system notification
   */
  async showSystemNotification(title: string, message: string, type: 'info' | 'warning' | 'error' | 'success' = 'info'): Promise<void> {
    const notification: AlertNotification = {
      title,
      message,
      type,
    };

    await this.showNotification(notification);
  }

  /**
   * Check if browser notifications are supported
   */
  isBrowserNotificationSupported(): boolean {
    return 'Notification' in window;
  }

  /**
   * Check if browser notifications are enabled and permitted
   */
  isBrowserNotificationEnabled(): boolean {
    return this.settings.enableBrowserNotifications && this.permissionGranted;
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default notificationService;

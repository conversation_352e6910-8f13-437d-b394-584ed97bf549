import { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { NetworkPacketChart } from "./NetworkFlowChart";
import { ProtocolDistributionChart } from "./ProtocolDistributionChart";
import { fetchTrafficData } from "@/services/trafficService";
import { TrafficResponse } from "@/types/traffic";
import { useToast } from "@/hooks/use-toast";
import { Network, RefreshCw, TrendingUp, Activity } from "lucide-react";

interface NetworkPacketDashboardProps {
  className?: string;
}

export function NetworkPacketDashboard({ className }: NetworkPacketDashboardProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [timeframe, setTimeframe] = useState("1h");
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [trafficData, setTrafficData] = useState<TrafficResponse>({
    traffic_data: [],
    protocol_data: [],
    packet_stats: {
      avg_packet_rate: 0,
      peak_packet_rate: 0,
      total_packets_period: 0,
      avg_bandwidth_util: 0,
      peak_bandwidth_util: 0,
      packet_distribution: {
        tcp_percent: 0,
        udp_percent: 0,
        icmp_percent: 0,
        other_percent: 0,
      },
    },
    timeframe: "1h",
    total_points: 0
  });
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(0);
  const lastUpdateRef = useRef(0);
  const isUpdatingRef = useRef(false);

  // Enhanced data fetching with error handling and persistence
  const loadTrafficData = useCallback(async (isInitial: boolean = false) => {
    const now = Date.now();

    // Debouncing: Skip if last update was less than 2 seconds ago or if already updating
    if (!isInitial && (now - lastUpdateRef.current < 2000 || isUpdatingRef.current)) {
      console.warn('Skipping dashboard traffic update: too frequent or already updating');
      return;
    }

    lastUpdateRef.current = now;
    isUpdatingRef.current = true;

    try {
      if (isInitial) {
        setIsLoading(true);
      } else {
        setIsUpdating(true);
      }

      const data = await fetchTrafficData(timeframe, now);

      // Only update if we got valid data
      if (data && (data.traffic_data.length > 0 || data.total_points >= 0)) {
        setTrafficData(data);
        setLastUpdateTime(now);
      } else {
        console.warn('Received empty dashboard traffic data, keeping previous data');
      }

      if (isInitial) {
        setIsInitialized(true);
      }
    } catch (error) {
      console.error('Error fetching dashboard traffic data:', error);

      // Handle different types of errors
      if (error.name === 'AbortError') {
        console.warn('Dashboard traffic API timeout - backend is slow, but dashboard will continue with cached data');
        // Don't show error toast for timeouts - the service handles fallback
      } else {
        // Only show toast for non-timeout errors and only on initial load
        if (isInitial || !isInitialized) {
          toast({
            title: "Connection Error",
            description: "Could not fetch network packet data",
            variant: "destructive"
          });
        }
      }
    } finally {
      if (isInitial) {
        setIsLoading(false);
      } else {
        setIsUpdating(false);
      }
      isUpdatingRef.current = false;
    }
  }, [timeframe, isInitialized, toast]);

  useEffect(() => {
    loadTrafficData(true);

    let intervalId: NodeJS.Timeout | null = null;

    if (autoRefresh && isInitialized) {
      // Poll for updates every 3 seconds when auto-refresh is enabled
      intervalId = setInterval(() => loadTrafficData(false), 3000);
    }

    // Handle browser throttling - force update when tab becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isInitialized && autoRefresh) {
        console.log('Tab became visible, forcing dashboard traffic update');
        loadTrafficData(false);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [timeframe, autoRefresh, isInitialized, loadTrafficData]);

  const handleTimeframeChange = (value: string) => {
    setTimeframe(value);
  };

  const handleManualRefresh = () => {
    loadTrafficData(false);
  };

  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
  };

  // Format numbers for display
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Network className="h-6 w-6 text-primary" />
          <h1 className="text-3xl font-bold">Network Packet Distribution</h1>
          {isUpdating && (
            <Badge variant="outline" className="animate-pulse">
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
              Updating...
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleManualRefresh}
            disabled={isUpdating}
          >
            <RefreshCw className={`h-4 w-4 mr-1 ${isUpdating ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button
            variant={autoRefresh ? "default" : "outline"}
            size="sm"
            onClick={toggleAutoRefresh}
          >
            <Activity className="h-4 w-4 mr-1" />
            Auto Refresh
          </Button>
          
          <Select value={timeframe} onValueChange={handleTimeframeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">1 Hour</SelectItem>
              <SelectItem value="6h">6 Hours</SelectItem>
              <SelectItem value="24h">24 Hours</SelectItem>
              <SelectItem value="7d">7 Days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Flows</CardTitle>
            <Activity className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
            ) : (
              <>
                <div className="text-2xl font-bold">{formatNumber(trafficData.packet_stats?.total_packets_period || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  in {timeframe}
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Avg Packet Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
            ) : (
              <>
                <div className="text-2xl font-bold">{trafficData.packet_stats?.avg_packet_rate || 0}</div>
                <p className="text-xs text-muted-foreground">packets/second</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Peak Packet Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
            ) : (
              <>
                <div className="text-2xl font-bold">{trafficData.packet_stats?.peak_packet_rate || 0}</div>
                <p className="text-xs text-muted-foreground">packets/second</p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Bandwidth Util</CardTitle>
            <Network className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
            ) : (
              <>
                <div className="text-2xl font-bold">{trafficData.packet_stats?.avg_bandwidth_util || 0}%</div>
                <p className="text-xs text-muted-foreground">
                  Peak: {trafficData.packet_stats?.peak_bandwidth_util || 0}%
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Data Points</CardTitle>
            <Activity className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
            ) : (
              <>
                <div className="text-2xl font-bold">{trafficData.total_points}</div>
                <p className="text-xs text-muted-foreground">
                  {timeframe} timeframe
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Main Packet Charts */}
      <NetworkPacketChart className="mb-6" showHeader={false} showStats={false} />

      {/* Protocol Distribution */}
      <ProtocolDistributionChart 
        trafficData={trafficData.traffic_data}
        protocolData={trafficData.protocol_data}
        isLoading={isLoading}
        className="mb-6"
      />

      {/* Footer Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Network Packet Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground space-y-2">
            <p>
              • <strong>Continuous Monitoring:</strong> Real-time network packet data is captured and analyzed every {timeframe === '1h' ? '5 minutes' : timeframe === '6h' ? '30 minutes' : timeframe === '24h' ? '1 hour' : '1 day'}.
            </p>
            <p>
              • <strong>Packet Classification:</strong> Traffic is categorized by protocol (TCP, UDP, ICMP, Other) and analyzed for patterns.
            </p>
            <p>
              • <strong>Anomaly Detection:</strong> Suspicious packets are identified and highlighted in the visualization.
            </p>
            <p>
              • <strong>Auto Refresh:</strong> Data automatically updates every 3 seconds when enabled.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

#!/usr/bin/env python3
"""
Offline Flow Extractor Script

This script captures network packets and extracts flows, saving them to CSV format.
It replicates the exact flow extraction methods used by the main IDS system.

Usage:
    python flow_extractor.py --interface wlan0 --duration 60 --output flows.csv
"""

import argparse
import time
import sys
import os
import signal
import logging
from collections import defaultdict
from typing import Dict, Any, Optional, Tuple, List
import pandas as pd
import numpy as np

# Network packet processing
from scapy.all import sniff, IP, TCP, UDP

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineFlowExtractor:
    """
    Offline flow extractor that captures packets and extracts network flows.
    Based on the RealTimeIDS flow extraction logic.
    """
    
    def __init__(self, capture_interval=10):
        """
        Initialize the flow extractor.
        
        Args:
            capture_interval: Time window for flow aggregation (seconds)
        """
        self.capture_interval = capture_interval
        self.running = False
        
        # Flow storage
        self.flows = defaultdict(lambda: {
            'start_time': None,
            'last_timestamp': None,
            'forward_packets': [],  # (timestamp, size, flags)
            'backward_packets': [], # (timestamp, size, flags)
            'protocol': None,
            'src_ip': None,
            'dst_ip': None,
            'src_port': None,
            'dst_port': None,
            'active_start': None,
            'active_times': [],
            'idle_start': None,
            'fwd_win_bytes': None,
            'bwd_win_bytes': None,
        })
        
        # Completed flows ready for processing
        self.completed_flows = []
        
        # Connection tracking for Conn_Rate calculation
        self.port_connections = defaultdict(set)
        self.connection_timestamps = defaultdict(list)
        
        # Statistics
        self.stats = {
            'total_packets': 0,
            'total_flows': 0,
            'protocol_distribution': defaultdict(int)
        }
        
        # Timing
        self.timestamp_first_packet = None
        
        # All extracted flows for final CSV
        self.all_flows = []

    def is_legitimate_traffic(self, src_ip: str, dst_ip: str, src_port: int, dst_port: int) -> bool:
        """
        Filter out legitimate traffic that shouldn't be analyzed.
        Made less restrictive to capture more flows for analysis.
        """
        # Only filter out very basic system traffic
        legitimate_ports = {
            53,    # DNS (already filtered by sniff filter)
            67, 68, # DHCP
            123,   # NTP
        }

        # Filter out traffic to/from legitimate ports
        if src_port in legitimate_ports or dst_port in legitimate_ports:
            return True

        # Filter out local network management traffic
        if (src_ip.startswith('169.254.') or dst_ip.startswith('169.254.') or  # Link-local
            src_ip.startswith('224.') or dst_ip.startswith('224.') or          # Multicast
            src_ip == '***************' or dst_ip == '***************'):      # Broadcast
            return True

        return False

    def get_flow_key(self, packet) -> Optional[Tuple]:
        """
        Generate a key for the flow based on the 5-tuple.
        Returns None if the packet should be filtered out.
        """
        if IP in packet:
            ip_src = packet[IP].src
            ip_dst = packet[IP].dst
            
            # Get port and protocol information
            src_port = 0
            dst_port = 0
            protocol = packet[IP].proto
            
            if TCP in packet:
                src_port = packet[TCP].sport
                dst_port = packet[TCP].dport
                protocol = 6  # TCP
            elif UDP in packet:
                src_port = packet[UDP].sport
                dst_port = packet[UDP].dport
                protocol = 17  # UDP
            
            # Filter out legitimate traffic
            if self.is_legitimate_traffic(ip_src, ip_dst, src_port, dst_port):
                return None
            
            # Create bidirectional flow key (normalize direction)
            if (ip_src, src_port) < (ip_dst, dst_port):
                return (ip_src, ip_dst, src_port, dst_port, protocol)
            else:
                return (ip_dst, ip_src, dst_port, src_port, protocol)
        
        return None

    def get_packet_direction(self, packet, flow_key) -> Optional[str]:
        """Determine if packet is forward or backward in the flow."""
        if IP not in packet:
            return None
            
        ip_src = packet[IP].src
        src_port = 0
        
        if TCP in packet:
            src_port = packet[TCP].sport
        elif UDP in packet:
            src_port = packet[UDP].sport
            
        # Check if this packet matches the forward direction of the flow
        if (ip_src, src_port) == (flow_key[0], flow_key[2]):
            return 'forward'
        else:
            return 'backward'

    def update_port_connections(self, packet, timestamp):
        """Update port connection tracking for Conn_Rate calculation."""
        if IP in packet and TCP in packet:
            src_ip = packet[IP].src
            dst_port = packet[TCP].dport
            
            # Track unique connections to this destination port
            connection_key = f"{src_ip}:{dst_port}"
            self.port_connections[dst_port].add(src_ip)
            self.connection_timestamps[dst_port].append(timestamp)
            
            # Clean old connections (older than 1 minute)
            cutoff_time = timestamp - 60
            self.connection_timestamps[dst_port] = [
                ts for ts in self.connection_timestamps[dst_port] if ts > cutoff_time
            ]

    def extract_packet_features(self, packet, timestamp, flow_key, flow_data) -> None:
        """Extract features from a packet and update the flow data."""
        direction = self.get_packet_direction(packet, flow_key)
        if direction is None:
            return
        
        # Initialize flow data for new flows
        if flow_data['start_time'] is None:
            flow_data['start_time'] = timestamp
            flow_data['last_timestamp'] = timestamp
            flow_data['active_start'] = timestamp
            flow_data['protocol'] = flow_key[4]
            flow_data['src_ip'] = flow_key[0]
            flow_data['dst_ip'] = flow_key[1]
            flow_data['src_port'] = flow_key[2]
            flow_data['dst_port'] = flow_key[3]
        
        # Update last timestamp
        flow_data['last_timestamp'] = timestamp
        
        # Get packet size
        packet_size = len(packet)
        
        # Extract TCP flags and window sizes
        tcp_flags = 0
        if TCP in packet:
            tcp_flags = packet[TCP].flags
            if direction == 'forward' and flow_data['fwd_win_bytes'] is None:
                flow_data['fwd_win_bytes'] = packet[TCP].window
            elif direction == 'backward' and flow_data['bwd_win_bytes'] is None:
                flow_data['bwd_win_bytes'] = packet[TCP].window
        
        # Store packet information
        packet_info = (timestamp, packet_size, tcp_flags)
        if direction == 'forward':
            flow_data['forward_packets'].append(packet_info)
        else:
            flow_data['backward_packets'].append(packet_info)

    def process_packet(self, packet) -> None:
        """Process a single packet and update flow records."""
        if not self.timestamp_first_packet:
            self.timestamp_first_packet = time.time()
        
        timestamp = time.time()
        
        # Update statistics
        self.stats['total_packets'] += 1
        
        # Track protocol distribution
        if IP in packet:
            if TCP in packet:
                self.stats['protocol_distribution']['TCP'] += 1
            elif UDP in packet:
                self.stats['protocol_distribution']['UDP'] += 1
            elif packet[IP].proto == 1:  # ICMP
                self.stats['protocol_distribution']['ICMP'] += 1
            else:
                self.stats['protocol_distribution']['Other'] += 1
        
        # Get flow key
        flow_key = self.get_flow_key(packet)
        if not flow_key:
            return
        
        # Update port connections for Conn_Rate calculation
        self.update_port_connections(packet, timestamp)
        
        # Update flow data
        self.extract_packet_features(packet, timestamp, flow_key, self.flows[flow_key])

    def compute_flow_features(self, flow_key, flow_data) -> Dict[str, Any]:
        """
        Compute features from flow data.
        This replicates the exact feature computation from the main IDS system.
        """
        features = {}
        
        # Basic flow information
        src_ip, dst_ip, src_port, dst_port, protocol = flow_key
        features['Src IP'] = src_ip
        features['Dst IP'] = dst_ip
        features['Src Port'] = src_port
        features['Dst Port'] = dst_port
        features['Protocol'] = protocol
        
        # Flow Duration (critical feature)
        if flow_data['start_time'] and flow_data['last_timestamp']:
            duration = flow_data['last_timestamp'] - flow_data['start_time']
            features['Flow Duration'] = int(duration * 1_000_000)  # Convert to microseconds
        else:
            features['Flow Duration'] = 0
        
        # Connection Rate (critical feature)
        unique_connections = len(self.port_connections.get(dst_port, set()))
        features['Conn_Rate'] = unique_connections
        
        # Forward and backward packets
        fwd_packets = flow_data['forward_packets']
        bwd_packets = flow_data['backward_packets']
        
        # Forward packet statistics
        fwd_lens = [size for _, size, _ in fwd_packets]
        if fwd_lens:
            features['Fwd Seg Size Min'] = min(fwd_lens)
            features['TotLen Fwd Pkts'] = sum(fwd_lens)
        else:
            features['Fwd Seg Size Min'] = 0
            features['TotLen Fwd Pkts'] = 0
        
        # Backward packet statistics
        bwd_lens = [size for _, size, _ in bwd_packets]
        if bwd_lens:
            features['Bwd Pkt Len Mean'] = sum(bwd_lens) / len(bwd_lens)
        else:
            features['Bwd Pkt Len Mean'] = 0
        
        # Window sizes (critical features)
        features['Init Fwd Win Byts'] = flow_data['fwd_win_bytes'] or 0
        features['Init Bwd Win Byts'] = flow_data['bwd_win_bytes'] or 0
        
        # Inter-arrival time statistics
        all_packets = [(ts, 'fwd') for ts, _, _ in fwd_packets] + [(ts, 'bwd') for ts, _, _ in bwd_packets]
        all_packets.sort()
        
        if len(all_packets) > 1:
            iats = [all_packets[i][0] - all_packets[i-1][0] for i in range(1, len(all_packets))]
            iats_microseconds = [iat * 1_000_000 for iat in iats]  # Convert to microseconds
            
            features['Flow IAT Mean'] = np.mean(iats_microseconds) if iats_microseconds else 0
            features['Flow IAT Max'] = max(iats_microseconds) if iats_microseconds else 0
        else:
            features['Flow IAT Mean'] = 0
            features['Flow IAT Max'] = 0
        
        return features

    def process_flows(self):
        """
        Process completed flows and prepare them for CSV export.
        Returns dataframe with flows ready for export.
        """
        # Compute features for all completed flows
        flow_features = []

        for flow_key, flow_data in self.completed_flows:
            features = self.compute_flow_features(flow_key, flow_data)
            flow_features.append(features)

        # Reset completed flows for next window
        self.completed_flows = []

        if not flow_features:
            return None

        # Create dataframe with all flow features
        flows_df = pd.DataFrame(flow_features)

        # Add timestamp for when flows were processed
        flows_df['Processing_Timestamp'] = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

        return flows_df

    def complete_active_flows(self):
        """Mark all active flows as completed for processing."""
        current_time = time.time()

        # Move all active flows to completed flows
        for flow_key, flow_data in list(self.flows.items()):
            if flow_data['start_time'] is not None:
                self.completed_flows.append((flow_key, flow_data))

        # Clear active flows
        self.flows.clear()

        logger.info(f"Completed {len(self.completed_flows)} active flows")

    def capture_packets(self, interface, duration):
        """
        Capture packets for the specified duration and extract flows.

        Args:
            interface: Network interface to capture from
            duration: Total capture duration in seconds
        """
        logger.info(f"Starting packet capture on interface {interface} for {duration} seconds")

        self.running = True
        start_time = time.time()

        try:
            while self.running and (time.time() - start_time) < duration:
                window_start = time.time()

                # Capture packets for one window interval
                try:
                    sniff(
                        iface=interface,
                        prn=self.process_packet,
                        store=False,
                        timeout=self.capture_interval,
                        filter="not port 53"  # Filter out DNS traffic
                    )
                except Exception as e:
                    logger.error(f"Error during packet capture: {str(e)}")
                    if not self.running:
                        break
                    time.sleep(1)
                    continue

                # Process flows from this window
                self.complete_active_flows()
                flows_df = self.process_flows()

                if flows_df is not None and len(flows_df) > 0:
                    self.all_flows.append(flows_df)
                    logger.info(f"Extracted {len(flows_df)} flows in this window")

                # Wait for remaining time in the interval
                elapsed = time.time() - window_start
                if elapsed < self.capture_interval:
                    time.sleep(self.capture_interval - elapsed)

        except KeyboardInterrupt:
            logger.info("Capture interrupted by user")
        finally:
            self.running = False

            # Process any remaining flows
            self.complete_active_flows()
            flows_df = self.process_flows()
            if flows_df is not None and len(flows_df) > 0:
                self.all_flows.append(flows_df)

        logger.info(f"Packet capture completed. Total packets: {self.stats['total_packets']}")

    def save_flows_to_csv(self, output_file):
        """
        Save all extracted flows to a CSV file.

        Args:
            output_file: Path to the output CSV file
        """
        if not self.all_flows:
            logger.warning("No flows to save")
            return

        # Combine all flow dataframes
        combined_df = pd.concat(self.all_flows, ignore_index=True)

        # Save to CSV
        combined_df.to_csv(output_file, index=False)

        logger.info(f"Saved {len(combined_df)} flows to {output_file}")

        # Print summary statistics
        print(f"\n=== Flow Extraction Summary ===")
        print(f"Total packets captured: {self.stats['total_packets']}")
        print(f"Total flows extracted: {len(combined_df)}")
        print(f"Protocol distribution: {dict(self.stats['protocol_distribution'])}")
        print(f"Output file: {output_file}")
        print(f"CSV columns: {list(combined_df.columns)}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False


def main():
    """Main function for the offline flow extractor."""
    parser = argparse.ArgumentParser(description='Offline Network Flow Extractor')

    parser.add_argument('--interface', '-i',
                       default='wlan0',
                       help='Network interface to capture from (default: wlan0)')

    parser.add_argument('--duration', '-d',
                       type=int, default=60,
                       help='Capture duration in seconds (default: 60)')

    parser.add_argument('--interval', '-t',
                       type=int, default=10,
                       help='Flow aggregation interval in seconds (default: 10)')

    parser.add_argument('--output', '-o',
                       default='extracted_flows.csv',
                       help='Output CSV file (default: extracted_flows.csv)')

    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create flow extractor
    extractor = OfflineFlowExtractor(capture_interval=args.interval)

    # Set up signal handlers
    signal.signal(signal.SIGINT, extractor.signal_handler)
    signal.signal(signal.SIGTERM, extractor.signal_handler)

    try:
        # Capture packets and extract flows
        extractor.capture_packets(args.interface, args.duration)

        # Save flows to CSV
        extractor.save_flows_to_csv(args.output)

        return 0

    except Exception as e:
        logger.error(f"Error during flow extraction: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

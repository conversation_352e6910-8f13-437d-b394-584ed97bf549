import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Server, 
  Monitor, 
  Cpu, 
  HardDrive, 
  Clock, 
  Globe,
  Code,
  Layers
} from "lucide-react";
import { formatUptime } from "@/services/systemResourceService";

interface SystemInfoData {
  platform: string;
  system: string;
  processor: string;
  architecture: string;
  hostname: string;
  boot_time: string;
  uptime_seconds: number;
  uptime_formatted: string;
  python_version: string;
  // Enhanced Linux/Kali-specific fields
  machine?: string;
  release?: string;
  version?: string;
  distribution?: string;
  distribution_version?: string;
  codename?: string;
  kernel_info?: string;
  is_kali?: boolean;
  kali_version?: string;
  home_url?: string;
}

interface SystemInfoProps {
  data: SystemInfoData;
}

export function SystemInfo({ data }: SystemInfoProps) {
  const bootTime = new Date(data.boot_time);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Server className="h-5 w-5" />
        <h3 className="text-lg font-semibold">System Information</h3>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* System Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              System Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Kali Linux specific display */}
              {data.is_kali ? (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Distribution:</span>
                    <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                      🐉 {data.distribution || 'Kali Linux'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Version:</span>
                    <Badge variant="secondary">{data.kali_version || data.distribution_version}</Badge>
                  </div>
                  {data.codename && (
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Codename:</span>
                      <span className="font-medium text-sm">{data.codename}</span>
                    </div>
                  )}
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Kernel:</span>
                    <span className="font-medium text-sm">{data.release}</span>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Operating System:</span>
                    <Badge variant="outline">{data.system}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Platform:</span>
                    <span className="font-medium text-sm">{data.platform}</span>
                  </div>
                </>
              )}

              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Architecture:</span>
                <Badge variant="secondary">{data.architecture}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Hostname:</span>
                <span className="font-medium flex items-center gap-1">
                  <Globe className="h-4 w-4" />
                  {data.hostname}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Hardware Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5" />
              Hardware Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <span className="text-muted-foreground block mb-1">Processor:</span>
                <span className="font-medium text-sm">{data.processor}</span>
              </div>
              {data.machine && (
                <div>
                  <span className="text-muted-foreground block mb-1">Machine Type:</span>
                  <span className="font-medium text-sm">{data.machine}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Kali Linux Specific Information */}
        {data.is_kali && (
          <Card className="border-red-200 bg-red-50 dark:bg-red-950 dark:border-red-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-800 dark:text-red-200">
                <Layers className="h-5 w-5" />
                🐉 Kali Linux Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.kernel_info && (
                  <div>
                    <span className="text-muted-foreground block mb-1">Kernel Information:</span>
                    <span className="font-medium text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded block">
                      {data.kernel_info}
                    </span>
                  </div>
                )}
                {data.home_url && (
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Official Website:</span>
                    <a
                      href={data.home_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 text-sm font-medium"
                    >
                      {data.home_url}
                    </a>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Security Focus:</span>
                  <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                    Penetration Testing
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* System Uptime */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              System Uptime
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">
                  {formatUptime(data.uptime_seconds)}
                </div>
                <div className="text-sm text-muted-foreground">
                  System has been running
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Boot Time:</span>
                  <span className="font-medium text-sm">
                    {bootTime.toLocaleDateString()} {bootTime.toLocaleTimeString()}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Uptime (formatted):</span>
                  <Badge variant="outline">{data.uptime_formatted}</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Software Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              Software Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Python Version:</span>
                <Badge variant="outline" className="flex items-center gap-1">
                  <Code className="h-3 w-3" />
                  {data.python_version}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">IDS Backend:</span>
                <Badge variant="default">Django REST API</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Frontend:</span>
                <Badge variant="default">React + TypeScript</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Status Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            System Status Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg">
              <div className="text-2xl font-bold text-green-600">Online</div>
              <div className="text-sm text-muted-foreground">System Status</div>
            </div>
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">Active</div>
              <div className="text-sm text-muted-foreground">Monitoring</div>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-950 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">Stable</div>
              <div className="text-sm text-muted-foreground">Performance</div>
            </div>
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-950 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {Math.floor(data.uptime_seconds / 86400)}d
              </div>
              <div className="text-sm text-muted-foreground">Uptime Days</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

"""
System resource monitoring views for the IDS application.
Provides API endpoints for system resource consumption data.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta
import logging
import json
import time
from django.core.cache import cache
from django.conf import settings

try:
    from ..utils.system_monitor import get_system_monitor
except ImportError:
    # Fallback import if the relative import fails
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from utils.system_monitor import get_system_monitor

logger = logging.getLogger(__name__)


class SystemResourceView(APIView):
    """
    API view for getting current system resource usage.
    Returns CPU, RAM, GPU, disk, and network usage statistics.
    """
    
    def get(self, request):
        """Get current system resource usage with performance monitoring."""
        start_time = time.time()
        request_id = f"sysres_{int(start_time * 1000)}"

        try:
            logger.debug(f"[{request_id}] Starting system resource request")

            # Check if we have cached data (cache for 1 second for real-time updates)
            cache_key = 'system_resources_current'
            cached_data = cache.get(cache_key)

            if cached_data:
                response_time = (time.time() - start_time) * 1000
                logger.debug(f"[{request_id}] Returned cached data in {response_time:.2f}ms")
                return Response(cached_data, status=status.HTTP_200_OK)

            # Get fresh system resource data
            monitor_start = time.time()
            try:
                monitor = get_system_monitor()
                resource_data = monitor.get_all_resources()
                monitor_time = (time.time() - monitor_start) * 1000
                logger.info(f"[{request_id}] System monitor SUCCESS - CPU: {resource_data.get('cpu', {}).get('usage_percent', 'N/A')}% - took {monitor_time:.2f}ms")
            except Exception as monitor_error:
                logger.error(f"[{request_id}] Error with system monitor: {str(monitor_error)}")
                # Fallback to basic system info using psutil directly
                fallback_start = time.time()
                resource_data = self._get_basic_system_info()
                fallback_time = (time.time() - fallback_start) * 1000
                logger.info(f"[{request_id}] FALLBACK method used - CPU: {resource_data.get('cpu', {}).get('usage_percent', 'N/A')}% - took {fallback_time:.2f}ms")

            # Add some computed metrics
            alerts_start = time.time()
            all_alerts = self._check_resource_alerts(resource_data)
            # Filter out internal system alerts for user display
            user_alerts = [alert for alert in all_alerts
                          if not (alert.get('type') == 'system' and
                                 ('api' in alert.get('message', '').lower() or
                                  'timeout' in alert.get('message', '').lower() or
                                  'cached' in alert.get('message', '').lower()))]
            resource_data['alerts'] = user_alerts
            alerts_time = (time.time() - alerts_start) * 1000
            logger.debug(f"[{request_id}] Alert checking took {alerts_time:.2f}ms")

            # Cache the data for 1 second for real-time updates
            cache.set(cache_key, resource_data, 1)

            total_time = (time.time() - start_time) * 1000
            logger.debug(f"[{request_id}] Total request completed in {total_time:.2f}ms")

            # Add performance metadata to response in debug mode
            if logger.isEnabledFor(logging.DEBUG):
                resource_data['_debug'] = {
                    'request_id': request_id,
                    'total_time_ms': round(total_time, 2),
                    'cached': False
                }

            return Response(resource_data, status=status.HTTP_200_OK)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            total_time = (time.time() - start_time) * 1000
            logger.error(f"[{request_id}] Error getting system resources after {total_time:.2f}ms: {str(e)}")
            logger.error(f"[{request_id}] Full traceback: {error_details}")
            return Response(
                {"error": "Failed to retrieve system resource data", "details": str(e), "request_id": request_id},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_basic_system_info(self):
        """Get basic system information using psutil directly."""
        import psutil
        import platform
        from datetime import datetime

        # Get CPU info (use short blocking measurement for accuracy)
        cpu_percent = psutil.cpu_percent(interval=0.1)

        cpu_count = psutil.cpu_count()
        cpu_count_logical = psutil.cpu_count(logical=True)

        # Get memory info
        memory = psutil.virtual_memory()

        # Get disk info (use C: on Windows, / on Unix)
        import os
        if os.name == 'nt':  # Windows
            disk_path = 'C:\\\\'
        else:  # Unix/Linux
            disk_path = '/'
        disk_usage = psutil.disk_usage(disk_path)

        # Get network info
        network_io = psutil.net_io_counters()

        return {
            'timestamp': datetime.now().isoformat(),
            'cpu': {
                'usage_percent': round(cpu_percent, 2),
                'cores_physical': cpu_count,
                'cores_logical': cpu_count_logical,
                'per_core_usage': []
            },
            'memory': {
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'used_gb': round(memory.used / (1024**3), 2),
                'usage_percent': round(memory.percent, 2),
                'free_gb': round(memory.free / (1024**3), 2),
                'swap_total_gb': 0,
                'swap_used_gb': 0,
                'swap_usage_percent': 0
            },
            'gpu': [],  # No GPU info in basic mode
            'disk': [{
                'device': disk_path,
                'mountpoint': disk_path,
                'filesystem': 'unknown',
                'total_gb': round(disk_usage.total / (1024**3), 2),
                'used_gb': round(disk_usage.used / (1024**3), 2),
                'free_gb': round(disk_usage.free / (1024**3), 2),
                'usage_percent': round((disk_usage.used / disk_usage.total) * 100, 2)
            }],
            'network': {
                'bytes_sent_total': network_io.bytes_sent,
                'bytes_recv_total': network_io.bytes_recv,
                'bytes_sent_gb': round(network_io.bytes_sent / (1024**3), 2),
                'bytes_recv_gb': round(network_io.bytes_recv / (1024**3), 2),
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv,
                'connections_count': 0,
                'bytes_sent_per_sec': 0,
                'bytes_recv_per_sec': 0
            },
            'system': {
                'platform': platform.platform(),
                'system': platform.system(),
                'processor': platform.processor(),
                'architecture': platform.architecture()[0],
                'hostname': platform.node(),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                'uptime_seconds': int((datetime.now() - datetime.fromtimestamp(psutil.boot_time())).total_seconds()),
                'uptime_formatted': str(datetime.now() - datetime.fromtimestamp(psutil.boot_time())).split('.')[0],
                'python_version': platform.python_version(),
                'machine': platform.machine(),
                'release': platform.release(),
                'version': platform.version()
            },
            'top_processes': []
        }

    def _check_resource_alerts(self, resource_data):
        """Check for resource usage alerts."""
        alerts = []
        
        # CPU alerts
        cpu_usage = resource_data.get('cpu', {}).get('usage_percent', 0)
        if cpu_usage > 90:
            alerts.append({
                'type': 'cpu',
                'level': 'critical',
                'message': f'CPU usage is critically high: {cpu_usage}%'
            })
        elif cpu_usage > 75:
            alerts.append({
                'type': 'cpu',
                'level': 'warning',
                'message': f'CPU usage is high: {cpu_usage}%'
            })
        
        # Memory alerts
        memory_usage = resource_data.get('memory', {}).get('usage_percent', 0)
        if memory_usage > 90:
            alerts.append({
                'type': 'memory',
                'level': 'critical',
                'message': f'Memory usage is critically high: {memory_usage}%'
            })
        elif memory_usage > 80:
            alerts.append({
                'type': 'memory',
                'level': 'warning',
                'message': f'Memory usage is high: {memory_usage}%'
            })
        
        # GPU alerts
        for gpu in resource_data.get('gpu', []):
            gpu_usage = gpu.get('usage_percent', 0)
            gpu_memory = gpu.get('memory_usage_percent', 0)
            gpu_temp = gpu.get('temperature', 0)
            
            if gpu_usage > 95:
                alerts.append({
                    'type': 'gpu',
                    'level': 'critical',
                    'message': f'GPU {gpu["name"]} usage is critically high: {gpu_usage}%'
                })
            
            if gpu_memory > 90:
                alerts.append({
                    'type': 'gpu',
                    'level': 'warning',
                    'message': f'GPU {gpu["name"]} memory usage is high: {gpu_memory}%'
                })
            
            if gpu_temp > 80:
                alerts.append({
                    'type': 'gpu',
                    'level': 'warning',
                    'message': f'GPU {gpu["name"]} temperature is high: {gpu_temp}°C'
                })
        
        # Disk alerts
        for disk in resource_data.get('disk', []):
            disk_usage = disk.get('usage_percent', 0)
            if disk_usage > 90:
                alerts.append({
                    'type': 'disk',
                    'level': 'critical',
                    'message': f'Disk {disk["device"]} usage is critically high: {disk_usage}%'
                })
            elif disk_usage > 80:
                alerts.append({
                    'type': 'disk',
                    'level': 'warning',
                    'message': f'Disk {disk["device"]} usage is high: {disk_usage}%'
                })
        
        return alerts
    



class SystemResourceHistoryView(APIView):
    """
    API view for getting historical system resource usage data.
    Returns time-series data for charting.
    """
    
    def get(self, request):
        """Get historical system resource usage."""
        try:
            # Get query parameters
            timeframe = request.GET.get('timeframe', '1h')  # 1h, 6h, 24h, 7d
            
            # For now, we'll generate sample historical data
            # In a production system, you'd store this data in a database or time-series DB
            historical_data = self._generate_sample_historical_data(timeframe)
            
            return Response(historical_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting historical system resources: {str(e)}")
            return Response(
                {"error": "Failed to retrieve historical system resource data"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _generate_sample_historical_data(self, timeframe):
        """Generate sample historical data for demonstration."""
        import random
        from datetime import datetime, timedelta
        
        # Determine time range and interval
        if timeframe == '1h':
            start_time = datetime.now() - timedelta(hours=1)
            interval = timedelta(minutes=1)
            points = 60
        elif timeframe == '6h':
            start_time = datetime.now() - timedelta(hours=6)
            interval = timedelta(minutes=5)
            points = 72
        elif timeframe == '24h':
            start_time = datetime.now() - timedelta(hours=24)
            interval = timedelta(minutes=15)
            points = 96
        else:  # 7d
            start_time = datetime.now() - timedelta(days=7)
            interval = timedelta(hours=1)
            points = 168
        
        # Generate sample data points
        data_points = []
        current_time = start_time
        
        # Base values that will fluctuate
        base_cpu = 25
        base_memory = 45
        base_network_in = 1024 * 1024  # 1 MB/s
        base_network_out = 512 * 1024  # 512 KB/s
        
        for i in range(points):
            # Add some realistic fluctuation
            cpu_usage = max(0, min(100, base_cpu + random.uniform(-15, 25)))
            memory_usage = max(0, min(100, base_memory + random.uniform(-10, 20)))
            network_in = max(0, base_network_in + random.uniform(-500000, 2000000))
            network_out = max(0, base_network_out + random.uniform(-200000, 1000000))
            
            data_points.append({
                'timestamp': current_time.isoformat(),
                'cpu_usage': round(cpu_usage, 2),
                'memory_usage': round(memory_usage, 2),
                'network_in_bytes': round(network_in, 2),
                'network_out_bytes': round(network_out, 2),
                'disk_usage': round(random.uniform(60, 75), 2)  # Disk usage changes slowly
            })
            
            current_time += interval
        
        return {
            'timeframe': timeframe,
            'data_points': data_points,
            'total_points': len(data_points)
        }

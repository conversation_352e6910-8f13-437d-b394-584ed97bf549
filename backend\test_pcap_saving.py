#!/usr/bin/env python3
"""
Test script to verify PCAP saving behavior (every 10 minutes)
"""

import os
import sys
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'flow_sentinel.settings')
django.setup()

from ids.ids_manager import IDSManager
from ids.models import IDSSettings

def test_pcap_saving():
    """Test the PCAP saving behavior"""
    
    print("=== Testing PCAP Saving (10 minute intervals) ===")
    
    # Start IDS
    ids_manager = IDSManager()
    if ids_manager.is_running:
        print("Stopping existing IDS...")
        ids_manager.stop_ids()
        time.sleep(2)

    print("Starting IDS with 10-minute PCAP saving...")
    settings = IDSSettings.get_settings()
    result = ids_manager.start_ids_with_settings(settings)
    print(f"Start result: {result['status']}")

    if result['status'] == 'success':
        print("IDS started. Monitoring PCAP saving behavior...")
        print("- PCAP files will be saved every 10 minutes")
        print("- Check the logs for PCAP save messages")
        print("- Press Ctrl+C to stop")
        
        try:
            # Monitor for 2 minutes to see the behavior
            start_time = time.time()
            while time.time() - start_time < 120:  # Run for 2 minutes
                time.sleep(10)
                elapsed = time.time() - start_time
                print(f"Running for {elapsed:.0f} seconds...")
                
                # Check accumulated packets
                if hasattr(ids_manager.ids, 'accumulated_packets'):
                    packet_count = len(ids_manager.ids.accumulated_packets)
                    print(f"  - Accumulated packets: {packet_count}")
                    
                    # Check time since last save
                    if hasattr(ids_manager.ids, 'last_pcap_save_time'):
                        time_since_save = time.time() - ids_manager.ids.last_pcap_save_time
                        print(f"  - Time since last PCAP save: {time_since_save:.1f} seconds")
                        
                        # Show when next save will occur
                        next_save_in = ids_manager.ids.pcap_save_interval - time_since_save
                        if next_save_in > 0:
                            print(f"  - Next PCAP save in: {next_save_in:.1f} seconds ({next_save_in/60:.1f} minutes)")
                        else:
                            print(f"  - PCAP save is overdue by {-next_save_in:.1f} seconds")
                
        except KeyboardInterrupt:
            print("\nStopping test...")
        
        print("Stopping IDS...")
        ids_manager.stop_ids()
        print("✅ Test complete!")
        print("\nExpected behavior:")
        print("- PCAP files should be saved every 10 minutes (600 seconds)")
        print("- Packets are accumulated across multiple capture windows")
        print("- A final PCAP file is saved when the IDS stops")
        
    else:
        print(f"Failed to start: {result['message']}")

if __name__ == "__main__":
    test_pcap_saving()


import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Maximize2, Minimize2, Terminal as TerminalIcon, Play, Square, Download, Trash } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export function Terminal() {
  const [expanded, setExpanded] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState<string[]>([
    "Flow Sentinel IDS v1.0",
    "Ready to start monitoring...",
    "Use the play button to connect to the backend and start monitoring."
  ]);
  const [isConnected, setIsConnected] = useState(false);
  const { toast } = useToast();
  
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const wsRef = useRef<WebSocket | null>(null);
  
  // Auto-scroll to bottom when logs update
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [logs]);
  
  // Check IDS status on component mount
  useEffect(() => {
    checkIDSStatus();
    
    // Check status every 5 seconds for more real-time updates
    const interval = setInterval(checkIDSStatus, 5000);
    
    return () => {
      clearInterval(interval);
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const checkIDSStatus = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/ids/status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setIsRunning(data.running);
        
        // Auto-connect to WebSocket if IDS is running
        if (data.running && !isConnected) {
          connectWebSocket();
        }
      }
    } catch (error) {
      console.error("Failed to check IDS status:", error);
    }
  };
  
  const connectWebSocket = () => {
    // Close existing connection if any
    if (wsRef.current) {
      wsRef.current.close();
    }
    
    // WebSocket URL should point to your Django backend
    const ws = new WebSocket('ws://localhost:8000/ws/ids/');
    
    ws.onopen = () => {
      setIsConnected(true);
      setLogs(prev => [...prev, "> WebSocket connection established"]);
      // Removed the toast notification for connection
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'log') {
          setLogs(prev => [...prev, data.message]);
        } else if (data.type === 'status') {
          setIsRunning(data.running);
        } else if (data.type === 'error') {
          toast({
            title: "Error",
            description: data.message,
            variant: "destructive"
          });
        }
      } catch (e) {
        console.error("Failed to parse WebSocket message:", e);
      }
    };
    
    ws.onclose = () => {
      setIsConnected(false);
      setLogs(prev => [...prev, "> WebSocket connection closed"]);
    };
    
    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      // Removed the toast notification for connection error
    };
    
    wsRef.current = ws;
  };
  
  const handleStartStop = async () => {
    if (!isConnected) {
      // Try to connect first
      connectWebSocket();
      return;
    }
    
    try {
      const response = await fetch(`http://localhost:8000/api/ids/${isRunning ? 'stop' : 'start'}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          interface: 'auto', // You could make this configurable
          interval: 2.0 // Capture interval in seconds
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to ${isRunning ? 'stop' : 'start'} IDS: ${response.statusText}`);
      }
      
      const data = await response.json();
      setLogs(prev => [...prev, `> ${isRunning ? 'Stopping' : 'Starting'} Flow Sentinel IDS...`]);
      setIsRunning(!isRunning);
      
      toast({
        title: isRunning ? "IDS Stopping" : "IDS Starting",
        description: data.message
      });
      
    } catch (error) {
      console.error(error);
      toast({
        title: "Operation Failed",
        description: String(error),
        variant: "destructive"
      });
    }
  };
  
  const handleClearLogs = () => {
    setLogs([
      "Flow Sentinel IDS v1.0",
      "Logs cleared...",
      isRunning ? "> IDS is currently running" : "> IDS is currently stopped"
    ]);
  };
  
  const handleDownloadLogs = () => {
    const logText = logs.join('\n');
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ids_logs_${new Date().toISOString().replace(/:/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Logs Downloaded",
      description: "IDS logs have been downloaded to your device."
    });
  };
  
  return (
    <Card className={`${expanded ? 'fixed inset-4 z-50' : ''}`}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center gap-2">
          <TerminalIcon className="h-4 w-4 text-primary" />
          <CardTitle className="text-sm font-medium">IDS Terminal</CardTitle>
        </div>
        <div className="flex items-center gap-1">
          <Button 
            variant="ghost" 
            size="icon" 
            className={`h-7 w-7 ${isConnected ? '' : 'text-yellow-500'}`}
            onClick={handleStartStop}
            title={isConnected ? (isRunning ? "Stop IDS" : "Start IDS") : "Connect to IDS Backend"}
          >
            {isRunning ? <Square className="h-3.5 w-3.5" /> : <Play className="h-3.5 w-3.5" />}
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-7 w-7"
            onClick={handleClearLogs}
            title="Clear terminal"
          >
            <Trash className="h-3.5 w-3.5" />
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-7 w-7"
            onClick={handleDownloadLogs}
            title="Download logs"
          >
            <Download className="h-3.5 w-3.5" />
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-7 w-7"
            onClick={() => setExpanded(!expanded)}
            title={expanded ? "Minimize terminal" : "Maximize terminal"}
          >
            {expanded ? <Minimize2 className="h-3.5 w-3.5" /> : <Maximize2 className="h-3.5 w-3.5" />}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea 
          className={`rounded bg-black/70 text-xs font-mono text-green-400 p-2 ${expanded ? 'h-[calc(100vh-8rem)]' : 'h-[240px]'}`}
          ref={scrollAreaRef}
        >
          {logs.map((log, i) => (
            <div key={i} className="py-0.5">
              {log}
            </div>
          ))}
          {isRunning && (
            <div className="py-0.5 flex items-center">
              <span className="w-2 h-4 bg-green-400 animate-pulse mr-1"></span>
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

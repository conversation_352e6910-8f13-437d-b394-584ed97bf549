
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ids', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='idsalert',
            name='protocol',
            field=models.Char<PERSON>ield(default='TCP', max_length=10),
        ),
        migrations.AddField(
            model_name='idsalert',
            name='description',
            field=models.TextField(default='No description provided'),
        ),
        migrations.AlterModelOptions(
            name='idsalert',
            options={'ordering': ['-timestamp']},
        ),
        migrations.AlterModelOptions(
            name='idslog',
            options={'ordering': ['-timestamp']},
        ),
        migrations.AlterField(
            model_name='idsalert',
            name='severity',
            field=models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], max_length=10),
        ),
        migrations.AlterField(
            model_name='idslog',
            name='level',
            field=models.Char<PERSON>ield(choices=[('debug', 'Debug'), ('info', 'Info'), ('warning', 'Warning'), ('error', 'Error')], max_length=10),
        ),
    ]


from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json

def send_log_message(level, message):
    """Send a log message to all connected WebSocket clients."""
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        'ids_logs',
        {
            'type': 'log_message',
            'level': level,
            'message': message
        }
    )

def send_alert_message(alert_data):
    """Send an alert message to all connected WebSocket clients."""
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        'ids_alerts',
        {
            'type': 'alert_message',
            **alert_data
        }
    )

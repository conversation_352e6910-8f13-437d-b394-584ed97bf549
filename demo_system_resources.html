<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flow Sentinel - System Resources Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .animate-pulse-slow {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .status-indicator {
            position: relative;
        }
        
        .status-indicator::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
        }
        
        .status-safe::after {
            background-color: rgb(34, 197, 94);
            opacity: 0.75;
        }
        
        @keyframes ping {
            75%, 100% {
                transform: scale(2);
                opacity: 0;
            }
        }
        
        .resource-bar {
            transition: width 0.8s ease-in-out;
        }
        
        .chart-container {
            height: 264px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .chart-grid {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .chart-canvas {
            position: relative;
            z-index: 2;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="shield" class="w-5 h-5 text-white"></i>
                    </div>
                    <h1 class="text-xl font-semibold text-gray-900">Flow Sentinel - System Resources</h1>
                </div>
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2 text-sm text-gray-600">
                        <div class="w-2 h-2 bg-green-500 rounded-full status-indicator status-safe"></div>
                        <span>System Monitoring Active</span>
                    </div>
                    <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg">
                        <i data-lucide="settings" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="p-6 max-w-7xl mx-auto">
        <!-- System Status Overview -->
        <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                    <div>
                        <h3 class="text-lg font-semibold text-green-800">System Status: OPTIMAL</h3>
                        <p class="text-green-700">All system resources operating within normal parameters</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="px-3 py-1 bg-green-600 text-white text-sm font-medium rounded-full">
                        HEALTHY
                    </div>
                    <div class="text-sm text-green-600 mt-1">
                        Uptime: <span id="system-uptime">72h 15m</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resource Usage Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white p-6 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">CPU Usage</p>
                        <p id="cpu-usage" class="text-2xl font-bold text-gray-900">23.4%</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="cpu" class="w-6 h-6 text-blue-600"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="cpu-bar" class="bg-blue-600 h-2 rounded-full resource-bar" style="width: 23.4%"></div>
                    </div>
                    <div class="mt-2 text-sm text-gray-600">4 cores active</div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Memory Usage</p>
                        <p id="memory-usage" class="text-2xl font-bold text-gray-900">67.8%</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="memory-stick" class="w-6 h-6 text-green-600"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="memory-bar" class="bg-green-600 h-2 rounded-full resource-bar" style="width: 67.8%"></div>
                    </div>
                    <div class="mt-2 text-sm text-gray-600">5.4GB / 8GB</div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Disk Usage</p>
                        <p id="disk-usage" class="text-2xl font-bold text-gray-900">45.2%</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="hard-drive" class="w-6 h-6 text-purple-600"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="disk-bar" class="bg-purple-600 h-2 rounded-full resource-bar" style="width: 45.2%"></div>
                    </div>
                    <div class="mt-2 text-sm text-gray-600">226GB / 500GB</div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Network I/O</p>
                        <p id="network-io" class="text-2xl font-bold text-gray-900">847 Mbps</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="wifi" class="w-6 h-6 text-orange-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-green-600 font-medium">↑ 423 Mbps</span>
                    <span class="text-gray-600 mx-2">|</span>
                    <span class="text-blue-600 font-medium">↓ 424 Mbps</span>
                </div>
            </div>
        </div>

        <!-- Performance Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- CPU Performance Chart -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">CPU Performance</h2>
                    <p class="text-sm text-gray-600">Real-time CPU usage monitoring</p>
                </div>
                <div class="p-0">
                    <div class="chart-container">
                        <!-- Grid Background -->
                        <svg class="chart-grid">
                            <defs>
                                <pattern id="cpuGrid" width="40" height="32" patternUnits="userSpaceOnUse">
                                    <path d="M 40 0 L 0 0 0 32" fill="none" stroke="#e2e8f0" stroke-width="1"/>
                                </pattern>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#cpuGrid)" />
                        </svg>
                        <canvas id="cpuChart" class="chart-canvas"></canvas>
                    </div>
                    <!-- Bottom info -->
                    <div class="px-4 py-2 text-xs text-gray-600 bg-gray-100 flex justify-between border-t">
                        <span>60 seconds</span>
                        <span class="text-right">0</span>
                    </div>
                </div>
            </div>

            <!-- Memory Performance Chart -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Memory Usage</h2>
                    <p class="text-sm text-gray-600">RAM utilization over time</p>
                </div>
                <div class="p-0">
                    <div class="chart-container">
                        <!-- Grid Background -->
                        <svg class="chart-grid">
                            <defs>
                                <pattern id="memoryGrid" width="40" height="32" patternUnits="userSpaceOnUse">
                                    <path d="M 40 0 L 0 0 0 32" fill="none" stroke="#e2e8f0" stroke-width="1"/>
                                </pattern>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#memoryGrid)" />
                        </svg>
                        <canvas id="memoryChart" class="chart-canvas"></canvas>
                    </div>
                    <!-- Bottom info -->
                    <div class="px-4 py-2 text-xs text-gray-600 bg-gray-100 flex justify-between border-t">
                        <span>60 seconds</span>
                        <span class="text-right">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Process Information -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Top Processes -->
            <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Top Processes</h2>
                    <p class="text-sm text-gray-600">Processes consuming the most resources</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="shield" class="w-4 h-4 text-blue-600"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">flow_sentinel_ids</div>
                                    <div class="text-sm text-gray-600">PID: 1234</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">12.3% CPU</div>
                                <div class="text-sm text-gray-600">1.2GB RAM</div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="database" class="w-4 h-4 text-green-600"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">postgresql</div>
                                    <div class="text-sm text-gray-600">PID: 5678</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">8.7% CPU</div>
                                <div class="text-sm text-gray-600">856MB RAM</div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="globe" class="w-4 h-4 text-purple-600"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">nginx</div>
                                    <div class="text-sm text-gray-600">PID: 9012</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">3.2% CPU</div>
                                <div class="text-sm text-gray-600">245MB RAM</div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i data-lucide="terminal" class="w-4 h-4 text-orange-600"></i>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">python3</div>
                                    <div class="text-sm text-gray-600">PID: 3456</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">2.8% CPU</div>
                                <div class="text-sm text-gray-600">512MB RAM</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">System Info</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <div class="text-sm font-medium text-gray-700">Operating System</div>
                            <div class="text-sm text-gray-600">Kali Linux 2023.4</div>
                        </div>

                        <div>
                            <div class="text-sm font-medium text-gray-700">Kernel Version</div>
                            <div class="text-sm text-gray-600">6.5.0-kali3-amd64</div>
                        </div>

                        <div>
                            <div class="text-sm font-medium text-gray-700">Architecture</div>
                            <div class="text-sm text-gray-600">x86_64</div>
                        </div>

                        <div>
                            <div class="text-sm font-medium text-gray-700">Total Memory</div>
                            <div class="text-sm text-gray-600">8.0 GB</div>
                        </div>

                        <div>
                            <div class="text-sm font-medium text-gray-700">CPU Cores</div>
                            <div class="text-sm text-gray-600">4 cores / 8 threads</div>
                        </div>

                        <div>
                            <div class="text-sm font-medium text-gray-700">Network Interface</div>
                            <div class="text-sm text-gray-600">wlan0 (Active)</div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                        <div class="flex items-center gap-2 mb-2">
                            <i data-lucide="info" class="w-4 h-4 text-blue-600"></i>
                            <span class="text-sm font-medium text-blue-800">System Status</span>
                        </div>
                        <p class="text-sm text-blue-700">
                            All system components are functioning normally.
                            IDS monitoring is active and operational.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // System resource tracking
        let resources = {
            cpu: 23.4,
            memory: 67.8,
            disk: 45.2,
            networkIn: 423,
            networkOut: 424
        };

        // Chart data storage
        let cpuData = [];
        let memoryData = [];
        let timeLabels = [];
        let cpuChart, memoryChart;

        // Initialize charts
        function initializeCharts() {
            // Generate initial data points (60 seconds)
            const now = Date.now();
            for (let i = 59; i >= 0; i--) {
                timeLabels.push('');
                cpuData.push(Math.random() * 30 + 15); // Random data between 15-45%
                memoryData.push(Math.random() * 20 + 60); // Random data between 60-80%
            }

            // CPU Chart
            const cpuCtx = document.getElementById('cpuChart').getContext('2d');
            cpuChart = new Chart(cpuCtx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [{
                        data: cpuData,
                        borderColor: '#00d4aa',
                        backgroundColor: function(context) {
                            const chart = context.chart;
                            const {ctx, chartArea} = chart;
                            if (!chartArea) return null;

                            const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom);
                            gradient.addColorStop(0, 'rgba(0, 212, 170, 0.9)');
                            gradient.addColorStop(0.5, 'rgba(0, 212, 170, 0.6)');
                            gradient.addColorStop(1, 'rgba(0, 212, 170, 0.2)');
                            return gradient;
                        },
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: '#ffffff',
                            titleColor: '#1f2937',
                            bodyColor: '#1f2937',
                            borderColor: '#e5e7eb',
                            borderWidth: 1,
                            displayColors: false,
                            callbacks: {
                                label: function(context) {
                                    return Math.round(context.parsed.y) + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        x: { display: false },
                        y: {
                            display: false,
                            min: 0,
                            max: 100
                        }
                    },
                    elements: {
                        line: { borderJoinStyle: 'round' }
                    },
                    animation: false
                }
            });

            // Memory Chart
            const memoryCtx = document.getElementById('memoryChart').getContext('2d');
            memoryChart = new Chart(memoryCtx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [{
                        data: memoryData,
                        borderColor: '#0078d4',
                        backgroundColor: function(context) {
                            const chart = context.chart;
                            const {ctx, chartArea} = chart;
                            if (!chartArea) return null;

                            const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom);
                            gradient.addColorStop(0, 'rgba(0, 120, 212, 0.9)');
                            gradient.addColorStop(0.5, 'rgba(0, 120, 212, 0.6)');
                            gradient.addColorStop(1, 'rgba(0, 120, 212, 0.2)');
                            return gradient;
                        },
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: '#ffffff',
                            titleColor: '#1f2937',
                            bodyColor: '#1f2937',
                            borderColor: '#e5e7eb',
                            borderWidth: 1,
                            displayColors: false,
                            callbacks: {
                                label: function(context) {
                                    return Math.round(context.parsed.y) + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        x: { display: false },
                        y: {
                            display: false,
                            min: 0,
                            max: 100
                        }
                    },
                    elements: {
                        line: { borderJoinStyle: 'round' }
                    },
                    animation: false
                }
            });
        }

        function updateCharts() {
            // Add new data point
            cpuData.push(resources.cpu);
            memoryData.push(resources.memory);
            timeLabels.push('');

            // Remove old data (keep last 60 points)
            if (cpuData.length > 60) {
                cpuData.shift();
                memoryData.shift();
                timeLabels.shift();
            }

            // Update charts
            cpuChart.update('none');
            memoryChart.update('none');
        }

        function updateResourceStats() {
            // Simulate fluctuating resource usage
            resources.cpu += (Math.random() - 0.5) * 5;
            resources.cpu = Math.max(5, Math.min(95, resources.cpu));

            resources.memory += (Math.random() - 0.5) * 2;
            resources.memory = Math.max(40, Math.min(85, resources.memory));

            resources.disk += (Math.random() - 0.5) * 0.5;
            resources.disk = Math.max(40, Math.min(50, resources.disk));

            resources.networkIn += (Math.random() - 0.5) * 100;
            resources.networkIn = Math.max(200, Math.min(800, resources.networkIn));

            resources.networkOut += (Math.random() - 0.5) * 100;
            resources.networkOut = Math.max(200, Math.min(800, resources.networkOut));

            // Update display
            document.getElementById('cpu-usage').textContent = resources.cpu.toFixed(1) + '%';
            document.getElementById('cpu-bar').style.width = resources.cpu + '%';

            document.getElementById('memory-usage').textContent = resources.memory.toFixed(1) + '%';
            document.getElementById('memory-bar').style.width = resources.memory + '%';

            document.getElementById('disk-usage').textContent = resources.disk.toFixed(1) + '%';
            document.getElementById('disk-bar').style.width = resources.disk + '%';

            const totalBandwidth = resources.networkIn + resources.networkOut;
            document.getElementById('network-io').textContent = totalBandwidth.toFixed(0) + ' Mbps';

            // Update charts
            updateCharts();
        }

        function updateUptime() {
            // Simulate increasing uptime
            const uptimeElement = document.getElementById('system-uptime');
            const currentUptime = uptimeElement.textContent;
            const [hours, minutes] = currentUptime.split('h ')[0].split('h');
            const mins = parseInt(currentUptime.split('h ')[1].replace('m', ''));

            let newMins = mins + 1;
            let newHours = parseInt(hours);

            if (newMins >= 60) {
                newMins = 0;
                newHours++;
            }

            uptimeElement.textContent = `${newHours}h ${newMins}m`;
        }

        // Start the simulation
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize charts
            initializeCharts();

            // Update resource stats every 2 seconds
            setInterval(updateResourceStats, 2000);

            // Update uptime every minute
            setInterval(updateUptime, 60000);

            // Initial update
            updateResourceStats();
        });
    </script>
</body>
</html>

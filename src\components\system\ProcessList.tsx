import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Zap, Cpu, MemoryStick } from "lucide-react";
import { getUsageColor } from "@/services/systemResourceService";

interface Process {
  pid: number;
  name: string;
  username?: string;
  status?: string;
  cmdline?: string;
  cpu_percent: number;
  memory_percent: number;
  memory_mb: number;
}

interface ProcessListProps {
  processes: Process[];
}

export function ProcessList({ processes }: ProcessListProps) {
  // Sort processes by CPU usage (highest first)
  const sortedProcesses = [...processes].sort((a, b) => b.cpu_percent - a.cpu_percent);

  // Check if we have valid process data
  const hasProcesses = processes && processes.length > 0;
  const hasValidData = hasProcesses && processes.some(p => p.cpu_percent > 0 || p.memory_percent > 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Top Processes by Resource Usage</h3>
        </div>
        <Badge variant="outline" className="text-xs">
          {hasProcesses ? `${processes.length} processes` : 'No data'}
        </Badge>
      </div>

      {!hasValidData && (
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-muted-foreground">
              <Zap className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No process data available</p>
              <p className="text-sm">Process monitoring may be starting up...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Top CPU Processes */}
      {hasValidData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5" />
              Top CPU Consumers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {sortedProcesses.slice(0, 8).map((process, index) => (
                <div key={`cpu-${process.pid}`} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <span className="font-medium">{process.name}</span>
                        <span className="text-sm text-muted-foreground ml-2">PID: {process.pid}</span>
                        {process.username && (
                          <span className="text-xs text-muted-foreground ml-2">({process.username})</span>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Badge variant="outline" className={getUsageColor(process.cpu_percent)}>
                          {process.cpu_percent}%
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {process.memory_mb.toFixed(0)} MB
                        </Badge>
                      </div>
                    </div>
                    <Progress value={Math.min(process.cpu_percent, 100)} className="h-2" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Top Memory Processes */}
      {hasValidData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MemoryStick className="h-5 w-5" />
              Top Memory Consumers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...processes]
                .sort((a, b) => b.memory_percent - a.memory_percent)
                .slice(0, 8)
                .map((process, index) => (
                  <div key={`memory-${process.pid}`} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <span className="font-medium">{process.name}</span>
                          <span className="text-sm text-muted-foreground ml-2">PID: {process.pid}</span>
                          {process.username && (
                            <span className="text-xs text-muted-foreground ml-2">({process.username})</span>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Badge variant="outline" className={getUsageColor(process.memory_percent)}>
                            {process.memory_percent.toFixed(1)}%
                          </Badge>
                          <Badge variant="secondary">
                            {process.memory_mb.toFixed(0)} MB
                          </Badge>
                        </div>
                      </div>
                      <Progress value={Math.min(process.memory_percent, 100)} className="h-2" />
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Process Table */}
      {hasValidData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>All Processes</span>
              <Badge variant="outline" className="text-xs">
                Showing top {sortedProcesses.length} processes
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Process Name</TableHead>
                    <TableHead>PID</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">CPU %</TableHead>
                    <TableHead className="text-right">Memory %</TableHead>
                    <TableHead className="text-right">Memory (MB)</TableHead>
                    <TableHead className="max-w-xs">Command</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedProcesses.length > 0 ? (
                    sortedProcesses.map((process) => (
                      <TableRow key={process.pid}>
                        <TableCell className="font-medium">{process.name}</TableCell>
                        <TableCell>{process.pid}</TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {process.username || 'Unknown'}
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary" className="text-xs">
                            {process.status || 'Unknown'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Badge
                            variant="outline"
                            className={`${getUsageColor(process.cpu_percent)} text-xs`}
                          >
                            {process.cpu_percent}%
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Badge
                            variant="outline"
                            className={`${getUsageColor(process.memory_percent)} text-xs`}
                          >
                            {process.memory_percent.toFixed(1)}%
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          {process.memory_mb.toFixed(0)} MB
                        </TableCell>
                        <TableCell className="max-w-xs truncate text-xs text-muted-foreground" title={process.cmdline}>
                          {process.cmdline || 'N/A'}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center text-muted-foreground py-8">
                        No process data available
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

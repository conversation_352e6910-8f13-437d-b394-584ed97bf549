
import {
  Wifi
} from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useState, useEffect } from "react";

interface StatsData {
  networkTraffic: number;
  changePercent: number;
}

export function DashboardStats() {
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [stats, setStats] = useState<StatsData>({
    networkTraffic: 0,
    changePercent: 0
  });

  useEffect(() => {
    // Fetch stats immediately and then every 5 seconds for more real-time updates
    fetchStats(true);
    const interval = setInterval(() => fetchStats(false), 5000);

    // Listen for dataset cleanup events
    const handleDatasetClean = () => {
      console.log('Dataset cleaned, refreshing stats...');
      fetchStats(true);
    };

    window.addEventListener('dataset-cleaned', handleDatasetClean);

    return () => {
      clearInterval(interval);
      window.removeEventListener('dataset-cleaned', handleDatasetClean);
    };
  }, []);

  const fetchStats = async (isInitial: boolean = false) => {
    try {
      if (isInitial) {
        setIsInitialLoad(true);
      } else {
        setIsUpdating(true);
      }

      // Always fetch fresh data - no caching
      const response = await fetch(`http://localhost:8000/api/ids/status?t=${Date.now()}`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();

        if (data.stats) {
          setStats({
            networkTraffic: data.stats.total_packets || 0,
            changePercent: data.stats.change_percent || 0
          });
        } else {
          // If no stats, reset to zero
          setStats({
            networkTraffic: 0,
            changePercent: 0
          });
        }
      } else {
        // If fetch fails, reset stats to zero
        setStats({
          networkTraffic: 0,
          changePercent: 0
        });
      }
    } catch (error) {
      console.error("Error fetching stats:", error);
      // If fetch fails, reset stats to zero
      setStats({
        networkTraffic: 0,
        changePercent: 0
      });
    } finally {
      if (isInitial) {
        setIsInitialLoad(false);
      } else {
        setIsUpdating(false);
      }
    }
  };

  // Format traffic number to a human-readable format
  const formatTraffic = (packets: number): string => {
    if (packets > 1000000) {
      return `${(packets / 1000000).toFixed(1)} M/h`;
    } else if (packets > 1000) {
      return `${(packets / 1000).toFixed(1)} K/h`;
    }
    return `${packets}/h`;
  };

  return (
    <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Network Traffic</CardTitle>
          <Wifi className="h-4 w-4 text-primary" />
        </CardHeader>
        <CardContent>
          {isInitialLoad ? (
            <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
          ) : (
            <>
              <div className="text-2xl font-bold">{formatTraffic(stats.networkTraffic)}</div>
              <p className="text-xs text-muted-foreground">
                Avg. {Math.floor(stats.networkTraffic / 60)} packets/min
              </p>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

#!/usr/bin/env python3
"""
Offline Flow Evaluator Script

This script loads network flows from a CSV file and evaluates them using the trained model.
It replicates the exact model evaluation methods used by the main IDS system.

Usage:
    python flow_evaluator.py --input flows.csv --output results.csv
"""

import argparse
import sys
import os
import logging
from typing import Dict, Any, List
import pandas as pd
import numpy as np
import joblib

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineFlowEvaluator:
    """
    Offline flow evaluator that loads flows from CSV and evaluates them using the trained model.
    Based on the RealTimeIDS prediction logic.
    """
    
    def __init__(self, model_path="DecisionTree_model.pkl", 
                 label_array_path="label_array.npy",
                 feature_columns_path="feature_columns.txt",
                 scaler_path="scaler.pkl"):
        """
        Initialize the flow evaluator.
        
        Args:
            model_path: Path to the trained model file
            label_array_path: Path to the label array file
            feature_columns_path: Path to the feature columns file
            scaler_path: Path to the scaler file
        """
        self.model_path = model_path
        self.label_array_path = label_array_path
        self.feature_columns_path = feature_columns_path
        self.scaler_path = scaler_path
        
        # Load model components
        self.model = None
        self.label_array = None
        self.feature_columns = None
        self.scaler = None
        
        self._load_model_components()

    def _load_model_components(self):
        """Load the model, label array, feature columns, and scaler."""
        try:
            # Load the Decision Tree model
            self.model = joblib.load(self.model_path)
            logger.info(f"Loaded Decision Tree model from {self.model_path}")
        except Exception as e:
            raise RuntimeError(f"Error loading model: {str(e)}")
        
        try:
            # Load label array
            self.label_array = np.load(self.label_array_path, allow_pickle=True)
            logger.info(f"Loaded label array with {len(self.label_array)} classes")
        except Exception as e:
            raise RuntimeError(f"Error loading label array: {str(e)}")
        
        try:
            # Load feature columns
            with open(self.feature_columns_path, 'r') as f:
                self.feature_columns = [line.strip() for line in f.readlines()]
            logger.info(f"Loaded {len(self.feature_columns)} feature columns")
        except Exception as e:
            raise RuntimeError(f"Error loading feature columns: {str(e)}")
        
        try:
            # Load scaler (optional)
            if os.path.exists(self.scaler_path):
                self.scaler = joblib.load(self.scaler_path)
                logger.info(f"Loaded scaler from {self.scaler_path}")
            else:
                logger.warning(f"Scaler not found at {self.scaler_path}. Features will not be scaled!")
        except Exception as e:
            logger.error(f"Error loading scaler: {str(e)}")
            logger.warning("Will proceed without scaling, but model accuracy may be affected.")

    def load_flows_from_csv(self, csv_file):
        """
        Load flows from a CSV file.
        
        Args:
            csv_file: Path to the CSV file containing flows
            
        Returns:
            DataFrame with loaded flows
        """
        try:
            flows_df = pd.read_csv(csv_file)
            logger.info(f"Loaded {len(flows_df)} flows from {csv_file}")
            
            # Display basic information about the loaded data
            logger.info(f"CSV columns: {list(flows_df.columns)}")
            
            return flows_df
            
        except Exception as e:
            raise RuntimeError(f"Error loading CSV file: {str(e)}")

    def preprocess_flows(self, flows_df):
        """
        Preprocess flows for model prediction.
        This replicates the exact preprocessing from the main IDS system.
        
        Args:
            flows_df: DataFrame with raw flow data
            
        Returns:
            DataFrame ready for model prediction
        """
        # Create a copy for processing
        processed_df = flows_df.copy()
        
        # Ensure all required feature columns exist with default values
        for col in self.feature_columns:
            if col not in processed_df.columns:
                processed_df[col] = 0
                logger.warning(f"Missing feature column '{col}', filled with zeros")
        
        # Create feature matrix in the exact order expected by the model
        feature_matrix = np.zeros((len(processed_df), len(self.feature_columns)))
        for i, col in enumerate(self.feature_columns):
            feature_matrix[:, i] = processed_df[col].values
        
        # Apply scaler if available
        if self.scaler:
            try:
                # Apply scaling to the feature matrix
                scaled_features = self.scaler.transform(feature_matrix)
                
                # Create new dataframe with scaled features
                model_df = pd.DataFrame()
                
                # Add network identifiers (not scaled)
                for col in ['Src IP', 'Dst IP', 'Src Port', 'Dst Port', 'Protocol']:
                    if col in processed_df.columns:
                        model_df[col] = processed_df[col]
                
                # Add scaled features in the correct order
                for i, col in enumerate(self.feature_columns):
                    model_df[col] = scaled_features[:, i]
                    
                logger.info("Applied feature scaling successfully")
                
            except Exception as e:
                logger.error(f"Error applying scaler: {str(e)}")
                logger.warning("Using unscaled features may affect model prediction accuracy.")
                
                # Fall back to unscaled features
                model_df = processed_df.copy()
        else:
            # Use unscaled features
            model_df = processed_df.copy()
            logger.info("No scaler available, using unscaled features")
        
        return model_df

    def predict_flows(self, flows_df):
        """
        Predict threats in flows using the loaded Decision Tree model.
        This replicates the exact prediction logic from the main IDS system.
        
        Args:
            flows_df: DataFrame with flows to evaluate
            
        Returns:
            DataFrame with prediction results
        """
        if flows_df is None or len(flows_df) == 0:
            logger.warning("No flows to predict")
            return None
        
        # Preprocess flows
        processed_df = self.preprocess_flows(flows_df)
        
        # Extract features for prediction (only the model features)
        X = processed_df[self.feature_columns].values
        
        logger.info(f"Predicting on {len(X)} flows with {X.shape[1]} features")
        
        try:
            # Make predictions
            y_pred_idx = self.model.predict(X)
            
            # Convert indices to labels
            y_pred_labels = [self.label_array[int(idx)] for idx in y_pred_idx]
            
            # Add binary threat classification
            threats = ['Malicious' if label != 'Benign' else 'Benign' for label in y_pred_labels]
            
            # Create results dataframe
            results_df = processed_df.copy()
            results_df['Prediction'] = y_pred_labels
            results_df['Threat'] = threats
            
            # Add evaluation timestamp
            results_df['Evaluation_Timestamp'] = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Calculate prediction statistics
            malicious_count = sum(1 for t in threats if t == 'Malicious')
            benign_count = len(threats) - malicious_count
            
            logger.info(f"Prediction completed: {malicious_count} malicious, {benign_count} benign flows")
            
            return results_df
            
        except Exception as e:
            logger.error(f"Error during prediction: {str(e)}")
            raise

    def save_results_to_csv(self, results_df, output_file):
        """
        Save prediction results to a CSV file.
        
        Args:
            results_df: DataFrame with prediction results
            output_file: Path to the output CSV file
        """
        try:
            results_df.to_csv(output_file, index=False)
            logger.info(f"Saved {len(results_df)} results to {output_file}")
            
            # Print summary statistics
            threat_counts = results_df['Threat'].value_counts()
            prediction_counts = results_df['Prediction'].value_counts()
            
            print(f"\n=== Flow Evaluation Summary ===")
            print(f"Total flows evaluated: {len(results_df)}")
            print(f"Threat classification: {threat_counts.to_dict()}")
            print(f"Detailed predictions: {prediction_counts.to_dict()}")
            print(f"Output file: {output_file}")
            
            # Show malicious flows if any
            malicious_flows = results_df[results_df['Threat'] == 'Malicious']
            if len(malicious_flows) > 0:
                print(f"\n=== Malicious Flows Detected ===")
                print(f"Count: {len(malicious_flows)}")
                print("Sample malicious flows:")
                display_cols = ['Src IP', 'Dst IP', 'Src Port', 'Dst Port', 'Prediction', 'Threat']
                available_cols = [col for col in display_cols if col in malicious_flows.columns]
                print(malicious_flows[available_cols].head(10).to_string(index=False))
            
        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")
            raise

    def evaluate_flows_from_csv(self, input_csv, output_csv):
        """
        Complete evaluation pipeline: load flows from CSV, predict, and save results.
        
        Args:
            input_csv: Path to input CSV file with flows
            output_csv: Path to output CSV file for results
        """
        # Load flows
        flows_df = self.load_flows_from_csv(input_csv)
        
        # Predict threats
        results_df = self.predict_flows(flows_df)
        
        if results_df is not None:
            # Save results
            self.save_results_to_csv(results_df, output_csv)
        else:
            logger.error("No results to save")


def main():
    """Main function for the offline flow evaluator."""
    parser = argparse.ArgumentParser(description='Offline Network Flow Evaluator')
    
    parser.add_argument('--input', '-i',
                       required=True,
                       help='Input CSV file with flows to evaluate')
    
    parser.add_argument('--output', '-o',
                       default='evaluation_results.csv',
                       help='Output CSV file for results (default: evaluation_results.csv)')
    
    parser.add_argument('--model', '-m',
                       default='DecisionTree_model.pkl',
                       help='Path to model file (default: DecisionTree_model.pkl)')
    
    parser.add_argument('--labels', '-l',
                       default='label_array.npy',
                       help='Path to label array file (default: label_array.npy)')
    
    parser.add_argument('--features', '-f',
                       default='feature_columns.txt',
                       help='Path to feature columns file (default: feature_columns.txt)')
    
    parser.add_argument('--scaler', '-s',
                       default='scaler.pkl',
                       help='Path to scaler file (default: scaler.pkl)')
    
    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Check if input file exists
    if not os.path.exists(args.input):
        logger.error(f"Input file not found: {args.input}")
        return 1
    
    try:
        # Create flow evaluator
        evaluator = OfflineFlowEvaluator(
            model_path=args.model,
            label_array_path=args.labels,
            feature_columns_path=args.features,
            scaler_path=args.scaler
        )
        
        # Evaluate flows
        evaluator.evaluate_flows_from_csv(args.input, args.output)
        
        return 0
        
    except Exception as e:
        logger.error(f"Error during flow evaluation: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

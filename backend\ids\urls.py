
from django.urls import path
from .views.core_views import (
    IDSStartView, IDSStopView, IDSStatusView
)
from .views.data_views import (
    IDSAlertsView, IDSLogsView, IDSDataCleanView, IDSTrafficView, AlertsExportView
)
from .views.analytics_views import (
    IDSAnalyticsView, AnalyticsExportView
)
from .views.settings_views import (
    IDSSettingsView
)
from .views.system_views import (
    SystemResourceView, SystemResourceHistoryView
)

urlpatterns = [
    path('start', IDSStartView.as_view(), name='ids-start'),
    path('stop', IDSStopView.as_view(), name='ids-stop'),
    path('status', IDSStatusView.as_view(), name='ids-status'),

    # Export endpoints must come before the main endpoints to avoid conflicts
    path('alerts/export', AlertsExportView.as_view(), name='alerts-export'),
    path('analytics/export', AnalyticsExportView.as_view(), name='analytics-export'),
    path('alerts', IDSAlertsView.as_view(), name='ids-alerts'),
    path('logs', IDSLogsView.as_view(), name='ids-logs'),
    path('traffic', IDSTrafficView.as_view(), name='ids-traffic'),
    path('settings', IDSSettingsView.as_view(), name='ids-settings'),
    path('analytics', IDSAnalyticsView.as_view(), name='ids-analytics'),
    path('clean', IDSDataCleanView.as_view(), name='ids-clean'),
    # System resource monitoring endpoints
    path('system/resources', SystemResourceView.as_view(), name='system-resources'),
    path('system/history', SystemResourceHistoryView.as_view(), name='system-history'),
]


import json
from channels.generic.websocket import WebsocketConsumer
from asgiref.sync import async_to_sync
from django.utils import timezone


class IDSConsumer(WebsocketConsumer):
    def connect(self):
        """Handle WebSocket connection."""
        # Add the client to the ids_logs and ids_alerts groups
        async_to_sync(self.channel_layer.group_add)("ids_logs", self.channel_name)
        async_to_sync(self.channel_layer.group_add)("ids_alerts", self.channel_name)

        # Accept the connection
        self.accept()

        # Send a welcome message
        self.send(
            text_data=json.dumps(
                {
                    "type": "log",
                    "timestamp": timezone.now().isoformat(),
                    "message": "Connected to IDS WebSocket server",
                }
            )
        )

    def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        # Remove the client from all groups
        async_to_sync(self.channel_layer.group_discard)("ids_logs", self.channel_name)
        async_to_sync(self.channel_layer.group_discard)("ids_alerts", self.channel_name)

    def receive(self, text_data):
        """Handle messages received from the client."""
        try:
            data = json.loads(text_data)
            message_type = data.get("type", "")

            # Handle different message types
            if message_type == "ping":
                try:
                    self.send(
                        text_data=json.dumps(
                            {"type": "pong", "timestamp": timezone.now().isoformat()}
                        )
                    )
                except Exception:
                    # Connection closed, ignore
                    pass
        except Exception as e:
            # Send error back to client
            try:
                self.send(
                    text_data=json.dumps(
                        {"type": "error", "message": f"Error processing message: {str(e)}"}
                    )
                )
            except Exception:
                # Connection closed, ignore
                pass

    def send_log(self, event):
        try:
            self.send(
                text_data=json.dumps(
                    {
                        "type": "log",
                        "level": event.get("level", "info"),
                        "timestamp": timezone.now().isoformat(),
                        "message": event.get("message", ""),
                    }
                )
            )
        except Exception as e:
            # Connection might be closed, ignore the error
            print(f"WebSocket send_log error (connection likely closed): {e}")
            pass

    def send_alert(self, event):
        # Directly send the alert data in the format expected by the frontend
        try:
            # Format source and target from IP:port format
            source = f"{event.get('src_ip', '')}:{event.get('src_port', '')}"
            target = f"{event.get('dst_ip', '')}:{event.get('dst_port', '')}"

            self.send(
                text_data=json.dumps(
                    {
                        "type": "alert",
                        "id": event.get("id"),
                        "timestamp": event.get("timestamp"),
                        "attack_type": event.get("attack_type"),
                        "src_ip": event.get("src_ip"),
                        "dst_ip": event.get("dst_ip"),
                        "source": source,
                        "target": target,
                    }
                )
            )
        except Exception as e:
            # Connection might be closed, ignore the error
            print(f"WebSocket send_alert error (connection likely closed): {e}")
            pass
        
    def send_status(self, event):
        """Handle status update messages from IDS manager"""
        try:
            # Forward the message to clients
            message_data = event.get("message", {})
            self.send(text_data=json.dumps(message_data))
        except Exception as e:
            # Connection might be closed, ignore the error
            print(f"WebSocket send error (connection likely closed): {e}")
            pass

    def send_attack_session_status(self, event):
        """Handle attack session status updates"""
        try:
            self.send(
                text_data=json.dumps(
                    {
                        "type": "attack_session_status",
                        "isActive": event.get("isActive", False),
                        "attackTypes": event.get("attackTypes", []),
                        "startTime": event.get("startTime")
                    }
                )
            )
        except Exception as e:
            # Connection might be closed, ignore the error
            print(f"WebSocket send_attack_session_status error (connection likely closed): {e}")
            pass

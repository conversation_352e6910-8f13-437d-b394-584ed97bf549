# Offline IDS Scripts

This folder contains offline versions of the IDS system that can extract network flows and evaluate them independently of the main web application.

## Contents

- `flow_extractor.py` - Captures network packets and extracts flows, saves to CSV
- `flow_evaluator.py` - Loads flows from CSV and evaluates them using the trained model
- `DecisionTree_model.pkl` - Trained Decision Tree model
- `label_array.npy` - Array of class labels for predictions
- `feature_columns.txt` - List of feature columns used by the model
- `scaler.pkl` - Feature scaler for preprocessing
- `README.md` - This documentation file

## Requirements

Make sure you have the following Python packages installed:

```bash
pip install scapy pandas numpy scikit-learn joblib
```

## Usage

### 1. Flow Extraction

The `flow_extractor.py` script captures network packets and extracts flows using the same methods as the main IDS system.

**Basic usage:**
```bash
sudo python flow_extractor.py --interface wlan0 --duration 60 --output flows.csv
```

**Parameters:**
- `--interface, -i`: Network interface to capture from (default: wlan0)
- `--duration, -d`: Capture duration in seconds (default: 60)
- `--interval, -t`: Flow aggregation interval in seconds (default: 10)
- `--output, -o`: Output CSV file (default: extracted_flows.csv)
- `--verbose, -v`: Enable verbose logging

**Examples:**
```bash
# Capture for 5 minutes on eth0 interface
sudo python flow_extractor.py -i eth0 -d 300 -o network_flows.csv

# Capture with 30-second flow windows
sudo python flow_extractor.py -i wlan0 -d 120 -t 30 -o flows_30s.csv

# Verbose output
sudo python flow_extractor.py -i wlan0 -d 60 -v -o debug_flows.csv
```

**Note:** Root privileges (sudo) are required for packet capture.

### 2. Flow Evaluation

The `flow_evaluator.py` script loads flows from a CSV file and evaluates them using the trained model.

**Basic usage:**
```bash
python flow_evaluator.py --input flows.csv --output results.csv
```

**Parameters:**
- `--input, -i`: Input CSV file with flows to evaluate (required)
- `--output, -o`: Output CSV file for results (default: evaluation_results.csv)
- `--model, -m`: Path to model file (default: DecisionTree_model.pkl)
- `--labels, -l`: Path to label array file (default: label_array.npy)
- `--features, -f`: Path to feature columns file (default: feature_columns.txt)
- `--scaler, -s`: Path to scaler file (default: scaler.pkl)
- `--verbose, -v`: Enable verbose logging

**Examples:**
```bash
# Evaluate flows with default settings
python flow_evaluator.py -i extracted_flows.csv -o threat_analysis.csv

# Use custom model files
python flow_evaluator.py -i flows.csv -o results.csv -m custom_model.pkl -l custom_labels.npy

# Verbose evaluation
python flow_evaluator.py -i flows.csv -o results.csv -v
```

## Complete Workflow

Here's a complete example of extracting flows and evaluating them:

```bash
# Step 1: Extract flows from network traffic (requires sudo)
sudo python flow_extractor.py --interface wlan0 --duration 300 --output network_flows.csv

# Step 2: Evaluate the extracted flows
python flow_evaluator.py --input network_flows.csv --output threat_results.csv
```

## Output Format

### Flow Extraction Output

The flow extractor creates a CSV file with the following columns:
- `Src IP`, `Dst IP` - Source and destination IP addresses
- `Src Port`, `Dst Port` - Source and destination ports
- `Protocol` - IP protocol number
- `Flow Duration` - Duration of the flow in microseconds
- `Conn_Rate` - Connection rate to destination port
- `Fwd Seg Size Min` - Minimum forward segment size
- `Bwd Pkt Len Mean` - Mean backward packet length
- `TotLen Fwd Pkts` - Total length of forward packets
- `Init Fwd Win Byts` - Initial forward window size
- `Init Bwd Win Byts` - Initial backward window size
- `Flow IAT Mean` - Mean inter-arrival time
- `Flow IAT Max` - Maximum inter-arrival time
- `Processing_Timestamp` - When the flow was processed

### Flow Evaluation Output

The flow evaluator adds the following columns to the input CSV:
- `Prediction` - Detailed attack classification (e.g., "DDoS", "PortScan", "Benign")
- `Threat` - Binary classification ("Malicious" or "Benign")
- `Evaluation_Timestamp` - When the evaluation was performed

## Features

### Flow Extraction Features
- **Real-time packet capture** using Scapy
- **Flow aggregation** with configurable time windows
- **Feature extraction** identical to the main IDS system
- **Traffic filtering** to exclude legitimate traffic (DNS, SSH, etc.)
- **Bidirectional flow tracking** with proper direction detection
- **Connection rate calculation** for DDoS detection
- **Inter-arrival time analysis** for timing-based attacks

### Flow Evaluation Features
- **Model loading** with automatic validation
- **Feature preprocessing** including scaling
- **Batch prediction** for efficient processing
- **Detailed threat classification** with confidence scores
- **Summary statistics** and threat analysis
- **Error handling** with graceful degradation

## Troubleshooting

### Common Issues

1. **Permission denied during packet capture**
   - Solution: Run the flow extractor with sudo privileges
   - `sudo python flow_extractor.py ...`

2. **Interface not found**
   - Solution: Check available interfaces with `ip link show` or `ifconfig`
   - Use the correct interface name (e.g., eth0, wlan0, enp0s3)

3. **Model files not found**
   - Solution: Ensure all model files are in the same directory as the scripts
   - Check file paths with `--model`, `--labels`, `--features`, `--scaler` parameters

4. **Missing features in CSV**
   - Solution: The evaluator will automatically fill missing features with zeros
   - Check the verbose output for warnings about missing columns

5. **No flows extracted**
   - Solution: Check if there's network traffic on the interface
   - Try increasing the capture duration
   - Use `--verbose` to see detailed logging

### Performance Tips

- Use shorter capture intervals (5-10 seconds) for real-time analysis
- Use longer intervals (30-60 seconds) for batch processing
- Monitor system resources during long captures
- Consider filtering specific traffic types if needed

## Integration with Main System

These scripts use the exact same algorithms and models as the main IDS web application:
- Flow extraction logic from `backend/ids/realtime_ids.py`
- Model prediction methods from the RealTimeIDS class
- Feature computation and preprocessing pipelines
- Same model files and scaling parameters

This ensures consistency between offline analysis and real-time detection.

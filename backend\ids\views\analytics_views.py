
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Count
from django.http import HttpResponse
from datetime import datetime, timedelta
import csv
import io
import random

from ..models import IDSAlert

class IDSAnalyticsView(APIView):
    def get(self, request):
        """Return analytics data based on actual alert data"""
        try:
            # Validate timeframe query parameter
            timeframe = request.query_params.get('timeframe', '7d')

            if timeframe not in ['24h', '7d', '30d', '90d']:
                return Response(
                    {"error": "Invalid timeframe. Must be one of: 24h, 7d, 30d, 90d"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Calculate time threshold based on timeframe
            if timeframe == '24h':
                time_threshold = datetime.now() - timedelta(hours=24)
            elif timeframe == '7d':
                time_threshold = datetime.now() - timedelta(days=7)
            elif timeframe == '30d':
                time_threshold = datetime.now() - timedelta(days=30)
            elif timeframe == '90d':
                time_threshold = datetime.now() - timedelta(days=90)

            # Get alerts in the selected timeframe
            alerts = IDSAlert.objects.filter(timestamp__gte=time_threshold)

            # Calculate total attacks and change percentage
            total_attacks = alerts.count()

            # For comparison, get counts from previous period
            if timeframe == '24h':
                prev_time_threshold = time_threshold - timedelta(hours=24)
            elif timeframe == '7d':
                prev_time_threshold = time_threshold - timedelta(days=7)
            elif timeframe == '30d':
                prev_time_threshold = time_threshold - timedelta(days=30)
            elif timeframe == '90d':
                prev_time_threshold = time_threshold - timedelta(days=90)

            prev_alerts_count = IDSAlert.objects.filter(
                timestamp__gte=prev_time_threshold,
                timestamp__lt=time_threshold
            ).count()

            # Calculate change percentage
            if prev_alerts_count > 0:
                change_percent = ((total_attacks - prev_alerts_count) / prev_alerts_count) * 100
            else:
                change_percent = 100 if total_attacks > 0 else 0

            # Get threat stats
            threat_stats = self._get_threat_stats(alerts, total_attacks)

            # If we have no real attacks but generated mock data, update the total attacks count
            if total_attacks == 0 and threat_stats:
                total_attacks = sum(stat['value'] for stat in threat_stats)
                # Generate a random change percentage for demonstration
                change_percent = random.uniform(-10, 20)

            # Prepare the response data
            response_data = {
                'threatStats': threat_stats,
                'attackTrends': self._get_attack_trends(timeframe, alerts),
                'attackHours': self._get_attack_hours(alerts),
                'geographicData': self._get_geographic_data(alerts),
                'totalAttacks': total_attacks,
                'changePercent': round(change_percent, 1)
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            # Log the error for server-side troubleshooting
            import logging
            logger = logging.getLogger('django')
            logger.error(f"Error in analytics view: {str(e)}")

            # Return a user-friendly error
            return Response(
                {"error": "An error occurred while processing analytics data"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_threat_stats(self, alerts, total_attacks):
        """Generate threat statistics from actual alert data"""
        try:
            # Group alerts by attack type and count
            attack_types = alerts.values('attack_type').annotate(count=Count('id'))

            # Format threat stats
            threat_stats = []

            if not attack_types:
                # Return empty data if no real attacks
                return []

            for attack in attack_types:
                attack_type = attack['attack_type']
                count = attack['count']
                percent = (count / total_attacks) * 100 if total_attacks > 0 else 0

                threat_stats.append({
                    'name': attack_type,
                    'value': count,
                    'percent': round(percent)
                })

            # Sort by count in descending order
            threat_stats.sort(key=lambda x: x['value'], reverse=True)
            return threat_stats

        except Exception as e:
            # Log the specific error
            import logging
            logger = logging.getLogger('django')
            logger.error(f"Error in _get_threat_stats: {str(e)}")

            # Return empty array as fallback
            return []

    def _get_attack_trends(self, timeframe, alerts):
        """Generate attack trends based on timeframe from actual alert data"""
        try:
            attack_trends = []
            has_data = alerts.exists()

            if timeframe == '24h':
                # Hourly data for 24 hours
                for hour in range(24):
                    date_point = datetime.now() - timedelta(hours=23-hour)
                    date_str = date_point.strftime('%H:00')

                    if has_data:
                        # Get alert count for this hour
                        hour_start = datetime(date_point.year, date_point.month, date_point.day, date_point.hour)
                        hour_end = hour_start + timedelta(hours=1)
                        hour_count = alerts.filter(timestamp__gte=hour_start, timestamp__lt=hour_end).count()
                    else:
                        # No mock data - use 0 if no real data
                        hour_count = 0

                    # Estimate traffic based on alerts (in a real system, this would come from actual traffic data)
                    traffic_multiplier = 20  # Each alert represents approximately 20 traffic flows
                    estimated_traffic = max(hour_count * traffic_multiplier, 50)  # Minimum of 50 for visualization

                    attack_trends.append({
                        'date': date_str,
                        'attacks': hour_count,
                        'traffic': estimated_traffic
                    })

            elif timeframe == '7d':
                # Daily data for 7 days
                for day in range(7):
                    date_point = datetime.now() - timedelta(days=6-day)
                    date_str = date_point.strftime('%m/%d')

                    if has_data:
                        # Get alert count for this day
                        day_start = datetime(date_point.year, date_point.month, date_point.day)
                        day_end = day_start + timedelta(days=1)
                        day_count = alerts.filter(timestamp__gte=day_start, timestamp__lt=day_end).count()
                    else:
                        # No mock data - use 0 if no real data
                        day_count = 0

                    # Estimate traffic based on alerts
                    traffic_multiplier = 20
                    estimated_traffic = max(day_count * traffic_multiplier, 100)

                    attack_trends.append({
                        'date': date_str,
                        'attacks': day_count,
                        'traffic': estimated_traffic
                    })

            elif timeframe in ['30d', '90d']:
                # Weekly data for 30 or 90 days
                weeks = 4 if timeframe == '30d' else 13
                for week in range(weeks):
                    week_ago = weeks - 1 - week
                    date_point = datetime.now() - timedelta(days=week_ago*7)
                    date_str = date_point.strftime('%m/%d')

                    if has_data:
                        # Get alert count for this week
                        week_start = datetime.now() - timedelta(days=(week_ago+1)*7)
                        week_end = datetime.now() - timedelta(days=week_ago*7)
                        week_count = alerts.filter(timestamp__gte=week_start, timestamp__lt=week_end).count()
                    else:
                        # No mock data - use 0 if no real data
                        week_count = 0

                    # Estimate traffic based on alerts
                    traffic_multiplier = 25
                    estimated_traffic = max(week_count * traffic_multiplier, 200)

                    attack_trends.append({
                        'date': date_str,
                        'attacks': week_count,
                        'traffic': estimated_traffic
                    })

            return attack_trends

        except Exception as e:
            # Log the specific error
            import logging
            logger = logging.getLogger('django')
            logger.error(f"Error in _get_attack_trends: {str(e)}")

            # Return empty array as fallback
            return []

    def _get_attack_hours(self, alerts):
        """Generate attack hour distribution from actual alert data"""
        try:
            attack_hours = []
            has_data = alerts.exists()

            # Create a distribution of attacks by hour of day
            for hour in range(24):
                hour_str = f"{hour:02d}:00"

                if has_data:
                    # Count alerts that occurred during this hour (across all days)
                    hour_count = alerts.filter(timestamp__hour=hour).count()
                else:
                    # No mock data - use 0 if no real data
                    hour_count = 0

                attack_hours.append({
                    'hour': hour_str,
                    'attacks': hour_count
                })

            return attack_hours

        except Exception as e:
            # Log the specific error
            import logging
            logger = logging.getLogger('django')
            logger.error(f"Error in _get_attack_hours: {str(e)}")

            # Return empty array as fallback
            return []

    def _get_geographic_data(self, alerts):
        """Generate geographic attack distribution data based on IP addresses"""
        try:
            # In a real system, you would resolve IP addresses to countries
            # For this implementation, we'll map some common IP prefixes to countries
            # as a demonstration of using real data

            # Sample mapping of IP prefixes to countries (for demonstration)
            country_mapping = {
                '45.33': 'United States',
                '61.177': 'China',
                '91.197': 'Russia',
                '181.49': 'Brazil',
                '116.12': 'India',
                '195.176': 'Germany',
                '212.98': 'France',
                '194.54': 'Ukraine'
            }

            # Count attacks by source country based on IP prefix
            country_counts = {}

            for alert in alerts:
                src_ip = alert.src_ip
                matched_country = None

                # Check if IP prefix matches any known country
                for prefix, country in country_mapping.items():
                    if src_ip.startswith(prefix):
                        matched_country = country
                        break

                # If no match, mark as "Other"
                if not matched_country:
                    matched_country = "Other"

                # Increment country count
                if matched_country in country_counts:
                    country_counts[matched_country] += 1
                else:
                    country_counts[matched_country] = 1

            # If no data, return empty geographic data
            if not country_counts:
                return []

            # Format geographic data
            geographic_data = []
            for country, count in country_counts.items():
                geographic_data.append({
                    'country': country,
                    'value': count,  # Value can be used for other metrics
                    'attackCount': count
                })

            # Sort by attack count
            geographic_data.sort(key=lambda x: x['attackCount'], reverse=True)
            return geographic_data

        except Exception as e:
            # Log the specific error
            import logging
            logger = logging.getLogger('django')
            logger.error(f"Error in _get_geographic_data: {str(e)}")

            # Return empty array as fallback
            return []


class AnalyticsExportView(APIView):
    """Export analytics data in various formats"""

    def get(self, request):
        """Export analytics data as CSV"""
        try:
            # Get query parameters
            timeframe = request.query_params.get('timeframe', '7d')
            export_format = request.query_params.get('format', 'csv')

            if timeframe not in ['24h', '7d', '30d', '90d']:
                return Response(
                    {"error": "Invalid timeframe. Must be one of: 24h, 7d, 30d, 90d"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if export_format != 'csv':
                return Response(
                    {"error": "Only CSV format is currently supported"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get analytics data using the same logic as the main analytics view
            analytics_data = self._get_analytics_data(timeframe)

            # Generate CSV content
            csv_content = self._generate_csv(analytics_data, timeframe)

            # Create HTTP response with CSV content
            response = HttpResponse(csv_content, content_type='text/csv')
            filename = f'analytics_{timeframe}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            import logging
            logger = logging.getLogger('django')
            logger.error(f"Error in analytics export: {str(e)}")

            return Response(
                {"error": "An error occurred while exporting analytics data"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_analytics_data(self, timeframe):
        """Get analytics data for the specified timeframe"""
        # Calculate time threshold based on timeframe
        if timeframe == '24h':
            time_threshold = datetime.now() - timedelta(hours=24)
        elif timeframe == '7d':
            time_threshold = datetime.now() - timedelta(days=7)
        elif timeframe == '30d':
            time_threshold = datetime.now() - timedelta(days=30)
        elif timeframe == '90d':
            time_threshold = datetime.now() - timedelta(days=90)

        # Get alerts in the selected timeframe
        alerts = IDSAlert.objects.filter(timestamp__gte=time_threshold)
        total_attacks = alerts.count()

        # Get analytics components
        analytics_view = IDSAnalyticsView()
        threat_stats = analytics_view._get_threat_stats(alerts, total_attacks)
        attack_trends = analytics_view._get_attack_trends(timeframe, alerts)
        attack_hours = analytics_view._get_attack_hours(alerts)
        geographic_data = analytics_view._get_geographic_data(alerts)

        return {
            'timeframe': timeframe,
            'total_attacks': total_attacks,
            'threat_stats': threat_stats,
            'attack_trends': attack_trends,
            'attack_hours': attack_hours,
            'geographic_data': geographic_data,
            'generated_at': datetime.now().isoformat()
        }

    def _generate_csv(self, data, timeframe):
        """Generate CSV content from analytics data"""
        output = io.StringIO()

        # Write header information
        output.write(f"# Flow Sentinel IDS Analytics Report\n")
        output.write(f"# Generated: {data['generated_at']}\n")
        output.write(f"# Timeframe: {timeframe}\n")
        output.write(f"# Total Attacks: {data['total_attacks']}\n")
        output.write("\n")

        # Write threat statistics
        output.write("## Threat Statistics\n")
        output.write("Attack Type,Count,Percentage\n")
        for stat in data['threat_stats']:
            output.write(f"{stat['name']},{stat['value']},{stat['percent']}\n")
        output.write("\n")

        # Write attack trends
        if data['attack_trends']:
            output.write("## Attack Trends\n")
            output.write("Time,Attacks,Traffic\n")
            for trend in data['attack_trends']:
                output.write(f"{trend['date']},{trend['attacks']},{trend.get('traffic', 0)}\n")
            output.write("\n")

        # Write attack hours distribution
        if data['attack_hours']:
            output.write("## Attack Distribution by Hour\n")
            output.write("Hour,Count\n")
            for hour in data['attack_hours']:
                output.write(f"{hour['hour']},{hour['attacks']}\n")
            output.write("\n")

        # Write geographic data
        if data['geographic_data']:
            output.write("## Geographic Distribution\n")
            output.write("Country,Count\n")
            for geo in data['geographic_data']:
                output.write(f"{geo['country']},{geo['attackCount']}\n")

        return output.getvalue()

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Cpu,
  MemoryStick,
  HardDrive,
  Network,
  Zap,
  Thermometer,
  Monitor
} from "lucide-react";
import { SystemResourceData, getUsageColor, formatBytes } from "@/services/systemResourceService";

interface ResourceOverviewProps {
  data: SystemResourceData;
}

export function ResourceOverview({ data }: ResourceOverviewProps) {
  return (
    <div className="space-y-6">
      {/* CPU and Memory Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* CPU Card - Task Manager Style */}
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between pb-3">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Cpu className="h-5 w-5 text-green-600" />
              CPU
              {data.cpu.monitoring_method === 'linux_proc' && (
                <Badge variant="outline" className="text-xs bg-green-100 text-green-800 border-green-300">
                  Linux Enhanced
                </Badge>
              )}
            </CardTitle>
            <div className="text-right">
              <div className="text-3xl font-bold text-green-600">
                {data.cpu.usage_percent}%
              </div>
              <div className="text-xs text-muted-foreground">
                {data.cpu.frequency_current ? `${data.cpu.frequency_current} MHz` : 'N/A'}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Task Manager style progress bar */}
              <div className="relative">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-sm h-4">
                  <div
                    className="bg-green-500 h-4 rounded-sm transition-all duration-300 relative overflow-hidden"
                    style={{ width: `${data.cpu.usage_percent}%` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                  </div>
                </div>
                <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white mix-blend-difference">
                  {data.cpu.usage_percent}%
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">Physical Cores</span>
                  <div className="font-semibold">{data.cpu.cores_physical}</div>
                </div>
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">Logical Cores</span>
                  <div className="font-semibold">{data.cpu.cores_logical}</div>
                </div>
                {data.cpu.frequency_current && (
                  <>
                    <div className="space-y-1">
                      <span className="text-muted-foreground text-xs">Current Freq</span>
                      <div className="font-semibold">{data.cpu.frequency_current} MHz</div>
                    </div>
                    <div className="space-y-1">
                      <span className="text-muted-foreground text-xs">Max Freq</span>
                      <div className="font-semibold">{data.cpu.frequency_max} MHz</div>
                    </div>
                  </>
                )}
                {/* Linux-specific enhanced metrics */}
                {data.cpu.monitoring_method === 'linux_proc' && (
                  <>
                    {data.cpu.iowait_percent !== undefined && (
                      <div className="space-y-1">
                        <span className="text-muted-foreground text-xs">I/O Wait</span>
                        <div className="font-semibold">{data.cpu.iowait_percent}%</div>
                      </div>
                    )}
                    {data.cpu.running_processes !== undefined && (
                      <div className="space-y-1">
                        <span className="text-muted-foreground text-xs">Running Procs</span>
                        <div className="font-semibold">{data.cpu.running_processes}/{data.cpu.total_processes}</div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Memory Card - Task Manager Style */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between pb-3">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <MemoryStick className="h-5 w-5 text-blue-600" />
              Memory
            </CardTitle>
            <div className="text-right">
              <div className="text-3xl font-bold text-blue-600">
                {data.memory.usage_percent}%
              </div>
              <div className="text-xs text-muted-foreground">
                {data.memory.used_gb} / {data.memory.total_gb} GB
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Task Manager style progress bar */}
              <div className="relative">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-sm h-4">
                  <div
                    className="bg-blue-500 h-4 rounded-sm transition-all duration-300 relative overflow-hidden"
                    style={{ width: `${data.memory.usage_percent}%` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                  </div>
                </div>
                <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white mix-blend-difference">
                  {data.memory.usage_percent}%
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">Used</span>
                  <div className="font-semibold">{data.memory.used_gb} GB</div>
                </div>
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">Available</span>
                  <div className="font-semibold">{data.memory.available_gb} GB</div>
                </div>
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">Total</span>
                  <div className="font-semibold">{data.memory.total_gb} GB</div>
                </div>
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">Swap Used</span>
                  <div className="font-semibold">{data.memory.swap_used_gb} GB</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* GPU Cards */}
      {data.gpu.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            GPU Information
          </h3>
          <div className="grid gap-6 md:grid-cols-2">
            {data.gpu.map((gpu, index) => (
              <Card key={gpu.id}>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">
                    {gpu.name}
                  </CardTitle>
                  <div className="flex gap-2">
                    <Badge variant="outline" className={getUsageColor(gpu.usage_percent)}>
                      {gpu.usage_percent}%
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Thermometer className="h-3 w-3" />
                      {gpu.temperature}°C
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>GPU Usage</span>
                        <span>{gpu.usage_percent}%</span>
                      </div>
                      <Progress value={gpu.usage_percent} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Memory Usage</span>
                        <span>{gpu.memory_usage_percent}%</span>
                      </div>
                      <Progress value={gpu.memory_usage_percent} className="h-2" />
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Memory Used:</span>
                        <div className="font-medium">{gpu.memory_used_mb} MB</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Memory Total:</span>
                        <div className="font-medium">{gpu.memory_total_mb} MB</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Disk and Network Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Disk Usage */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              Disk Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.disk.map((disk, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="text-sm">
                      <div className="font-medium">{disk.device}</div>
                      <div className="text-muted-foreground text-xs">{disk.mountpoint}</div>
                    </div>
                    <Badge variant="outline" className={getUsageColor(disk.usage_percent)}>
                      {disk.usage_percent}%
                    </Badge>
                  </div>
                  <Progress value={disk.usage_percent} className="h-2" />
                  <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                    <div>Used: {disk.used_gb} GB</div>
                    <div>Free: {disk.free_gb} GB</div>
                    <div>Total: {disk.total_gb} GB</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Network Usage */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Network className="h-4 w-4" />
              Network Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {formatBytes(data.network.bytes_recv_per_sec)}/s
                  </div>
                  <div className="text-sm text-muted-foreground">Download</div>
                </div>
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatBytes(data.network.bytes_sent_per_sec)}/s
                  </div>
                  <div className="text-sm text-muted-foreground">Upload</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Total Sent:</span>
                  <div className="font-medium">{data.network.bytes_sent_gb} GB</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Total Received:</span>
                  <div className="font-medium">{data.network.bytes_recv_gb} GB</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Packets Sent:</span>
                  <div className="font-medium">{data.network.packets_sent.toLocaleString()}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Connections:</span>
                  <div className="font-medium">{data.network.connections_count}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

#!/usr/bin/env python3
"""
Test script to verify IDS settings functionality
"""

import os
import sys
import django
import time

# Setup Django
sys.path.append('backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ids.models import IDSSettings
from ids.ids_manager import IDSManager

def test_settings_functionality():
    """Test that settings are properly saved and used by the IDS"""
    
    print("=== IDS Settings Test ===")
    
    # 1. Test database operations
    print("\n1. Testing database operations...")
    
    # Get current settings
    settings = IDSSettings.get_settings()
    print(f"Current settings:")
    print(f"  - capture_interval: {settings.capture_interval}")
    print(f"  - max_idle_time: {settings.max_idle_time}")
    print(f"  - pcap_capture: {settings.pcap_capture}")
    
    # Update settings
    original_interval = settings.capture_interval
    original_idle_time = settings.max_idle_time
    
    test_interval = 8.0
    test_idle_time = 4.0
    
    settings.capture_interval = test_interval
    settings.max_idle_time = test_idle_time
    settings.save()
    
    print(f"\nUpdated settings to:")
    print(f"  - capture_interval: {test_interval}")
    print(f"  - max_idle_time: {test_idle_time}")
    
    # Verify the update
    settings_reloaded = IDSSettings.get_settings()
    assert settings_reloaded.capture_interval == test_interval, f"Expected {test_interval}, got {settings_reloaded.capture_interval}"
    assert settings_reloaded.max_idle_time == test_idle_time, f"Expected {test_idle_time}, got {settings_reloaded.max_idle_time}"
    
    print("✅ Database operations working correctly")
    
    # 2. Test IDS initialization with settings
    print("\n2. Testing IDS initialization...")
    
    try:
        ids_manager = IDSManager()
        
        # Make sure IDS is stopped first
        if ids_manager.is_running:
            print("Stopping existing IDS...")
            ids_manager.stop_ids()
            time.sleep(2)
        
        print("Starting IDS with test settings...")
        result = ids_manager.start_ids_with_settings(settings_reloaded)
        
        if result['status'] == 'success':
            print("✅ IDS started successfully with custom settings")
            
            # Check if the IDS instance has the correct settings
            if ids_manager.ids:
                actual_interval = ids_manager.ids.capture_interval
                actual_idle_time = ids_manager.ids.max_idle_time
                actual_pcap = ids_manager.ids.pcap_capture
                
                print(f"IDS instance settings:")
                print(f"  - capture_interval: {actual_interval}")
                print(f"  - max_idle_time: {actual_idle_time}")
                print(f"  - pcap_capture: {actual_pcap}")
                
                # Verify settings match
                if actual_interval == test_interval:
                    print("✅ Capture interval correctly applied")
                else:
                    print(f"❌ Capture interval mismatch: expected {test_interval}, got {actual_interval}")
                
                if actual_idle_time == test_idle_time:
                    print("✅ Max idle time correctly applied")
                else:
                    print(f"❌ Max idle time mismatch: expected {test_idle_time}, got {actual_idle_time}")
                
                if actual_pcap == settings_reloaded.pcap_capture:
                    print("✅ PCAP capture setting correctly applied")
                else:
                    print(f"❌ PCAP capture mismatch: expected {settings_reloaded.pcap_capture}, got {actual_pcap}")
            
            # Stop the IDS
            print("\nStopping IDS...")
            ids_manager.stop_ids()
            time.sleep(1)
            
        else:
            print(f"❌ Failed to start IDS: {result['message']}")
            
    except Exception as e:
        print(f"❌ Error testing IDS: {str(e)}")
    
    # 3. Restore original settings
    print("\n3. Restoring original settings...")
    settings.capture_interval = original_interval
    settings.max_idle_time = original_idle_time
    settings.save()
    
    print(f"Restored settings:")
    print(f"  - capture_interval: {original_interval}")
    print(f"  - max_idle_time: {original_idle_time}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_settings_functionality()

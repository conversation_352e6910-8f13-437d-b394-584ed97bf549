# Generated by Django 4.2.10 on 2025-04-30 15:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ids', '0003_alter_idsalert_dst_ip_alter_idsalert_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='IDSSettings',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('capture_interface', models.CharField(default='auto', max_length=50)),
                ('capture_interval', models.FloatField(default=2.0)),
                ('max_idle_time', models.FloatField(default=2.0)),
                ('output_dir', models.CharField(default='./ids_results', max_length=255)),
                ('dark_mode', models.BooleanField(default=True)),
                ('model_path', models.Char<PERSON>ield(default='./DecisionTree_model.pkl', max_length=255)),
                ('alert_threshold', models.Float<PERSON>ield(default=0.7)),
                ('conn_rate_window', models.Float<PERSON>ield(default=5.0)),
                ('enable_learning', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('enable_heuristics', models.BooleanField(default=True)),
                ('enable_notifications', models.BooleanField(default=True)),
                ('email_alerts', models.BooleanField(default=False)),
                ('email_address', models.CharField(blank=True, default='', max_length=255)),
                ('min_severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='medium', max_length=10)),
                ('historical_window', models.IntegerField(default=60)),
                ('debug_mode', models.BooleanField(default=False)),
                ('pcap_capture', models.BooleanField(default=True)),
                ('auto_mitigation', models.BooleanField(default=False)),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ids_idssettings',
            },
        ),
    ]


from django.db import models
from django.utils import timezone

class IDSSettings(models.Model):
    """Model for IDS settings."""

    id = models.AutoField(primary_key=True)
    capture_interface = models.CharField(max_length=50, default='wlan0')
    capture_interval = models.FloatField(default=2.0)
    max_idle_time = models.FloatField(default=1.0)  # Reduced from 2.0 to 1.0 for faster flow timeout
    output_dir = models.CharField(max_length=255, default='./ids_results')
    dark_mode = models.BooleanField(default=True)
    enable_notifications = models.BooleanField(default=True)
    email_alerts = models.BooleanField(default=False)
    email_address = models.CharField(max_length=255, blank=True, default='')
    historical_window = models.IntegerField(default=60)
    debug_mode = models.BooleanField(default=False)
    pcap_capture = models.BooleanField(default=True)
    consolidated_csv = models.BooleanField(default=True)
    csv_save_interval = models.FloatField(default=300.0)  # 5 minutes
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ids_idssettings'

    def __str__(self):
        return f"IDS Settings (last updated: {self.last_updated})"

    @classmethod
    def get_settings(cls):
        """Get the current settings or create default settings if none exist."""
        settings, created = cls.objects.get_or_create(id=1)
        return settings

    def to_dict(self):
        """Convert settings to a dictionary for the API."""
        return {
            'captureInterface': self.capture_interface,
            'captureInterval': self.capture_interval,
            'maxIdleTime': self.max_idle_time,
            'outputDir': self.output_dir,
            'darkMode': self.dark_mode,
            'enableNotifications': self.enable_notifications,
            'emailAlerts': self.email_alerts,
            'emailAddress': self.email_address,
            'historicalWindow': self.historical_window,
            'debugMode': self.debug_mode,
            'pcapCapture': self.pcap_capture,
            'consolidatedCsv': self.consolidated_csv,
            'csvSaveInterval': self.csv_save_interval
        }

    def update_from_dict(self, settings_dict):
        """Update settings from a dictionary."""
        if 'captureInterface' in settings_dict:
            self.capture_interface = settings_dict['captureInterface']
        if 'captureInterval' in settings_dict:
            self.capture_interval = float(settings_dict['captureInterval'])
        if 'maxIdleTime' in settings_dict:
            self.max_idle_time = float(settings_dict['maxIdleTime'])
        if 'outputDir' in settings_dict:
            self.output_dir = settings_dict['outputDir']
        if 'darkMode' in settings_dict:
            self.dark_mode = settings_dict['darkMode']
        if 'enableNotifications' in settings_dict:
            self.enable_notifications = settings_dict['enableNotifications']
        if 'emailAlerts' in settings_dict:
            self.email_alerts = settings_dict['emailAlerts']
        if 'emailAddress' in settings_dict:
            self.email_address = settings_dict['emailAddress']
        if 'historicalWindow' in settings_dict:
            self.historical_window = int(settings_dict['historicalWindow'])
        if 'debugMode' in settings_dict:
            self.debug_mode = settings_dict['debugMode']
        if 'pcapCapture' in settings_dict:
            self.pcap_capture = settings_dict['pcapCapture']
        if 'consolidatedCsv' in settings_dict:
            self.consolidated_csv = settings_dict['consolidatedCsv']
        if 'csvSaveInterval' in settings_dict:
            self.csv_save_interval = float(settings_dict['csvSaveInterval'])
        self.save()

class IDSAlert(models.Model):
    """Model for IDS alerts."""
    STATUS_CHOICES = (
        ('new', 'New'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
    )

    id = models.AutoField(primary_key=True)
    timestamp = models.DateTimeField(default=timezone.now)
    src_ip = models.CharField(max_length=45)  # IPv6 addresses are longer
    src_port = models.IntegerField()
    dst_ip = models.CharField(max_length=45)
    dst_port = models.IntegerField()
    protocol = models.CharField(max_length=10, default="TCP")
    attack_type = models.CharField(max_length=100)
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='new')
    description = models.TextField(default="No description provided")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'ids_idsalert'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['status', '-timestamp']),
            models.Index(fields=['attack_type', '-timestamp']),
            models.Index(fields=['-created_at']),
            # Additional indexes for performance optimization
            models.Index(fields=['-timestamp']),  # For time-based queries
            models.Index(fields=['attack_type']),  # For aggregation queries
            models.Index(fields=['src_ip', '-timestamp']),  # For geographic analysis
            models.Index(fields=['timestamp']),  # For range queries
        ]

    def __str__(self):
        return f"{self.attack_type} from {self.src_ip}:{self.src_port} at {self.timestamp}"


class IDSLog(models.Model):
    """Model for IDS logs."""
    LEVEL_CHOICES = (
        ('debug', 'Debug'),
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error'),
    )

    id = models.AutoField(primary_key=True)
    timestamp = models.DateTimeField(default=timezone.now)
    level = models.CharField(max_length=10, choices=LEVEL_CHOICES)
    message = models.TextField()

    class Meta:
        db_table = 'ids_idslog'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.timestamp} - {self.level}: {self.message[:50]}"

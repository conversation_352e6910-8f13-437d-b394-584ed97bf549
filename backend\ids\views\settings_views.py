
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import time
import socket
import psutil
import platform

from ..models import IDSSettings

class IDSSettingsView(APIView):
    def get(self, request):
        """Return current IDS settings"""
        # Get settings from database or create default settings if none exist
        settings_obj = IDSSettings.get_settings()
        settings = settings_obj.to_dict()

        # Get available network interfaces
        available_interfaces = self.get_available_interfaces()

        return Response({
            'settings': settings,
            'available_interfaces': available_interfaces
        }, status=status.HTTP_200_OK)

    def post(self, request):
        """Save IDS settings"""
        try:
            settings_data = request.data.get('settings', {})

            # Validate settings
            if not isinstance(settings_data, dict):
                return Response(
                    {'status': 'error', 'message': 'Invalid settings format'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get settings from database or create default settings if none exist
            settings_obj = IDSSettings.get_settings()

            # Update settings with new values
            settings_obj.update_from_dict(settings_data)

            # Small delay to make the saving feel more realistic
            time.sleep(0.5)

            return Response({
                'status': 'success',
                'message': 'Settings saved successfully'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {'status': 'error', 'message': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_available_interfaces(self):
        """Get available network interfaces on the system."""
        available_interfaces = []  # Start with empty list

        try:
            # Different approaches based on platform
            if platform.system() == "Windows":
                # On Windows, use psutil
                for iface, addrs in psutil.net_if_addrs().items():
                    # Only include interfaces that have an IPv4 address
                    for addr in addrs:
                        if addr.family == socket.AF_INET:
                            available_interfaces.append(iface)
                            break
            else:
                # On Unix-like systems, use socket.if_nameindex() if available
                try:
                    for idx, iface in socket.if_nameindex():
                        available_interfaces.append(iface)
                except (AttributeError, OSError):
                    # Fallback to psutil on older Python versions or if if_nameindex fails
                    for iface, addrs in psutil.net_if_addrs().items():
                        for addr in addrs:
                            if addr.family == socket.AF_INET:
                                available_interfaces.append(iface)
                                break
        except Exception as e:
            # If all else fails, return some common interface names
            print(f"Error detecting network interfaces: {e}")
            available_interfaces.extend(['wlan0', 'eth0', 'en0', 'Wi-Fi', 'Ethernet'])

        # Remove duplicates and prioritize wlan0 for Kali Linux
        available_interfaces = list(set(available_interfaces))

        # Sort but put wlan0 first if it exists (common on Kali Linux)
        if 'wlan0' in available_interfaces:
            available_interfaces.remove('wlan0')
            available_interfaces = ['wlan0'] + sorted(available_interfaces)
        else:
            available_interfaces = sorted(available_interfaces)

        # Add 'auto' option at the end
        available_interfaces.append('auto')

        return available_interfaces




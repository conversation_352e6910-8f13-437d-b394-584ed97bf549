import { useState, useEffect, useRef, useCallback, memo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from "recharts";
import { Activity, Network, TrendingUp, Zap } from "lucide-react";
import { fetchTrafficData } from "@/services/trafficService";
import { TrafficResponse, TrafficDataPoint } from "@/types/traffic";
import { useToast } from "@/hooks/use-toast";

// Utility function for formatting numbers
const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
};

// Memoized chart components for performance
const NetworkPacketAreaChart = memo(({ data, isLoading }: { data: TrafficDataPoint[], isLoading: boolean }) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatNumber(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return <div className="h-full w-full bg-muted animate-pulse rounded"></div>;
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
        <XAxis
          dataKey="time"
          className="text-xs"
        />
        <YAxis
          tickFormatter={formatNumber}
          className="text-xs"
        />
        <Tooltip content={<CustomTooltip />} />
        <Area
          type="monotone"
          dataKey="total_packets"
          stroke="#3b82f6"
          fill="#3b82f6"
          fillOpacity={0.3}
          name="Total Flows"
          isAnimationActive={false}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
});

const PacketRateBandwidthChart = memo(({ data, isLoading }: { data: TrafficDataPoint[], isLoading: boolean }) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatNumber(entry.value)}
              {entry.dataKey === 'bandwidth_utilization' && '%'}
              {entry.dataKey === 'packet_rate' && ' packets/s'}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return <div className="h-full w-full bg-muted animate-pulse rounded"></div>;
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
        <XAxis
          dataKey="time"
          className="text-xs"
        />
        <YAxis
          yAxisId="left"
          tickFormatter={(value) => `${value} p/s`}
          className="text-xs"
        />
        <YAxis
          yAxisId="right"
          orientation="right"
          tickFormatter={(value) => `${value}%`}
          className="text-xs"
        />
        <Tooltip content={<CustomTooltip />} />
        <Line
          yAxisId="left"
          type="monotone"
          dataKey="packet_rate"
          stroke="#10b981"
          strokeWidth={2}
          dot={false}
          name="Packet Rate"
          isAnimationActive={false}
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey="bandwidth_utilization"
          stroke="#f59e0b"
          strokeWidth={2}
          dot={false}
          name="Bandwidth Util"
          isAnimationActive={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );
});

interface NetworkPacketChartProps {
  className?: string;
  showHeader?: boolean;
  showStats?: boolean;
}

export function NetworkPacketChart({ className, showHeader = true, showStats = true }: NetworkPacketChartProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [timeframe, setTimeframe] = useState("1h");
  const [trafficData, setTrafficData] = useState<TrafficResponse>({
    traffic_data: [],
    protocol_data: [],
    packet_stats: {
      avg_packet_rate: 0,
      peak_packet_rate: 0,
      total_packets_period: 0,
      avg_bandwidth_util: 0,
      peak_bandwidth_util: 0,
      packet_distribution: {
        tcp_percent: 0,
        udp_percent: 0,
        icmp_percent: 0,
        other_percent: 0,
      },
    },
    timeframe: "1h",
    total_points: 0
  });
  const [isInitialized, setIsInitialized] = useState(false);
  const lastUpdateRef = useRef(0);
  const isUpdatingRef = useRef(false);

  // Enhanced data fetching with error handling and persistence
  const loadTrafficData = useCallback(async (isInitial: boolean = false) => {
    const now = Date.now();

    // Debouncing: Skip if last update was less than 2 seconds ago or if already updating
    if (!isInitial && (now - lastUpdateRef.current < 2000 || isUpdatingRef.current)) {
      console.warn('Skipping traffic update: too frequent or already updating');
      return;
    }

    lastUpdateRef.current = now;
    isUpdatingRef.current = true;

    try {
      if (isInitial) {
        setIsLoading(true);
      } else {
        setIsUpdating(true);
      }

      const data = await fetchTrafficData(timeframe, now);

      // Only update if we got valid data
      if (data && (data.traffic_data.length > 0 || data.total_points >= 0)) {
        setTrafficData(data);
      } else {
        console.warn('Received empty traffic data, keeping previous data');
      }

      if (isInitial) {
        setIsInitialized(true);
      }
    } catch (error) {
      console.error('Error fetching traffic data:', error);

      // Handle different types of errors
      if (error.name === 'AbortError') {
        console.warn('Traffic API timeout - backend is slow, but graphs will continue with cached data');
        // Don't show error toast for timeouts - the service handles fallback
      } else {
        // Only show toast for non-timeout errors and only on initial load
        if (isInitial || !isInitialized) {
          toast({
            title: "Connection Error",
            description: "Could not fetch network packet data",
            variant: "destructive"
          });
        }
      }
    } finally {
      if (isInitial) {
        setIsLoading(false);
      } else {
        setIsUpdating(false);
      }
      isUpdatingRef.current = false;
    }
  }, [timeframe, isInitialized, toast]);

  // Initialize and set up real-time updates
  useEffect(() => {
    loadTrafficData(true);

    // Poll for updates every 3 seconds
    const intervalId = setInterval(() => {
      if (isInitialized) {
        loadTrafficData(false);
      }
    }, 3000);

    // Handle browser throttling - force update when tab becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isInitialized) {
        console.log('Tab became visible, forcing traffic update');
        loadTrafficData(false);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(intervalId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [timeframe, isInitialized, loadTrafficData]);

  const handleTimeframeChange = (value: string) => {
    setTimeframe(value);
  };

  // Format numbers for display
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };


  return (
    <div className={className}>
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Network className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Network Packet Distribution</h2>
            {isUpdating && (
              <Badge variant="outline" className="animate-pulse">
                Updating...
              </Badge>
            )}
          </div>

          <Select value={timeframe} onValueChange={handleTimeframeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">1 Hour</SelectItem>
              <SelectItem value="6h">6 Hours</SelectItem>
              <SelectItem value="24h">24 Hours</SelectItem>
              <SelectItem value="7d">7 Days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Statistics Cards */}
      {showStats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Packets</CardTitle>
              <Activity className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
              ) : (
                <>
                  <div className="text-2xl font-bold">{formatNumber(trafficData.packet_stats?.total_packets_period || 0)}</div>
                  <p className="text-xs text-muted-foreground">
                    Avg: {trafficData.packet_stats?.avg_packet_rate || 0} packets/s
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Peak Packet Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
              ) : (
                <>
                  <div className="text-2xl font-bold">{trafficData.packet_stats?.peak_packet_rate || 0}</div>
                  <p className="text-xs text-muted-foreground">packets per second</p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Bandwidth Util</CardTitle>
              <Zap className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
              ) : (
                <>
                  <div className="text-2xl font-bold">{trafficData.packet_stats?.avg_bandwidth_util || 0}%</div>
                  <p className="text-xs text-muted-foreground">
                    Peak: {trafficData.packet_stats?.peak_bandwidth_util || 0}%
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Protocol Mix</CardTitle>
              <Network className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
              ) : (
                <div className="flex flex-wrap gap-1">
                  <Badge variant="outline" className="text-xs">
                    TCP: {trafficData.packet_stats.packet_distribution?.tcp_percent || 0}%
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    UDP: {trafficData.packet_stats.packet_distribution?.udp_percent || 0}%
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Packet Chart */}
      <div className="grid gap-6 md:grid-cols-2 mb-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Network Packets Over Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <NetworkPacketAreaChart data={trafficData.traffic_data} isLoading={isLoading} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Packet Rate & Bandwidth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <PacketRateBandwidthChart data={trafficData.traffic_data} isLoading={isLoading} />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

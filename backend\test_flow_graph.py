#!/usr/bin/env python3
"""
Test script to verify Network Flow Graph functionality
"""

import os
import sys
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ids.models import IDSSettings
from ids.ids_manager import IDSManager

def test_flow_graph():
    """Test that flow history is being collected for graphs"""
    
    print("=== Testing Network Flow Graph Fix ===")
    
    # Start IDS to generate flow data
    settings = IDSSettings.get_settings()
    ids_manager = IDSManager()

    if ids_manager.is_running:
        print("Stopping existing IDS...")
        ids_manager.stop_ids()
        time.sleep(1)

    print("Starting IDS to collect flow data...")
    result = ids_manager.start_ids_with_settings(settings)

    if result['status'] == 'success':
        print("IDS started. Collecting flow data for 15 seconds...")
        time.sleep(15)
        
        # Check if flow history is being collected
        if ids_manager.ids and hasattr(ids_manager.ids, 'flow_history'):
            flow_history = list(ids_manager.ids.flow_history)
            print(f"Flow history collected: {len(flow_history)} records")
            
            if flow_history:
                latest = flow_history[-1]
                print(f"Latest record: {latest['total_flows']} flows at {latest['time_str']}")
                print(f"Protocol breakdown: TCP={latest['tcp_flows']}, UDP={latest['udp_flows']}")
                print(f"Flow rate: {latest['flow_rate']} flows/min")
            
            # Test the get_flow_history method
            history_1h = ids_manager.ids.get_flow_history('1h')
            print(f"1-hour history: {len(history_1h)} records")
            
            # Test traffic API endpoint
            print("\nTesting traffic API...")
            from ids.views.data_views import IDSTrafficView
            from django.test import RequestFactory
            
            factory = RequestFactory()
            request = factory.get('/api/ids/traffic?timeframe=1h')
            
            view = IDSTrafficView()
            response = view.get(request)
            
            if response.status_code == 200:
                data = response.data
                print(f"API returned {len(data['traffic_data'])} data points")
                if data['traffic_data']:
                    first_point = data['traffic_data'][0]
                    print(f"First data point: {first_point['total_flows']} flows at {first_point['time']}")
            else:
                print(f"API error: {response.status_code}")
        else:
            print("Flow history not found - check if flow_history attribute exists")
        
        print("Stopping IDS...")
        ids_manager.stop_ids()
    else:
        print(f"Failed to start IDS: {result}")

    print("Test complete")

if __name__ == "__main__":
    test_flow_graph()

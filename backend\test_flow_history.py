#!/usr/bin/env python3
"""
Test script to verify flow history collection
"""

import os
import sys
import django
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'flow_sentinel.settings')
django.setup()

from ids.ids_manager import IDSManager
from ids.models import IDSSettings

def test_flow_history():
    """Test that flow history is being collected"""
    
    print("=== Testing Flow History Collection ===")
    
    # Stop any running IDS
    ids_manager = IDSManager()
    if ids_manager.is_running:
        print("Stopping existing IDS...")
        ids_manager.stop_ids()
        time.sleep(2)

    # Start IDS with settings
    print("Starting IDS...")
    settings = IDSSettings.get_settings()
    result = ids_manager.start_ids_with_settings(settings)
    print(f"Start result: {result['status']}")

    if result['status'] == 'success':
        print("IDS started. Waiting 15 seconds for flow data...")
        time.sleep(15)
        
        print("Checking flow history...")
        if hasattr(ids_manager.ids, 'flow_history'):
            print(f"Flow history length: {len(ids_manager.ids.flow_history)}")
            
            if ids_manager.ids.flow_history:
                print("Recent flow records:")
                for i, record in enumerate(list(ids_manager.ids.flow_history)[-3:]):
                    print(f"  {i}: {record['time_str']} - {record['total_flows']} flows")
            else:
                print("Flow history is empty")
        else:
            print("No flow_history attribute found")
        
        print("Stopping IDS...")
        ids_manager.stop_ids()
    else:
        print(f"Failed to start: {result['message']}")

    print("Test complete")

if __name__ == "__main__":
    test_flow_history()

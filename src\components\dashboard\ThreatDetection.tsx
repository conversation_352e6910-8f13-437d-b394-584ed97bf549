
import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useState, useEffect, useRef } from "react";

interface ThreatStat {
  name: string;
  value: number;
}

export function ThreatDetection() {
  const [loading, setLoading] = useState(true);
  const [threatData, setThreatData] = useState<ThreatStat[]>([]);
  const [totalThreats, setTotalThreats] = useState(0);
  const wsRef = useRef<WebSocket | null>(null);
  const fetchingRef = useRef(false); // Prevent overlapping requests
  
  useEffect(() => {
    // Initial fetch
    fetchThreatData();

    // Connect to WebSocket for real-time updates
    connectWebSocket();

    // Reduced polling frequency to 30 seconds (was 15) to reduce server load
    // WebSocket should handle most real-time updates
    const interval = setInterval(fetchThreatData, 30000);

    return () => {
      clearInterval(interval);
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);
  
  const connectWebSocket = () => {
    // Close any existing connection
    if (wsRef.current) {
      wsRef.current.close();
    }
    
    const ws = new WebSocket('ws://localhost:8000/ws/ids/');
    
    ws.onopen = () => {
      console.log('ThreatDetection WebSocket connected');
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        // If we receive a status update with threat stats, update the UI
        if (data.type === "status" && data.stats && (data.stats.database_threats?.attack_types || data.stats.attack_types)) {
          processThreatData(data.stats);
        }
      } catch (e) {
        console.error("Failed to parse WebSocket message:", e);
      }
    };
    
    ws.onclose = () => {
      console.log('ThreatDetection WebSocket disconnected');
      // Try to reconnect after a delay
      setTimeout(connectWebSocket, 5000);
    };
    
    ws.onerror = (error) => {
      console.error("ThreatDetection WebSocket error:", error);
    };
    
    wsRef.current = ws;
  };
  
  const fetchThreatData = async () => {
    // Prevent overlapping requests
    if (fetchingRef.current) {
      console.log('ThreatDetection: Skipping fetch - request already in progress');
      return;
    }

    try {
      fetchingRef.current = true;
      setLoading(true);

      const response = await fetch('http://localhost:8000/api/ids/status');

      if (response.ok) {
        const data = await response.json();

        if (data.stats) {
          processThreatData(data.stats);
        }
      }
    } catch (error) {
      console.error("Error fetching threat data:", error);
    } finally {
      setLoading(false);
      fetchingRef.current = false;
    }
  };
  
  const processThreatData = (stats: any) => {
    // Use database threat data if available, otherwise fall back to real-time data
    const attackTypes = stats.database_threats?.attack_types || stats.attack_types;

    if (attackTypes) {
      // Convert attack types object to array format
      const threatStats: ThreatStat[] = [];
      let total = 0;

      // Process attack types from the backend
      Object.entries(attackTypes).forEach(([name, count]) => {
        total += count as number;
        threatStats.push({
          name,
          value: count as number
        });
      });
      
      // Sort by count (descending)
      threatStats.sort((a, b) => b.value - a.value);
      
      // Get top threats or add "Other" category if needed
      let processedThreats: ThreatStat[];
      if (threatStats.length <= 5) {
        processedThreats = threatStats;
      } else {
        const topThreats = threatStats.slice(0, 4);
        const otherCount = threatStats.slice(4).reduce((sum, item) => sum + item.value, 0);
        processedThreats = [
          ...topThreats,
          { name: 'Other', value: otherCount }
        ];
      }
      
      // Calculate percentages for the visualization
      processedThreats = processedThreats.map(item => ({
        ...item,
        value: total ? Math.round((item.value / total) * 100) : 0
      }));
      
      setThreatData(processedThreats);
      setTotalThreats(total);
    }
  };
  
  // Get colors for threat visualization
  const getThreatColor = (index: number) => {
    const colors = [
      "bg-alert-high", 
      "bg-alert-medium", 
      "bg-primary", 
      "bg-security-warning", 
      "bg-muted"
    ];
    return colors[index % colors.length];
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Threat Distribution</CardTitle>
        <CardDescription>
          Types of attacks detected today
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[240px]">
          {loading && threatData.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full"></div>
            </div>
          ) : (
            <>
              <div className="h-full w-full flex items-center justify-center">
                <div className="w-32 h-32 rounded-full border-4 border-security-danger relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-xs text-muted-foreground">Threats</div>
                      <div className="text-xl font-bold">{totalThreats}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 grid grid-cols-2 gap-2">
                {threatData.length === 0 ? (
                  <div className="col-span-2 text-center text-sm text-muted-foreground">
                    No threats detected
                  </div>
                ) : (
                  threatData.map((item, i) => (
                    <div key={item.name} className="flex items-center gap-2">
                      <div className={`h-3 w-3 rounded-full ${getThreatColor(i)}`}></div>
                      <span className="text-xs">{item.name} ({item.value}%)</span>
                    </div>
                  ))
                )}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

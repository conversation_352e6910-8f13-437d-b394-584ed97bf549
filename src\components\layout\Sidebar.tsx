
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  Alert<PERSON>riangle,
  BarChart3,
  Home,
  LayoutGrid,
  Monitor,
  Settings,
  Shield,
  Wifi
} from "lucide-react";

const sidebarItems = [
  {
    name: "Dashboard",
    href: "/",
    icon: Home
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    href: "/alerts",
    icon: AlertTriangle
  },
  {
    name: "Analytics",
    href: "/analytics",
    icon: BarChart3
  },
  {
    name: "System Resources",
    href: "/system-resources",
    icon: Monitor
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings
  }
];

export function Sidebar() {
  const location = useLocation();

  return (
    <div className="hidden md:flex h-screen w-64 flex-col fixed left-0 top-0 border-r border-border/40 bg-sidebar">
      <div className="flex h-14 items-center border-b border-border/40 px-4">
        <Link to="/" className="flex items-center gap-2 font-semibold">
          <Shield className="h-6 w-6 text-primary" />
          <span>Flow Sentinel</span>
        </Link>
      </div>

      <div className="flex-1 overflow-auto py-2">
        <nav className="grid gap-1 px-2">
          {sidebarItems.map((item) => (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent/50 transition-colors",
                location.pathname === item.href
                  ? "bg-accent text-accent-foreground"
                  : "text-muted-foreground"
              )}
            >
              <item.icon className="h-4 w-4" />
              {item.name}
            </Link>
          ))}
        </nav>
      </div>

      <div className="border-t border-border/40 p-4">
        <div className="flex items-center gap-2">
          <div className="h-2 w-2 rounded-full bg-security-safe animate-pulse-slow"></div>
          <span className="text-xs text-muted-foreground">System Active</span>
        </div>
      </div>
    </div>
  );
}

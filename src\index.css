
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode colors - Professional security theme */
    --background: 0 0% 98%;
    --foreground: 220 13% 18%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    --primary: 196 78% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 220 13% 18%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14% 96%;
    --accent-foreground: 220 13% 18%;

    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 196 78% 45%;

    --radius: 0.375rem;

    --sidebar-background: 220 14% 95%;
    --sidebar-foreground: 220 13% 18%;
    --sidebar-primary: 196 78% 45%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 14% 92%;
    --sidebar-accent-foreground: 220 13% 18%;
    --sidebar-border: 220 13% 87%;
    --sidebar-ring: 196 78% 45%;

    /* Security-themed colors for light mode */
    --security-safe: 142 69% 35%;
    --security-warning: 38 85% 48%;
    --security-danger: 0 72% 51%;
  }

  .dark {
    /* Dark mode colors - Enhanced security theme */
    --background: 220 20% 12%;
    --foreground: 210 40% 98%;

    --card: 222 25% 16%;
    --card-foreground: 210 40% 98%;

    --popover: 222 25% 16%;
    --popover-foreground: 210 40% 98%;

    --primary: 196 80% 50%;
    --primary-foreground: 220 20% 12%;

    --secondary: 220 30% 20%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 30% 25%;
    --muted-foreground: 215 20% 65%;

    --accent: 196 70% 40%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 70% 55%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 15% 25%;
    --input: 217 20% 30%;
    --ring: 196 80% 50%;

    --sidebar-background: 222 35% 10%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 196 80% 50%;
    --sidebar-primary-foreground: 220 20% 12%;
    --sidebar-accent: 220 20% 16%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 222 25% 20%;
    --sidebar-ring: 196 80% 50%;

    /* Security-themed colors for dark mode */
    --security-safe: 142 76% 40%;
    --security-warning: 38 92% 55%;
    --security-danger: 0 70% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }
}

@layer utilities {
  .grid-flow-col-dense {
    grid-auto-flow: column dense;
  }

  .glass-panel {
    @apply backdrop-blur-sm border bg-white/60 border-black/10;
  }

  .dark .glass-panel {
    @apply bg-black/30 border-white/10;
  }

  .pulse-dot {
    @apply relative;
  }

  .pulse-dot::after {
    content: '';
    @apply absolute w-full h-full rounded-full animate-ping-slow opacity-75;
  }

  /* Enhanced light mode shadows */
  .light-shadow {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .light-shadow-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }
}

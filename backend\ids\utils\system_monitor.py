"""
System resource monitoring utilities for the IDS application.
Provides functions to collect CPU, RAM, GPU, disk, and network usage statistics.
Optimized for Linux systems with direct /proc filesystem access.
"""

import psutil
import time
import platform
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

try:
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    logger.warning("GPUtil not available. GPU monitoring will be disabled.")

# Check if we're running on Linux for enhanced monitoring
IS_LINUX = platform.system().lower() == 'linux'
if IS_LINUX:
    logger.info("Linux detected - enabling enhanced CPU monitoring with /proc filesystem access")


class LinuxCPUMonitor:
    """
    Linux-specific CPU monitoring using direct /proc filesystem access.
    Provides more accurate CPU usage readings than generic cross-platform methods.
    """

    def __init__(self):
        self.last_cpu_times = None
        self.last_cpu_times_per_core = None
        self.last_measurement_time = None

    def _read_proc_stat(self) -> Dict:
        """Read CPU statistics from /proc/stat."""
        try:
            with open('/proc/stat', 'r') as f:
                lines = f.readlines()

            cpu_data = {}
            for line in lines:
                if line.startswith('cpu'):
                    parts = line.strip().split()
                    cpu_name = parts[0]
                    # CPU times: user, nice, system, idle, iowait, irq, softirq, steal, guest, guest_nice
                    times = [int(x) for x in parts[1:]]

                    # Ensure we have at least 7 values (some older kernels may have fewer)
                    while len(times) < 10:
                        times.append(0)

                    cpu_data[cpu_name] = {
                        'user': times[0],
                        'nice': times[1],
                        'system': times[2],
                        'idle': times[3],
                        'iowait': times[4],
                        'irq': times[5],
                        'softirq': times[6],
                        'steal': times[7] if len(times) > 7 else 0,
                        'guest': times[8] if len(times) > 8 else 0,
                        'guest_nice': times[9] if len(times) > 9 else 0
                    }

            return cpu_data
        except Exception as e:
            logger.error(f"Error reading /proc/stat: {e}")
            return {}

    def _read_proc_loadavg(self) -> Dict:
        """Read load averages from /proc/loadavg."""
        try:
            with open('/proc/loadavg', 'r') as f:
                line = f.read().strip()

            parts = line.split()
            return {
                'load_1min': float(parts[0]),
                'load_5min': float(parts[1]),
                'load_15min': float(parts[2]),
                'running_processes': int(parts[3].split('/')[0]),
                'total_processes': int(parts[3].split('/')[1]),
                'last_pid': int(parts[4])
            }
        except Exception as e:
            logger.error(f"Error reading /proc/loadavg: {e}")
            return {}

    def _calculate_cpu_usage(self, current_times: Dict, previous_times: Dict) -> float:
        """Calculate CPU usage percentage from CPU time differences."""
        if not previous_times:
            return 0.0

        try:
            # Calculate total time differences
            current_total = sum(current_times.values())
            previous_total = sum(previous_times.values())
            total_diff = current_total - previous_total

            if total_diff <= 0:
                return 0.0

            # Calculate idle time difference
            idle_diff = current_times['idle'] - previous_times['idle']

            # CPU usage = (total_time - idle_time) / total_time * 100
            usage = ((total_diff - idle_diff) / total_diff) * 100
            return max(0.0, min(100.0, usage))  # Clamp between 0 and 100

        except Exception as e:
            logger.error(f"Error calculating CPU usage: {e}")
            return 0.0

    def get_enhanced_cpu_info(self) -> Dict:
        """Get enhanced CPU information using Linux /proc filesystem."""
        try:
            current_time = time.time()
            cpu_data = self._read_proc_stat()
            load_data = self._read_proc_loadavg()

            if not cpu_data or 'cpu' not in cpu_data:
                logger.warning("Failed to read CPU data from /proc/stat")
                return {}

            current_cpu_times = cpu_data['cpu']

            # Calculate overall CPU usage
            cpu_usage = 0.0
            if self.last_cpu_times and self.last_measurement_time:
                time_diff = current_time - self.last_measurement_time
                # Only calculate if enough time has passed (at least 0.1 seconds)
                if time_diff >= 0.1:
                    cpu_usage = self._calculate_cpu_usage(current_cpu_times, self.last_cpu_times)

            # Calculate per-core CPU usage
            per_core_usage = []
            if self.last_cpu_times_per_core:
                for i in range(len([k for k in cpu_data.keys() if k.startswith('cpu') and k != 'cpu'])):
                    cpu_core_key = f'cpu{i}'
                    if cpu_core_key in cpu_data and cpu_core_key in self.last_cpu_times_per_core:
                        core_usage = self._calculate_cpu_usage(
                            cpu_data[cpu_core_key],
                            self.last_cpu_times_per_core[cpu_core_key]
                        )
                        per_core_usage.append(round(core_usage, 2))

            # Store current times for next calculation
            self.last_cpu_times = current_cpu_times.copy()
            self.last_cpu_times_per_core = {k: v.copy() for k, v in cpu_data.items() if k.startswith('cpu')}
            self.last_measurement_time = current_time

            # Get additional CPU info using psutil for compatibility
            cpu_count = psutil.cpu_count()
            cpu_count_logical = psutil.cpu_count(logical=True)
            cpu_freq = psutil.cpu_freq()

            # Calculate additional metrics from CPU times
            total_time = sum(current_cpu_times.values())
            if total_time > 0:
                iowait_percent = (current_cpu_times['iowait'] / total_time) * 100
                system_percent = (current_cpu_times['system'] / total_time) * 100
                user_percent = (current_cpu_times['user'] / total_time) * 100
            else:
                iowait_percent = system_percent = user_percent = 0.0

            result = {
                'usage_percent': round(cpu_usage, 2),
                'cores_physical': cpu_count,
                'cores_logical': cpu_count_logical,
                'frequency_current': round(cpu_freq.current, 2) if cpu_freq else None,
                'frequency_max': round(cpu_freq.max, 2) if cpu_freq else None,
                'per_core_usage': per_core_usage,
                'load_average': [load_data.get('load_1min', 0), load_data.get('load_5min', 0), load_data.get('load_15min', 0)] if load_data else None,
                # Enhanced Linux-specific metrics
                'iowait_percent': round(iowait_percent, 2),
                'system_percent': round(system_percent, 2),
                'user_percent': round(user_percent, 2),
                'running_processes': load_data.get('running_processes', 0) if load_data else 0,
                'total_processes': load_data.get('total_processes', 0) if load_data else 0,
                'monitoring_method': 'linux_proc'
            }

            return result

        except Exception as e:
            logger.error(f"Error in enhanced CPU monitoring: {e}")
            return {}


class SystemResourceMonitor:
    """Monitor system resources including CPU, RAM, GPU, disk, and network."""

    def __init__(self):
        self.start_time = time.time()
        self.network_stats_history = []
        self.last_network_stats = None

        # Cache for expensive operations
        self._cache = {}
        self._cache_timeout = 0.5  # Cache for 500ms

        # Static system info (doesn't change often)
        self._static_info_cache = None
        self._static_info_timestamp = 0

        # Initialize Linux-specific CPU monitor if available
        self.linux_cpu_monitor = LinuxCPUMonitor() if IS_LINUX else None
        if self.linux_cpu_monitor:
            logger.info("Initialized Linux-specific CPU monitoring for enhanced accuracy")

    def _get_cached_or_compute(self, cache_key: str, compute_func, timeout: float = None):
        """Get cached value or compute new one if cache expired."""
        current_time = time.time()
        cache_timeout = timeout or self._cache_timeout

        if (cache_key in self._cache and
            current_time - self._cache[cache_key]['timestamp'] < cache_timeout):
            return self._cache[cache_key]['data']

        # Compute new value
        data = compute_func()
        self._cache[cache_key] = {
            'data': data,
            'timestamp': current_time
        }
        return data

    def get_cpu_info(self) -> Dict:
        """Get CPU usage and information with Linux-specific enhancements when available."""
        try:
            # Try Linux-specific monitoring first for better accuracy
            if self.linux_cpu_monitor:
                enhanced_info = self.linux_cpu_monitor.get_enhanced_cpu_info()
                if enhanced_info:
                    logger.debug(f"Using Linux-specific CPU monitoring: {enhanced_info.get('usage_percent', 0)}%")
                    return enhanced_info
                else:
                    logger.warning("Linux-specific CPU monitoring failed, falling back to psutil")

            # Fallback to psutil for cross-platform compatibility
            logger.debug("Using psutil for CPU monitoring")

            # Always use a short blocking measurement to ensure accurate data
            # This is more reliable than non-blocking which can return 0.0
            cpu_percent = psutil.cpu_percent(interval=0.1)

            cpu_count = psutil.cpu_count()
            cpu_count_logical = psutil.cpu_count(logical=True)
            cpu_freq = psutil.cpu_freq()

            # Get per-core CPU usage with blocking measurement
            cpu_per_core = psutil.cpu_percent(interval=0.1, percpu=True)

            return {
                'usage_percent': round(cpu_percent, 2),
                'cores_physical': cpu_count,
                'cores_logical': cpu_count_logical,
                'frequency_current': round(cpu_freq.current, 2) if cpu_freq else None,
                'frequency_max': round(cpu_freq.max, 2) if cpu_freq else None,
                'per_core_usage': [round(usage, 2) for usage in cpu_per_core],
                'load_average': list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else None,
                'monitoring_method': 'psutil_fallback'
            }
        except Exception as e:
            logger.error(f"Error getting CPU info: {e}")
            return {'usage_percent': 0, 'cores_physical': 0, 'cores_logical': 0, 'monitoring_method': 'error'}
    
    def get_memory_info(self) -> Dict:
        """Get memory usage information."""
        try:
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            return {
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'used_gb': round(memory.used / (1024**3), 2),
                'usage_percent': round(memory.percent, 2),
                'free_gb': round(memory.free / (1024**3), 2),
                'swap_total_gb': round(swap.total / (1024**3), 2),
                'swap_used_gb': round(swap.used / (1024**3), 2),
                'swap_usage_percent': round(swap.percent, 2)
            }
        except Exception as e:
            logger.error(f"Error getting memory info: {e}")
            return {'total_gb': 0, 'available_gb': 0, 'used_gb': 0, 'usage_percent': 0}
    
    def get_gpu_info(self) -> List[Dict]:
        """Get GPU usage information."""
        if not GPU_AVAILABLE:
            return []
        
        try:
            gpus = GPUtil.getGPUs()
            gpu_info = []
            
            for gpu in gpus:
                gpu_info.append({
                    'id': gpu.id,
                    'name': gpu.name,
                    'usage_percent': round(gpu.load * 100, 2),
                    'memory_total_mb': round(gpu.memoryTotal, 2),
                    'memory_used_mb': round(gpu.memoryUsed, 2),
                    'memory_free_mb': round(gpu.memoryFree, 2),
                    'memory_usage_percent': round((gpu.memoryUsed / gpu.memoryTotal) * 100, 2),
                    'temperature': round(gpu.temperature, 2)
                })
            
            return gpu_info
        except Exception as e:
            logger.error(f"Error getting GPU info: {e}")
            return []
    
    def get_disk_info(self) -> List[Dict]:
        """Get disk usage information."""
        try:
            disk_info = []
            partitions = psutil.disk_partitions()
            
            for partition in partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info.append({
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'filesystem': partition.fstype,
                        'total_gb': round(usage.total / (1024**3), 2),
                        'used_gb': round(usage.used / (1024**3), 2),
                        'free_gb': round(usage.free / (1024**3), 2),
                        'usage_percent': round((usage.used / usage.total) * 100, 2)
                    })
                except PermissionError:
                    # Skip partitions we can't access
                    continue
            
            return disk_info
        except Exception as e:
            logger.error(f"Error getting disk info: {e}")
            return []
    
    def get_network_info(self) -> Dict:
        """Get network usage information."""
        try:
            network_io = psutil.net_io_counters()
            # Skip expensive network connections count for performance
            # network_connections = len(psutil.net_connections())
            network_connections = 0  # Placeholder to avoid slow operation

            current_stats = {
                'bytes_sent': network_io.bytes_sent,
                'bytes_recv': network_io.bytes_recv,
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv,
                'timestamp': time.time()
            }
            
            # Calculate rates if we have previous stats
            rates = {'bytes_sent_per_sec': 0, 'bytes_recv_per_sec': 0}
            if self.last_network_stats:
                time_diff = current_stats['timestamp'] - self.last_network_stats['timestamp']
                if time_diff > 0:
                    rates['bytes_sent_per_sec'] = round(
                        (current_stats['bytes_sent'] - self.last_network_stats['bytes_sent']) / time_diff, 2
                    )
                    rates['bytes_recv_per_sec'] = round(
                        (current_stats['bytes_recv'] - self.last_network_stats['bytes_recv']) / time_diff, 2
                    )
            
            self.last_network_stats = current_stats
            
            return {
                'bytes_sent_total': network_io.bytes_sent,
                'bytes_recv_total': network_io.bytes_recv,
                'bytes_sent_gb': round(network_io.bytes_sent / (1024**3), 2),
                'bytes_recv_gb': round(network_io.bytes_recv / (1024**3), 2),
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv,
                'connections_count': network_connections,
                **rates
            }
        except Exception as e:
            logger.error(f"Error getting network info: {e}")
            return {'bytes_sent_total': 0, 'bytes_recv_total': 0, 'connections_count': 0}
    
    def _get_linux_distro_info(self) -> Dict:
        """Get Linux distribution specific information."""
        distro_info = {}
        try:
            # Try to read /etc/os-release for detailed Linux distribution info
            if os.path.exists('/etc/os-release'):
                with open('/etc/os-release', 'r') as f:
                    for line in f:
                        if '=' in line:
                            key, value = line.strip().split('=', 1)
                            # Remove quotes from values
                            value = value.strip('"\'')
                            distro_info[key.lower()] = value

            # Get kernel information
            if os.path.exists('/proc/version'):
                with open('/proc/version', 'r') as f:
                    kernel_info = f.read().strip()
                    distro_info['kernel_version'] = kernel_info

            return distro_info
        except Exception as e:
            logger.error(f"Error reading Linux distribution info: {e}")
            return {}

    def get_system_info(self) -> Dict:
        """Get general system information with Linux-specific enhancements."""
        try:
            current_time = time.time()

            # Cache static info for 60 seconds (doesn't change often)
            if (self._static_info_cache is None or
                current_time - self._static_info_timestamp > 60):

                base_info = {
                    'platform': platform.platform(),
                    'system': platform.system(),
                    'processor': platform.processor(),
                    'architecture': platform.architecture()[0],
                    'hostname': platform.node(),
                    'python_version': platform.python_version(),
                    'machine': platform.machine(),
                    'release': platform.release(),
                    'version': platform.version()
                }

                # Add Linux-specific information if available
                if IS_LINUX:
                    linux_info = self._get_linux_distro_info()
                    if linux_info:
                        # Override generic info with specific Linux distribution details
                        if 'pretty_name' in linux_info:
                            base_info['platform'] = linux_info['pretty_name']
                        if 'name' in linux_info:
                            base_info['distribution'] = linux_info['name']
                        if 'version_id' in linux_info:
                            base_info['distribution_version'] = linux_info['version_id']
                        if 'version_codename' in linux_info:
                            base_info['codename'] = linux_info['version_codename']
                        if 'kernel_version' in linux_info:
                            base_info['kernel_info'] = linux_info['kernel_version']

                        # Add Kali-specific information
                        if 'kali' in linux_info.get('id', '').lower():
                            base_info['is_kali'] = True
                            base_info['kali_version'] = linux_info.get('version', 'Unknown')
                            base_info['home_url'] = linux_info.get('home_url', '')

                self._static_info_cache = base_info
                self._static_info_timestamp = current_time

            # Only compute dynamic info (boot time and uptime)
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time

            return {
                **self._static_info_cache,
                'boot_time': boot_time.isoformat(),
                'uptime_seconds': int(uptime.total_seconds()),
                'uptime_formatted': str(uptime).split('.')[0],  # Remove microseconds
            }
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {'platform': 'Unknown', 'system': 'Unknown'}
    
    def get_process_info(self, limit: int = 10) -> List[Dict]:
        """Get information about top processes by CPU and memory usage with enhanced Kali Linux support."""
        try:
            # Get total system memory for calculations
            total_memory_mb = psutil.virtual_memory().total / (1024 * 1024)

            # First, warm up CPU percent counters for accurate readings
            logger.debug("Warming up CPU percent counters for accurate process monitoring")
            for proc in psutil.process_iter():
                try:
                    proc.cpu_percent(None)  # Initialize CPU percent calculation
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass

            # Wait a short time for CPU calculation to stabilize
            time.sleep(0.1)

            # Collect all process data with enhanced information
            all_processes = []

            # Get process information with detailed attributes
            for proc in psutil.process_iter(['pid', 'username', 'name', 'status', 'cmdline']):
                try:
                    # Get CPU and memory percentages (now with proper timing)
                    cpu_percent = proc.cpu_percent(None) or 0
                    memory_percent = proc.memory_percent() or 0

                    # Get process info from the iterator
                    proc_info = proc.info
                    pid = proc_info.get('pid', 0)
                    name = proc_info.get('name', 'Unknown')
                    username = proc_info.get('username', 'Unknown')
                    status = proc_info.get('status', 'Unknown')
                    cmdline = proc_info.get('cmdline', [])

                    # Calculate memory in MB using multiple approaches
                    memory_mb = 0
                    try:
                        # Primary method: get RSS memory directly
                        memory_info = proc.memory_info()
                        if memory_info and hasattr(memory_info, 'rss'):
                            memory_mb = memory_info.rss / (1024 * 1024)  # Convert bytes to MB
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass

                    # Fallback: calculate from memory percentage if direct method failed
                    if memory_mb == 0 and memory_percent > 0:
                        memory_mb = (memory_percent / 100) * total_memory_mb

                    # Format command line (limit length for display)
                    cmdline_str = ' '.join(cmdline) if cmdline else ''
                    if len(cmdline_str) > 100:  # Limit command line length
                        cmdline_str = cmdline_str[:97] + '...'

                    # Include processes with any resource usage (lower threshold for better visibility)
                    # Also include system processes that might be important for Kali Linux monitoring
                    if (cpu_percent > 0.01 or memory_percent > 0.01 or memory_mb > 1 or
                        any(kali_proc in name.lower() for kali_proc in ['kali', 'metasploit', 'nmap', 'wireshark', 'aircrack'])):

                        all_processes.append({
                            'pid': pid,
                            'name': name,
                            'username': username,
                            'status': status,
                            'cmdline': cmdline_str,
                            'cpu_percent': round(cpu_percent, 2),
                            'memory_percent': round(memory_percent, 2),
                            'memory_mb': round(memory_mb, 1)
                        })

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    # Skip processes we can't access or that no longer exist
                    continue
                except Exception as e:
                    # Log individual process errors but continue
                    logger.debug(f"Error getting info for process: {e}")
                    continue

            # Sort processes by CPU usage (highest first), then by memory usage
            if all_processes:
                all_processes.sort(key=lambda x: (x['cpu_percent'], x['memory_percent']), reverse=True)

                # Return top processes up to the limit
                top_processes = all_processes[:limit]

                logger.debug(f"Successfully collected {len(top_processes)} top processes out of {len(all_processes)} total")
                return top_processes

            logger.warning("No processes found with measurable resource usage")
            return []

        except Exception as e:
            logger.error(f"Error getting process info: {e}")
            return []
    
    def get_all_resources(self) -> Dict:
        """Get all system resource information in one call."""
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu': self.get_cpu_info(),
            'memory': self.get_memory_info(),
            'gpu': self.get_gpu_info(),
            'disk': self.get_disk_info(),
            'network': self.get_network_info(),
            'system': self.get_system_info(),
            'top_processes': self.get_process_info(limit=15)  # Get more processes for better visibility
        }


# Global instance for reuse with thread safety
_monitor_instance = None
_monitor_lock = None

def get_system_monitor() -> SystemResourceMonitor:
    """Get a singleton instance of the system resource monitor."""
    global _monitor_instance, _monitor_lock

    if _monitor_lock is None:
        import threading
        _monitor_lock = threading.Lock()

    if _monitor_instance is None:
        with _monitor_lock:
            if _monitor_instance is None:  # Double-check locking
                _monitor_instance = SystemResourceMonitor()
    return _monitor_instance

/**
 * Service for fetching system resource data from the backend API
 */

export interface SystemResourceData {
  timestamp: string;
  cpu: {
    usage_percent: number;
    cores_physical: number;
    cores_logical: number;
    frequency_current?: number;
    frequency_max?: number;
    per_core_usage: number[];
    load_average?: number[];
    // Linux-specific enhanced metrics
    iowait_percent?: number;
    system_percent?: number;
    user_percent?: number;
    running_processes?: number;
    total_processes?: number;
    monitoring_method?: string;
  };
  memory: {
    total_gb: number;
    available_gb: number;
    used_gb: number;
    usage_percent: number;
    free_gb: number;
    swap_total_gb: number;
    swap_used_gb: number;
    swap_usage_percent: number;
  };
  gpu: Array<{
    id: number;
    name: string;
    usage_percent: number;
    memory_total_mb: number;
    memory_used_mb: number;
    memory_free_mb: number;
    memory_usage_percent: number;
    temperature: number;
  }>;
  disk: Array<{
    device: string;
    mountpoint: string;
    filesystem: string;
    total_gb: number;
    used_gb: number;
    free_gb: number;
    usage_percent: number;
  }>;
  network: {
    bytes_sent_total: number;
    bytes_recv_total: number;
    bytes_sent_gb: number;
    bytes_recv_gb: number;
    packets_sent: number;
    packets_recv: number;
    connections_count: number;
    bytes_sent_per_sec: number;
    bytes_recv_per_sec: number;
  };
  system: {
    platform: string;
    system: string;
    processor: string;
    architecture: string;
    hostname: string;
    boot_time: string;
    uptime_seconds: number;
    uptime_formatted: string;
    python_version: string;
    // Enhanced Linux/Kali-specific fields
    machine?: string;
    release?: string;
    version?: string;
    distribution?: string;
    distribution_version?: string;
    codename?: string;
    kernel_info?: string;
    is_kali?: boolean;
    kali_version?: string;
    home_url?: string;
  };
  top_processes: Array<{
    pid: number;
    name: string;
    cpu_percent: number;
    memory_percent: number;
    memory_mb: number;
  }>;
  alerts: Array<{
    type: string;
    level: string;
    message: string;
  }>;
}

export interface SystemResourceHistoryData {
  timeframe: string;
  data_points: Array<{
    timestamp: string;
    cpu_usage: number;
    memory_usage: number;
    network_in_bytes: number;
    network_out_bytes: number;
    disk_usage: number;
  }>;
  total_points: number;
}

// Cache for last known good data
let lastKnownData: SystemResourceData | null = null;
let consecutiveFailures = 0;

// Request deduplication to prevent multiple simultaneous calls
let activeRequest: Promise<SystemResourceData> | null = null;
let lastRequestTime = 0;

/**
 * Clear cached system resource data to force fresh requests
 */
export function clearSystemResourceCache(): void {
  lastKnownData = null;
  lastRequestTime = 0;
  consecutiveFailures = 0;
  activeRequest = null;
  console.log('System resource cache cleared - next request will fetch fresh data');
}

/**
 * Fetch current system resource data with timeout, performance monitoring, and fallback
 */
export async function fetchSystemResources(): Promise<SystemResourceData> {
  const now = Date.now();

  // Request deduplication: if there's an active request or recent request, reuse it
  if (activeRequest) {
    console.log('Reusing active request to prevent overlap');
    return activeRequest;
  }

  // If last request was very recent (< 500ms), return cached data if available
  // BUT only if the cached data has valid CPU usage (not 0) to avoid stale cache issues
  if (now - lastRequestTime < 500 && lastKnownData && lastKnownData.cpu.usage_percent > 0) {
    console.log('Using recent cached data to prevent excessive requests');
    return {
      ...lastKnownData,
      timestamp: new Date().toISOString()
    };
  }

  // Create new request
  activeRequest = performActualRequest();
  lastRequestTime = now;

  try {
    const result = await activeRequest;
    return result;
  } finally {
    activeRequest = null; // Clear active request when done
  }
}

/**
 * Perform the actual API request
 */
async function performActualRequest(): Promise<SystemResourceData> {
  try {
    // Add timeout to prevent hanging (Task Manager style - fast response)
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 1500); // Increased to 1.5s for slow backend

    // Use unique timer names to avoid conflicts
    const timerName = `fetchSystemResources_${Date.now()}`;
    console.time(timerName);

    // Add cache-busting parameter to ensure fresh data
    const cacheBuster = Date.now();
    const response = await fetch(`http://localhost:8000/api/ids/system/resources?t=${cacheBuster}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    console.timeEnd(timerName);

    if (!response.ok) {
      throw new Error(`Failed to fetch system resources: ${response.statusText}`);
    }

    const data = await response.json();

    // Ensure all required fields exist with fallbacks (Task Manager style)
    const processedData = {
      timestamp: data.timestamp || new Date().toISOString(),
      cpu: {
        usage_percent: data.cpu?.usage_percent ?? 0,
        cores_physical: data.cpu?.cores_physical ?? 1,
        cores_logical: data.cpu?.cores_logical ?? 1,
        frequency_current: data.cpu?.frequency_current ?? 0,
        frequency_max: data.cpu?.frequency_max ?? 0,
        per_core_usage: data.cpu?.per_core_usage ?? [],
        load_average: data.cpu?.load_average ?? []
      },
      memory: {
        total_gb: data.memory?.total_gb ?? 0,
        available_gb: data.memory?.available_gb ?? 0,
        used_gb: data.memory?.used_gb ?? 0,
        usage_percent: data.memory?.usage_percent ?? 0,
        free_gb: data.memory?.free_gb ?? 0,
        swap_total_gb: data.memory?.swap_total_gb ?? 0,
        swap_used_gb: data.memory?.swap_used_gb ?? 0,
        swap_usage_percent: data.memory?.swap_usage_percent ?? 0
      },
      network: {
        bytes_sent_total: data.network?.bytes_sent_total ?? 0,
        bytes_recv_total: data.network?.bytes_recv_total ?? 0,
        bytes_sent_gb: data.network?.bytes_sent_gb ?? 0,
        bytes_recv_gb: data.network?.bytes_recv_gb ?? 0,
        packets_sent: data.network?.packets_sent ?? 0,
        packets_recv: data.network?.packets_recv ?? 0,
        connections_count: data.network?.connections_count ?? 0,
        bytes_sent_per_sec: data.network?.bytes_sent_per_sec ?? 0,
        bytes_recv_per_sec: data.network?.bytes_recv_per_sec ?? 0
      },
      disk: data.disk?.length > 0 ? data.disk : [{
        device: 'Unknown',
        mountpoint: 'Unknown',
        filesystem: 'Unknown',
        total_gb: 0,
        used_gb: 0,
        free_gb: 0,
        usage_percent: 0
      }],
      gpu: data.gpu ?? [],
      system: data.system ?? {},
      top_processes: data.top_processes ?? [],
      alerts: data.alerts ?? []
    };

    // Cache successful response
    lastKnownData = processedData;
    consecutiveFailures = 0;

    return processedData;

  } catch (error) {
    consecutiveFailures++;
    console.error(`Error in fetchSystemResources (failure #${consecutiveFailures}):`, error);

    // Determine error type for better handling
    let errorType = 'unknown';
    let errorMessage = 'Unknown error occurred';

    if (error.name === 'AbortError') {
      errorType = 'timeout';
      errorMessage = 'API request timed out - using cached data';
      // Only log timeout on first occurrence to reduce console spam
      if (consecutiveFailures === 1) {
        console.warn('API timeout detected - backend is slow, using cached data for smooth experience');
      }
    } else if (error.message?.includes('Failed to fetch') || error.message?.includes('ERR_CONNECTION_REFUSED')) {
      errorType = 'connection';
      errorMessage = 'Cannot connect to backend server - check if it\'s running on port 8000';
      console.error(errorMessage);
    } else if (error.message?.includes('NetworkError')) {
      errorType = 'network';
      errorMessage = 'Network error - check your internet connection';
      console.error(errorMessage);
    }

    // Return cached data if available and failures are recent
    if (lastKnownData && consecutiveFailures <= 5) {
      // Only log on first failure to reduce console spam
      if (consecutiveFailures === 1) {
        console.info('Seamlessly switching to cached data for uninterrupted monitoring');
      }

      // Create a copy of the data with filtered alerts for user display
      const userAlerts = (lastKnownData.alerts || []).filter(alert => {
        // Only show user-relevant alerts, not internal API status
        const message = alert.message?.toLowerCase() || '';
        return alert.type !== 'system' ||
               (!message.includes('api') &&
                !message.includes('timeout') &&
                !message.includes('cached') &&
                !message.includes('failure'));
      });

      return {
        ...lastKnownData,
        timestamp: new Date().toISOString(), // Update timestamp
        alerts: userAlerts, // Only show user-relevant alerts
        _internal_status: {
          using_cached_data: true,
          error_type: errorType,
          consecutive_failures: consecutiveFailures,
          last_error: errorMessage
        }
      };
    }

    // If no cached data or too many failures, return minimal fallback
    if (consecutiveFailures === 1) {
      console.warn('System monitoring API unavailable - using fallback data');
    }
    return createFallbackData();
  }
}

/**
 * Create minimal fallback data when API is completely unavailable
 */
function createFallbackData(): SystemResourceData {
  return {
    timestamp: new Date().toISOString(),
    cpu: {
      usage_percent: 0,
      cores_physical: 1,
      cores_logical: 1,
      frequency_current: 0,
      frequency_max: 0,
      per_core_usage: [],
      load_average: []
    },
    memory: {
      total_gb: 0,
      available_gb: 0,
      used_gb: 0,
      usage_percent: 0,
      free_gb: 0,
      swap_total_gb: 0,
      swap_used_gb: 0,
      swap_usage_percent: 0
    },
    network: {
      bytes_sent_total: 0,
      bytes_recv_total: 0,
      bytes_sent_gb: 0,
      bytes_recv_gb: 0,
      packets_sent: 0,
      packets_recv: 0,
      connections_count: 0,
      bytes_sent_per_sec: 0,
      bytes_recv_per_sec: 0
    },
    disk: [{
      device: 'Unavailable',
      mountpoint: 'Unavailable',
      filesystem: 'Unknown',
      total_gb: 0,
      used_gb: 0,
      free_gb: 0,
      usage_percent: 0
    }],
    gpu: [],
    system: {
      platform: 'Unknown',
      system: 'Unknown',
      processor: 'Unknown',
      architecture: 'Unknown',
      hostname: 'Unknown',
      boot_time: new Date().toISOString(),
      uptime_seconds: 0,
      uptime_formatted: '0:00:00',
      python_version: 'Unknown'
    },
    top_processes: [],
    alerts: [] // No user-visible alerts for API issues
  };
}

/**
 * Fetch historical system resource data
 */
export async function fetchSystemResourceHistory(timeframe: string = '1h'): Promise<SystemResourceHistoryData> {
  try {
    const response = await fetch(`http://localhost:8000/api/ids/system/history?timeframe=${timeframe}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch system resource history: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error in fetchSystemResourceHistory:', error);
    throw error;
  }
}

/**
 * Format bytes to human readable format
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format uptime seconds to human readable format
 */
export function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

/**
 * Get color based on usage percentage
 */
export function getUsageColor(percentage: number): string {
  if (percentage >= 90) return 'text-red-500';
  if (percentage >= 75) return 'text-orange-500';
  if (percentage >= 50) return 'text-yellow-500';
  return 'text-green-500';
}

/**
 * Get background color based on usage percentage
 */
export function getUsageBackgroundColor(percentage: number): string {
  if (percentage >= 90) return 'bg-red-500';
  if (percentage >= 75) return 'bg-orange-500';
  if (percentage >= 50) return 'bg-yellow-500';
  return 'bg-green-500';
}

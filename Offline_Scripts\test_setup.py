#!/usr/bin/env python3
"""
Test script to verify that all model files and dependencies are properly set up.
"""

import os
import sys
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dependencies():
    """Test if all required Python packages are available."""
    logger.info("Testing Python dependencies...")
    
    try:
        import scapy
        logger.info(f"✓ Scapy version: {scapy.__version__}")
    except ImportError as e:
        logger.error(f"✗ Scapy not found: {e}")
        return False
    
    try:
        import pandas as pd
        logger.info(f"✓ Pandas version: {pd.__version__}")
    except ImportError as e:
        logger.error(f"✗ Pandas not found: {e}")
        return False
    
    try:
        import numpy as np
        logger.info(f"✓ NumPy version: {np.__version__}")
    except ImportError as e:
        logger.error(f"✗ NumPy not found: {e}")
        return False
    
    try:
        import sklearn
        logger.info(f"✓ Scikit-learn version: {sklearn.__version__}")
    except ImportError as e:
        logger.error(f"✗ Scikit-learn not found: {e}")
        return False
    
    try:
        import joblib
        logger.info(f"✓ Joblib version: {joblib.__version__}")
    except ImportError as e:
        logger.error(f"✗ Joblib not found: {e}")
        return False
    
    return True

def test_model_files():
    """Test if all required model files are present."""
    logger.info("Testing model files...")
    
    required_files = [
        'DecisionTree_model.pkl',
        'label_array.npy',
        'feature_columns.txt',
        'scaler.pkl'
    ]
    
    all_present = True
    for file_name in required_files:
        if os.path.exists(file_name):
            file_size = os.path.getsize(file_name)
            logger.info(f"✓ {file_name} ({file_size} bytes)")
        else:
            logger.error(f"✗ {file_name} not found")
            all_present = False
    
    return all_present

def test_model_loading():
    """Test if the model files can be loaded successfully."""
    logger.info("Testing model loading...")
    
    try:
        import joblib
        import numpy as np
        
        # Test model loading
        model = joblib.load('DecisionTree_model.pkl')
        logger.info(f"✓ Model loaded successfully (type: {type(model).__name__})")
        
        # Test label array loading
        labels = np.load('label_array.npy', allow_pickle=True)
        logger.info(f"✓ Label array loaded ({len(labels)} classes)")
        
        # Test feature columns loading
        with open('feature_columns.txt', 'r') as f:
            features = [line.strip() for line in f.readlines()]
        logger.info(f"✓ Feature columns loaded ({len(features)} features)")
        
        # Test scaler loading
        scaler = joblib.load('scaler.pkl')
        logger.info(f"✓ Scaler loaded (type: {type(scaler).__name__})")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Error loading model components: {e}")
        return False

def test_scripts():
    """Test if the main scripts can be imported."""
    logger.info("Testing script imports...")
    
    try:
        # Test flow extractor import
        sys.path.insert(0, '.')
        from flow_extractor import OfflineFlowExtractor
        logger.info("✓ Flow extractor can be imported")
        
        # Test flow evaluator import
        from flow_evaluator import OfflineFlowEvaluator
        logger.info("✓ Flow evaluator can be imported")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Error importing scripts: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("=== Offline IDS Setup Test ===")
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Model Files", test_model_files),
        ("Model Loading", test_model_loading),
        ("Script Imports", test_scripts)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        if not test_func():
            all_passed = False
    
    logger.info(f"\n=== Test Results ===")
    if all_passed:
        logger.info("✓ All tests passed! The offline IDS setup is ready to use.")
        logger.info("\nNext steps:")
        logger.info("1. Run flow extraction: sudo python flow_extractor.py -i wlan0 -d 60 -o flows.csv")
        logger.info("2. Run flow evaluation: python flow_evaluator.py -i flows.csv -o results.csv")
    else:
        logger.error("✗ Some tests failed. Please check the errors above and fix them.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

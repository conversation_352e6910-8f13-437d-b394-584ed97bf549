#!/usr/bin/env python3
"""
Offline Flow Evaluator Script

This script loads network flows from a CSV file and evaluates them using the trained model.
It replicates the exact model evaluation methods used by the main IDS system.

Usage:
    python flow_evaluator.py --input flows.csv --output results.csv
"""

import argparse
import sys
import os
import logging
from typing import Dict, Any, List
import pandas as pd
import numpy as np
import joblib

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineFlowEvaluator:
    """
    Offline flow evaluator that loads flows from CSV and evaluates them using the trained model.
    Based on the RealTimeIDS prediction logic.
    """
    
    def __init__(self, model_path="DecisionTree_model.pkl", 
                 label_array_path="label_array.npy",
                 feature_columns_path="feature_columns.txt",
                 scaler_path="scaler.pkl"):
        """
        Initialize the flow evaluator.
        
        Args:
            model_path: Path to the trained model file
            label_array_path: Path to the label array file
            feature_columns_path: Path to the feature columns file
            scaler_path: Path to the scaler file
        """
        self.model_path = model_path
        self.label_array_path = label_array_path
        self.feature_columns_path = feature_columns_path
        self.scaler_path = scaler_path
        
        # Load model components
        self.model = None
        self.label_array = None
        self.feature_columns = None
        self.scaler = None
        
        self._load_model_components()

    def _load_model_components(self):
        """Load the model, label array, feature columns, and scaler."""
        try:
            # Load the Decision Tree model
            self.model = joblib.load(self.model_path)
            logger.info(f"Loaded Decision Tree model from {self.model_path}")
        except Exception as e:
            raise RuntimeError(f"Error loading model: {str(e)}")
        
        try:
            # Load label array
            self.label_array = np.load(self.label_array_path, allow_pickle=True)
            logger.info(f"Loaded label array with {len(self.label_array)} classes")
        except Exception as e:
            raise RuntimeError(f"Error loading label array: {str(e)}")
        
        try:
            # Load feature columns
            with open(self.feature_columns_path, 'r') as f:
                self.feature_columns = [line.strip() for line in f.readlines()]
            logger.info(f"Loaded {len(self.feature_columns)} feature columns")
        except Exception as e:
            raise RuntimeError(f"Error loading feature columns: {str(e)}")
        
        try:
            # Load scaler (optional)
            if os.path.exists(self.scaler_path):
                self.scaler = joblib.load(self.scaler_path)
                logger.info(f"Loaded scaler from {self.scaler_path}")
            else:
                logger.warning(f"Scaler not found at {self.scaler_path}. Features will not be scaled!")
        except Exception as e:
            logger.error(f"Error loading scaler: {str(e)}")
            logger.warning("Will proceed without scaling, but model accuracy may be affected.")

    def load_flows_from_csv(self, csv_file):
        """
        Load flows from a CSV file.
        
        Args:
            csv_file: Path to the CSV file containing flows
            
        Returns:
            DataFrame with loaded flows
        """
        try:
            flows_df = pd.read_csv(csv_file)
            logger.info(f"Loaded {len(flows_df)} flows from {csv_file}")
            
            # Display basic information about the loaded data
            logger.info(f"CSV columns: {list(flows_df.columns)}")
            
            return flows_df
            
        except Exception as e:
            raise RuntimeError(f"Error loading CSV file: {str(e)}")

    def preprocess_flows(self, flows_df):
        """
        Enhanced preprocessing with better handling of attack patterns.

        Args:
            flows_df: DataFrame with raw flow data

        Returns:
            DataFrame ready for model prediction
        """
        # Create a copy for processing
        processed_df = flows_df.copy()

        # Enhanced feature handling with attack-aware defaults
        feature_defaults = {
            'Conn_Rate': 1,           # Minimum 1 connection
            'Fwd Seg Size Min': 40,   # Typical TCP header
            'Bwd Pkt Len Mean': 0.0,  # Can be zero for attacks
            'Dst Port': 80,           # Default HTTP port
            'TotLen Fwd Pkts': 40,    # Minimum packet size
            'Init Fwd Win Byts': 65535, # Default window size
            'Flow Duration': 1000,    # 1ms minimum
            'Flow IAT Mean': 1000.0,  # 1ms minimum
            'Init Bwd Win Byts': 0,   # Can be zero for attacks
            'Flow IAT Max': 1000.0    # 1ms minimum
        }

        # Ensure all required feature columns exist with intelligent defaults
        for col in self.feature_columns:
            if col not in processed_df.columns:
                default_val = feature_defaults.get(col, 0)
                processed_df[col] = default_val
                logger.warning(f"Missing feature column '{col}', filled with {default_val}")
            else:
                # Replace zero values in critical features with intelligent defaults
                if col in feature_defaults and col != 'Bwd Pkt Len Mean' and col != 'Init Bwd Win Byts':
                    zero_mask = processed_df[col] == 0
                    if zero_mask.any():
                        processed_df.loc[zero_mask, col] = feature_defaults[col]
                        logger.info(f"Replaced {zero_mask.sum()} zero values in '{col}' with {feature_defaults[col]}")

        # Create feature matrix in the exact order expected by the model
        feature_matrix = np.zeros((len(processed_df), len(self.feature_columns)))
        for i, col in enumerate(self.feature_columns):
            feature_matrix[:, i] = processed_df[col].values

        # Apply scaler if available
        if self.scaler:
            try:
                # Apply scaling to the feature matrix
                scaled_features = self.scaler.transform(feature_matrix)

                # Create new dataframe with scaled features
                model_df = pd.DataFrame()

                # Add network identifiers (not scaled)
                for col in ['Src IP', 'Dst IP', 'Src Port', 'Dst Port', 'Protocol']:
                    if col in processed_df.columns:
                        model_df[col] = processed_df[col]

                # Add scaled features in the correct order
                for i, col in enumerate(self.feature_columns):
                    model_df[col] = scaled_features[:, i]

                logger.info("Applied feature scaling successfully")

            except Exception as e:
                logger.error(f"Error applying scaler: {str(e)}")
                logger.warning("Using unscaled features may affect model prediction accuracy.")

                # Fall back to unscaled features
                model_df = processed_df.copy()
        else:
            # Use unscaled features
            model_df = processed_df.copy()
            logger.info("No scaler available, using unscaled features")

        return model_df

    def detect_ddos_patterns(self, flows_df, time_window=60):
        """
        Detect DDoS patterns by analyzing multiple sources attacking the same target.

        Args:
            flows_df: DataFrame with flows to analyze
            time_window: Time window in seconds to analyze for coordinated attacks

        Returns:
            Dictionary with DDoS analysis results
        """
        ddos_analysis = {
            'ddos_detected': False,
            'target_ips': {},
            'coordinated_attacks': [],
            'attack_summary': {}
        }

        if flows_df is None or len(flows_df) == 0:
            return ddos_analysis

        # Group flows by destination IP to find potential targets
        target_groups = flows_df.groupby('Dst IP')

        for target_ip, target_flows in target_groups:
            # Count unique source IPs attacking this target
            unique_sources = target_flows['Src IP'].nunique()
            total_flows = len(target_flows)

            # Check for malicious flows to this target - ONLY count ML-detected malicious sources
            if 'Prediction' in target_flows.columns:
                # Only count sources that were originally classified as malicious by ML model
                malicious_attack_types = ['DoS attack', 'DDoS attack', 'Malicious']  # All malicious classifications
                malicious_flows = target_flows[target_flows['Prediction'].isin(malicious_attack_types)]
                malicious_sources = malicious_flows['Src IP'].nunique() if len(malicious_flows) > 0 else 0

                logger.info(f"Target {target_ip}: {unique_sources} total sources, {malicious_sources} malicious sources")
            else:
                # If no predictions yet, can't determine malicious sources
                malicious_sources = 0
                malicious_flows = pd.DataFrame()

            # DDoS detection criteria - STRICT: Must have 3+ MALICIOUS source IPs
            ddos_threshold_sources = 3  # Minimum 3 different MALICIOUS source IPs (REQUIRED)
            ddos_threshold_flows = 50   # Minimum 50 flows total

            # Only classify as DDoS if there are 3+ sources classified as malicious by ML model
            if malicious_sources >= ddos_threshold_sources and total_flows >= ddos_threshold_flows:
                ddos_analysis['ddos_detected'] = True
                ddos_analysis['target_ips'][target_ip] = {
                    'unique_sources': unique_sources,
                    'total_flows': total_flows,
                    'malicious_sources': malicious_sources,
                    'attack_intensity': total_flows / unique_sources
                }

                # Analyze ONLY malicious source IP patterns
                malicious_source_ips = list(malicious_flows['Src IP'].unique())
                source_analysis = malicious_flows.groupby('Src IP').agg({
                    'Src Port': 'count',  # Number of connections per source
                    'Dst Port': lambda x: x.nunique(),  # Number of different target ports
                }).rename(columns={'Src Port': 'connection_count', 'Dst Port': 'target_ports'})

                coordinated_attack = {
                    'target_ip': target_ip,
                    'source_ips': malicious_source_ips,  # Only malicious sources
                    'attack_details': source_analysis.to_dict('index'),
                    'total_connections': len(malicious_flows),  # Only malicious flows
                    'attack_type': 'DDoS' if malicious_sources >= 3 else 'Coordinated'
                }

                ddos_analysis['coordinated_attacks'].append(coordinated_attack)

        # Generate attack summary
        if ddos_analysis['ddos_detected']:
            ddos_analysis['attack_summary'] = {
                'total_targets': len(ddos_analysis['target_ips']),
                'max_malicious_sources_per_target': max([info['malicious_sources'] for info in ddos_analysis['target_ips'].values()]),
                'total_coordinated_attacks': len(ddos_analysis['coordinated_attacks'])
            }

            logger.info(f"DDoS DETECTED! {ddos_analysis['attack_summary']}")

        return ddos_analysis

    def predict_flows(self, flows_df):
        """
        Enhanced prediction with DDoS correlation analysis.

        Args:
            flows_df: DataFrame with flows to evaluate

        Returns:
            DataFrame with prediction results including DDoS detection
        """
        if flows_df is None or len(flows_df) == 0:
            logger.warning("No flows to predict")
            return None

        # Log input data characteristics for debugging
        logger.info(f"Input flows shape: {flows_df.shape}")
        logger.info(f"Input columns: {list(flows_df.columns)}")

        # Preprocess flows
        processed_df = self.preprocess_flows(flows_df)

        # Extract features for prediction (only the model features)
        X = processed_df[self.feature_columns].values

        logger.info(f"Predicting on {len(X)} flows with {X.shape[1]} features")

        # Log feature statistics for debugging
        for i, col in enumerate(self.feature_columns):
            col_data = X[:, i]
            logger.info(f"Feature '{col}': min={col_data.min():.2f}, max={col_data.max():.2f}, mean={col_data.mean():.2f}")

        try:
            # Make predictions
            y_pred_idx = self.model.predict(X)

            # Get prediction probabilities for confidence analysis
            if hasattr(self.model, 'predict_proba'):
                y_pred_proba = self.model.predict_proba(X)
                max_proba = np.max(y_pred_proba, axis=1)
            else:
                max_proba = np.ones(len(y_pred_idx))  # Default confidence

            # Convert indices to labels
            y_pred_labels = [self.label_array[int(idx)] for idx in y_pred_idx]

            # Add binary threat classification
            threats = ['Malicious' if label != 'Benign' else 'Benign' for label in y_pred_labels]

            # Create results dataframe
            results_df = processed_df.copy()
            results_df['Prediction'] = y_pred_labels
            results_df['Threat'] = threats
            results_df['Confidence'] = max_proba

            # Add evaluation timestamp
            results_df['Evaluation_Timestamp'] = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # ENHANCED: Perform DDoS correlation analysis
            ddos_analysis = self.detect_ddos_patterns(results_df)

            # Update predictions based on DDoS analysis
            if ddos_analysis['ddos_detected']:
                logger.info("Applying DDoS correlation corrections...")

                for attack in ddos_analysis['coordinated_attacks']:
                    target_ip = attack['target_ip']
                    malicious_source_ips = attack['source_ips']  # These are only malicious sources now

                    # Find flows that are part of coordinated attack (only malicious sources)
                    coordinated_mask = (
                        (results_df['Dst IP'] == target_ip) &
                        (results_df['Src IP'].isin(malicious_source_ips)) &
                        (results_df['Prediction'].isin(['DoS attack', 'DDoS attack', 'Malicious']))  # Only malicious flows
                    )

                    # Update predictions for coordinated attacks
                    if coordinated_mask.sum() > 0:
                        # ONLY classify as DDoS if there are 3+ malicious source IPs
                        if len(malicious_source_ips) >= 3:
                            results_df.loc[coordinated_mask, 'Prediction'] = 'DDoS attack'
                            results_df.loc[coordinated_mask, 'Threat'] = 'Malicious'
                            results_df.loc[coordinated_mask, 'Confidence'] = 0.95  # High confidence for DDoS

                            logger.info(f"Updated {coordinated_mask.sum()} flows to 'DDoS attack' for target {target_ip} (from {len(malicious_source_ips)} malicious sources)")

                        # If only 1-2 malicious sources, keep as DoS (do not upgrade to DDoS)
                        else:
                            logger.info(f"Keeping {coordinated_mask.sum()} flows as DoS for target {target_ip} (only {len(malicious_source_ips)} malicious sources - need 3+ for DDoS)")

            # Calculate prediction statistics
            malicious_count = sum(1 for t in threats if t == 'Malicious')
            benign_count = len(threats) - malicious_count

            logger.info(f"Prediction completed: {malicious_count} malicious, {benign_count} benign flows")

            # Log detailed prediction breakdown
            prediction_counts = {}
            for label in results_df['Prediction']:
                prediction_counts[label] = prediction_counts.get(label, 0) + 1
            logger.info(f"Final predictions after DDoS analysis: {prediction_counts}")

            return results_df

        except Exception as e:
            logger.error(f"Error during prediction: {str(e)}")
            raise

    def save_results_to_csv(self, results_df, output_file):
        """
        Save prediction results to a CSV file with enhanced DDoS analysis.

        Args:
            results_df: DataFrame with prediction results
            output_file: Path to the output CSV file
        """
        try:
            results_df.to_csv(output_file, index=False)
            logger.info(f"Saved {len(results_df)} results to {output_file}")

            # Print summary statistics
            threat_counts = results_df['Threat'].value_counts()
            prediction_counts = results_df['Prediction'].value_counts()

            print(f"\n=== Flow Evaluation Summary ===")
            print(f"Total flows evaluated: {len(results_df)}")
            print(f"Threat classification: {threat_counts.to_dict()}")
            print(f"Detailed predictions: {prediction_counts.to_dict()}")
            print(f"Output file: {output_file}")

            # Enhanced DDoS Analysis Summary
            ddos_flows = results_df[results_df['Prediction'] == 'DDoS attack']
            dos_flows = results_df[results_df['Prediction'] == 'DoS attack']

            if len(ddos_flows) > 0:
                print(f"\n=== DDoS ATTACK DETECTED ===")
                print(f"DDoS flows: {len(ddos_flows)}")

                # Analyze DDoS patterns
                ddos_targets = ddos_flows.groupby('Dst IP').agg({
                    'Src IP': lambda x: x.nunique(),
                    'Src Port': 'count'
                }).rename(columns={'Src IP': 'unique_sources', 'Src Port': 'total_connections'})

                print("DDoS Targets Analysis:")
                for target_ip, stats in ddos_targets.iterrows():
                    print(f"  Target: {target_ip}")
                    print(f"    - Attacking sources: {stats['unique_sources']}")
                    print(f"    - Total connections: {stats['total_connections']}")

                    # Show attacking source IPs
                    attacking_sources = ddos_flows[ddos_flows['Dst IP'] == target_ip]['Src IP'].unique()
                    print(f"    - Source IPs: {', '.join(attacking_sources)}")

                print("\nSample DDoS flows:")
                display_cols = ['Src IP', 'Dst IP', 'Src Port', 'Dst Port', 'Prediction', 'Threat', 'Confidence']
                available_cols = [col for col in display_cols if col in ddos_flows.columns]
                print(ddos_flows[available_cols].head(10).to_string(index=False))

            elif len(dos_flows) > 0:
                print(f"\n=== DoS Attack Detected ===")
                print(f"DoS flows: {len(dos_flows)}")

                # Analyze DoS patterns
                dos_analysis = dos_flows.groupby(['Src IP', 'Dst IP']).size().reset_index(name='flow_count')
                print("DoS Attack Analysis:")
                for _, row in dos_analysis.iterrows():
                    print(f"  {row['Src IP']} → {row['Dst IP']}: {row['flow_count']} flows")

                print("Sample DoS flows:")
                display_cols = ['Src IP', 'Dst IP', 'Src Port', 'Dst Port', 'Prediction', 'Threat']
                available_cols = [col for col in display_cols if col in dos_flows.columns]
                print(dos_flows[available_cols].head(10).to_string(index=False))

            # Show other malicious flows if any
            other_malicious = results_df[
                (results_df['Threat'] == 'Malicious') &
                (~results_df['Prediction'].isin(['DoS attack', 'DDoS attack']))
            ]
            if len(other_malicious) > 0:
                print(f"\n=== Other Malicious Flows Detected ===")
                print(f"Count: {len(other_malicious)}")
                print("Sample flows:")
                display_cols = ['Src IP', 'Dst IP', 'Src Port', 'Dst Port', 'Prediction', 'Threat']
                available_cols = [col for col in display_cols if col in other_malicious.columns]
                print(other_malicious[available_cols].head(10).to_string(index=False))

        except Exception as e:
            logger.error(f"Error saving results: {str(e)}")
            raise

    def evaluate_flows_from_csv(self, input_csv, output_csv):
        """
        Complete evaluation pipeline: load flows from CSV, predict, and save results with DDoS analysis.

        Args:
            input_csv: Path to input CSV file with flows
            output_csv: Path to output CSV file for results
        """
        # Load flows
        flows_df = self.load_flows_from_csv(input_csv)

        # Perform initial DDoS pattern analysis
        logger.info("Performing initial DDoS pattern analysis...")
        initial_ddos_analysis = self.detect_ddos_patterns(flows_df)

        if initial_ddos_analysis['ddos_detected']:
            logger.warning("POTENTIAL DDoS PATTERNS DETECTED in input data!")
            for target_ip, stats in initial_ddos_analysis['target_ips'].items():
                logger.warning(f"Target {target_ip}: {stats['unique_sources']} sources, {stats['total_flows']} flows")

        # Predict threats with DDoS correlation
        results_df = self.predict_flows(flows_df)

        if results_df is not None:
            # Final DDoS analysis on results
            final_ddos_analysis = self.detect_ddos_patterns(results_df)

            # Log final DDoS analysis
            if final_ddos_analysis['ddos_detected']:
                logger.info("=== FINAL DDoS ANALYSIS ===")
                for attack in final_ddos_analysis['coordinated_attacks']:
                    logger.info(f"Coordinated attack on {attack['target_ip']}:")
                    logger.info(f"  - Attack type: {attack['attack_type']}")
                    logger.info(f"  - Source IPs: {attack['source_ips']}")
                    logger.info(f"  - Total connections: {attack['total_connections']}")

            # Save results
            self.save_results_to_csv(results_df, output_csv)
        else:
            logger.error("No results to save")


def main():
    """Main function for the offline flow evaluator."""
    parser = argparse.ArgumentParser(description='Offline Network Flow Evaluator')
    
    parser.add_argument('--input', '-i',
                       required=True,
                       help='Input CSV file with flows to evaluate')
    
    parser.add_argument('--output', '-o',
                       default='evaluation_results.csv',
                       help='Output CSV file for results (default: evaluation_results.csv)')
    
    parser.add_argument('--model', '-m',
                       default='DecisionTree_model.pkl',
                       help='Path to model file (default: DecisionTree_model.pkl)')
    
    parser.add_argument('--labels', '-l',
                       default='label_array.npy',
                       help='Path to label array file (default: label_array.npy)')
    
    parser.add_argument('--features', '-f',
                       default='feature_columns.txt',
                       help='Path to feature columns file (default: feature_columns.txt)')
    
    parser.add_argument('--scaler', '-s',
                       default='scaler.pkl',
                       help='Path to scaler file (default: scaler.pkl)')
    
    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Check if input file exists
    if not os.path.exists(args.input):
        logger.error(f"Input file not found: {args.input}")
        return 1
    
    try:
        # Create flow evaluator
        evaluator = OfflineFlowEvaluator(
            model_path=args.model,
            label_array_path=args.labels,
            feature_columns_path=args.features,
            scaler_path=args.scaler
        )
        
        # Evaluate flows
        evaluator.evaluate_flows_from_csv(args.input, args.output)
        
        return 0
        
    except Exception as e:
        logger.error(f"Error during flow evaluation: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())


import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { HelpCircle } from 'lucide-react';

export function SetupGuide() {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <HelpCircle className="h-4 w-4" />
          User Guide
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Flow Sentinel Guide</DialogTitle>
          <DialogDescription>
            Learn how to use the Flow Sentinel IDS dashboard.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[60vh] rounded border p-4 bg-muted/10">
          <div className="whitespace-pre-wrap">
            <h2 className="text-lg font-semibold mb-2">Getting Started</h2>
            <p className="mb-4">
              Welcome to Flow Sentinel IDS! This dashboard allows you to monitor and analyze network traffic for security threats.
            </p>
            
            <h3 className="text-md font-semibold mb-1">Key Features:</h3>
            <ul className="list-disc pl-5 mb-4">
              <li>Real-time traffic monitoring</li>
              <li>Intrusion detection alerts</li>
              <li>Network visualization</li>
              <li>Security analytics</li>
              <li>System configuration</li>
            </ul>
            
            <h3 className="text-md font-semibold mb-1">Dashboard Navigation:</h3>
            <ul className="list-disc pl-5 mb-4">
              <li><strong>Dashboard:</strong> Overview of system status and recent alerts</li>
              <li><strong>Alerts:</strong> Detailed view of security incidents</li>
              <li><strong>Traffic:</strong> Network traffic visualization and analysis</li>
              <li><strong>Network:</strong> Interactive network topology map</li>
              <li><strong>Analytics:</strong> Statistical analysis of security events</li>
              <li><strong>Settings:</strong> System configuration options</li>
            </ul>
            
            <p>For additional help, please refer to the documentation or contact support.</p>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

import { TrafficResponse } from "@/types/traffic";

// Request deduplication and caching
let activeRequest: Promise<TrafficResponse> | null = null;
let lastRequestTime = 0;
let lastKnownData: TrafficResponse | null = null;
let consecutiveFailures = 0;

/**
 * Fetch traffic data with timeout, performance monitoring, and fallback
 */
export async function fetchTrafficData(timeframe: string = '1h', timestamp?: number): Promise<TrafficResponse> {
  const now = Date.now();

  // Request deduplication: if there's an active request, reuse it
  if (activeRequest) {
    console.log('Reusing active traffic request to prevent overlap');
    return activeRequest;
  }

  // If last request was very recent (< 500ms), return cached data if available
  if (now - lastRequestTime < 500 && lastKnownData) {
    console.log('Using recent cached traffic data to prevent excessive requests');
    return {
      ...lastKnownData,
      timeframe: timeframe
    };
  }

  // Create new request
  activeRequest = performActualTrafficRequest(timeframe, timestamp);
  lastRequestTime = now;

  try {
    return await activeRequest;
  } finally {
    activeRequest = null; // Clear when done
  }
}

/**
 * Perform the actual API request
 */
async function performActualTrafficRequest(timeframe: string, timestamp?: number): Promise<TrafficResponse> {
  try {
    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 2000); // 2 second timeout

    // Use unique timer names to avoid conflicts
    const timerName = `fetchTrafficData_${Date.now()}`;
    console.time(timerName);

    // Add cache-busting parameter
    const cacheBuster = timestamp || Date.now();
    const response = await fetch(`http://localhost:8000/api/ids/traffic?timeframe=${timeframe}&t=${cacheBuster}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    console.timeEnd(timerName);

    if (!response.ok) {
      throw new Error(`Failed to fetch traffic data: ${response.statusText}`);
    }

    const data = await response.json();

    // Process and validate the data
    const processedData: TrafficResponse = {
      traffic_data: data.traffic_data ?? [],
      protocol_data: data.protocol_data ?? [],
      packet_stats: {
        avg_packet_rate: data.packet_stats?.avg_packet_rate ?? 0,
        peak_packet_rate: data.packet_stats?.peak_packet_rate ?? 0,
        total_packets_period: data.packet_stats?.total_packets_period ?? 0,
        avg_bandwidth_util: data.packet_stats?.avg_bandwidth_util ?? 0,
        peak_bandwidth_util: data.packet_stats?.peak_bandwidth_util ?? 0,
        packet_distribution: data.packet_stats?.packet_distribution ? {
          tcp_percent: data.packet_stats.packet_distribution.tcp_percent ?? 0,
          udp_percent: data.packet_stats.packet_distribution.udp_percent ?? 0,
          icmp_percent: data.packet_stats.packet_distribution.icmp_percent ?? 0,
          other_percent: data.packet_stats.packet_distribution.other_percent ?? 0,
        } : undefined,
      },
      timeframe: timeframe,
      total_points: data.total_points ?? 0
    };

    // Cache successful response
    lastKnownData = processedData;
    consecutiveFailures = 0;

    return processedData;

  } catch (error) {
    consecutiveFailures++;
    console.error(`Error in fetchTrafficData (failure #${consecutiveFailures}):`, error);

    // Determine error type for better handling
    let errorType = 'unknown';
    let errorMessage = 'Unknown error occurred';

    if (error.name === 'AbortError') {
      errorType = 'timeout';
      errorMessage = 'Request timed out';
    } else if (error.message?.includes('Failed to fetch')) {
      errorType = 'network';
      errorMessage = 'Network connection failed';
    } else if (error.message?.includes('500')) {
      errorType = 'server';
      errorMessage = 'Server error';
    }

    // Return fallback data if we have cached data and it's not too many consecutive failures
    if (lastKnownData && consecutiveFailures < 5) {
      console.warn(`Using cached traffic data due to ${errorType}: ${errorMessage}`);
      return {
        ...lastKnownData,
        timeframe: timeframe
      };
    }

    // If no cached data or too many failures, return empty but valid structure
    console.error(`No cached traffic data available, returning empty structure`);
    return {
      traffic_data: [],
      protocol_data: [],
      packet_stats: {
        avg_packet_rate: 0,
        peak_packet_rate: 0,
        total_packets_period: 0,
        avg_bandwidth_util: 0,
        peak_bandwidth_util: 0,
        packet_distribution: {
          tcp_percent: 0,
          udp_percent: 0,
          icmp_percent: 0,
          other_percent: 0,
        },
      },
      timeframe: timeframe,
      total_points: 0
    };
  }
}

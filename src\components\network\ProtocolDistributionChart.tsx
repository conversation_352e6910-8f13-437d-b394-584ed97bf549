import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from "recharts";
import { Network } from "lucide-react";
import { TrafficDataPoint, ProtocolData } from "@/types/traffic";

interface ProtocolDistributionChartProps {
  trafficData: TrafficDataPoint[];
  protocolData: ProtocolData[];
  isLoading: boolean;
  className?: string;
}

const PROTOCOL_COLORS = {
  TCP: '#3b82f6',
  UDP: '#10b981', 
  ICMP: '#f59e0b',
  Other: '#6b7280'
};

export function ProtocolDistributionChart({ 
  trafficData, 
  protocolData, 
  isLoading, 
  className 
}: ProtocolDistributionChartProps) {
  


  // Custom tooltip for pie chart
  const PieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            {data.value} flows ({data.percent}%)
          </p>
        </div>
      );
    }
    return null;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <div className={className}>
      <div className="grid gap-6">
        {/* Protocol Distribution Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              Protocol Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 w-full bg-muted animate-pulse rounded"></div>
            ) : (
              <div className="space-y-4">
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={protocolData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={2}
                        dataKey="value"
                      >
                        {protocolData.map((entry, index) => (
                          <Cell 
                            key={`cell-${index}`} 
                            fill={PROTOCOL_COLORS[entry.name as keyof typeof PROTOCOL_COLORS] || '#6b7280'} 
                          />
                        ))}
                      </Pie>
                      <Tooltip content={<PieTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                
                {/* Protocol Legend */}
                <div className="flex flex-wrap gap-2">
                  {protocolData.map((protocol, index) => (
                    <Badge 
                      key={index}
                      variant="outline" 
                      className="flex items-center gap-1"
                    >
                      <div 
                        className="h-2 w-2 rounded-full" 
                        style={{ 
                          backgroundColor: PROTOCOL_COLORS[protocol.name as keyof typeof PROTOCOL_COLORS] || '#6b7280' 
                        }}
                      />
                      {protocol.name}: {protocol.percent}%
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>


      </div>

      {/* Protocol Statistics */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Network className="h-5 w-5" />
            Protocol Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="h-16 w-full bg-muted animate-pulse rounded"></div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {protocolData.map((protocol, index) => (
                <div key={index} className="text-center p-4 border rounded-lg">
                  <div 
                    className="h-3 w-3 rounded-full mx-auto mb-2" 
                    style={{ 
                      backgroundColor: PROTOCOL_COLORS[protocol.name as keyof typeof PROTOCOL_COLORS] || '#6b7280' 
                    }}
                  />
                  <div className="text-lg font-bold">{formatNumber(protocol.value)}</div>
                  <div className="text-sm text-muted-foreground">{protocol.name} Flows</div>
                  <div className="text-xs text-muted-foreground">{protocol.percent}% of total</div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}


# Flow Sentinel IDS Backend

This is the Django backend for the Flow Sentinel IDS application. It provides a RESTful API for controlling the Intrusion Detection System and a WebSocket interface for real-time alerts and logs.

## Setup Instructions

### 1. Install requirements

```bash
cd backend
pip install -r requirements.txt
```

### 2. Set up the model files

```bash
# First, create the model files directory and placeholder files
python setup_model_files.py

# Replace the placeholder files with your actual model files:
# - ids/model_files/DecisionTree_model.pkl
# - ids/model_files/label_array.npy
# - ids/model_files/DecisionTree_feature_columns.txt
```

### 3. Initialize the database

```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. Create a superuser (optional)

```bash
python manage.py createsuperuser
```

### 5. Run the server

```bash
python manage.py runserver
```

## API Endpoints

- `POST /api/ids/start` - Start the IDS (parameters: interface, interval)
- `POST /api/ids/stop` - Stop the IDS
- `GET /api/ids/status` - Get IDS status
- `GET /api/ids/alerts` - Get recent alerts
- `GET /api/ids/logs` - Get recent logs

## WebSocket Endpoint

Connect to `ws://localhost:8000/ws/ids/` to receive real-time updates for:
- IDS logs
- Security alerts

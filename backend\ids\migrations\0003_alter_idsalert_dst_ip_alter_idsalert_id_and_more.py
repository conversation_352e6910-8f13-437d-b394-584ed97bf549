# Generated by Django 4.2.10 on 2025-04-07 16:23

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("ids", "0002_auto_add_protocol_and_description"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="idsalert",
            name="dst_ip",
            field=models.CharField(max_length=45),
        ),
        migrations.AlterField(
            model_name="idsalert",
            name="id",
            field=models.AutoField(primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name="idsalert",
            name="src_ip",
            field=models.Char<PERSON>ield(max_length=45),
        ),
        migrations.AlterField(
            model_name="idsalert",
            name="timestamp",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="idslog",
            name="id",
            field=models.AutoField(primary_key=True, serialize=False),
        ),
        migrations.Alter<PERSON>ield(
            model_name="idslog",
            name="timestamp",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterModelTable(
            name="idsalert",
            table="ids_idsalert",
        ),
        migrations.AlterModelTable(
            name="idslog",
            table="ids_idslog",
        ),
    ]


import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { Navbar } from "@/components/layout/Navbar";
import { Sidebar } from "@/components/layout/Sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Trash2, Database, AlertTriangle } from "lucide-react";
import { notificationService } from "@/services/notificationService";


// Interface for IDS settings
interface IDSSettings {
  captureInterface: string;
  captureInterval: number;
  maxIdleTime: number;
  outputDir: string;
  darkMode: boolean;
  enableNotifications: boolean;
  emailAlerts: boolean;
  emailAddress: string;
  historicalWindow: number;
  debugMode: boolean;
  pcapCapture: boolean;
  consolidatedCsv: boolean;
  csvSaveInterval: number;
}

// Data Management Component
function DataManagementCard() {
  const [dataStats, setDataStats] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [cleaning, setCleaning] = useState(false);
  const { toast } = useToast();

  // Fetch dataset statistics
  const fetchDataStats = async () => {
    try {
      setLoading(true);
      // Always fetch fresh data - no caching
      const response = await fetch(`http://localhost:8000/api/ids/clean?t=${Date.now()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Dataset statistics received:', data);
        setDataStats(data);
      } else {
        console.error('Failed to fetch data statistics:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('Error response:', errorText);
      }
    } catch (error) {
      console.error('Error fetching data statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  // Clean dataset
  const cleanDataset = async (type: string, options: any = {}) => {
    try {
      setCleaning(true);

      const response = await fetch('http://localhost:8000/api/ids/clean', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          ...options
        })
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Dataset Cleaned",
          description: result.message,
          variant: "default"
        });

        // Refresh statistics
        fetchDataStats();

        // Notify other components that dataset was cleaned
        window.dispatchEvent(new CustomEvent('dataset-cleaned', {
          detail: { type, options, result }
        }));
      } else {
        throw new Error('Failed to clean dataset');
      }
    } catch (error) {
      console.error('Error cleaning dataset:', error);
      toast({
        title: "Cleanup Error",
        description: String(error),
        variant: "destructive"
      });
    } finally {
      setCleaning(false);
    }
  };

  // Load data stats on component mount
  useEffect(() => {
    fetchDataStats();
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Dataset Management
        </CardTitle>
        <CardDescription>
          Manage and clean the IDS dataset (alerts and logs)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin h-6 w-6 border-t-2 border-b-2 border-primary rounded-full"></div>
          </div>
        ) : dataStats ? (
          <div className="space-y-4">
            {/* Dataset Statistics */}
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium text-sm text-muted-foreground">Alerts</h4>
                <p className="text-2xl font-bold">{dataStats.alerts?.total || 0}</p>
                {dataStats.alerts?.oldest && (
                  <p className="text-xs text-muted-foreground">
                    Oldest: {new Date(dataStats.alerts.oldest).toLocaleDateString()}
                  </p>
                )}
              </div>
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium text-sm text-muted-foreground">Logs</h4>
                <p className="text-2xl font-bold">{dataStats.logs?.total || 0}</p>
                {dataStats.logs?.oldest && (
                  <p className="text-xs text-muted-foreground">
                    Oldest: {new Date(dataStats.logs.oldest).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>

            {/* Cleanup Actions */}
            <div className="space-y-3">
              <h4 className="font-medium">Cleanup Actions</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  onClick={() => cleanDataset('alerts')}
                  disabled={cleaning}
                  className="justify-start"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All Alerts
                </Button>

                <Button
                  variant="outline"
                  onClick={() => cleanDataset('logs')}
                  disabled={cleaning}
                  className="justify-start"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All Logs
                </Button>

                <Button
                  variant="outline"
                  onClick={() => cleanDataset('by_date', { alerts_days: 7, logs_days: 3 })}
                  disabled={cleaning}
                  className="justify-start"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Old Data (7+ days)
                </Button>

                <Button
                  variant="destructive"
                  onClick={() => {
                    if (confirm('Are you sure you want to delete ALL data? This cannot be undone.')) {
                      cleanDataset('all');
                    }
                  }}
                  disabled={cleaning}
                  className="justify-start"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Clear All Data
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <p className="text-muted-foreground">Unable to load dataset statistics</p>
        )}
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          onClick={fetchDataStats}
          disabled={loading || cleaning}
        >
          {loading ? 'Loading...' : 'Refresh Statistics'}
        </Button>
      </CardFooter>
    </Card>
  );
}

export default function Settings() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [applying, setApplying] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState(notificationService.getSettings());
  const { theme, setTheme } = useTheme();
  const [settings, setSettings] = useState<IDSSettings>({
    captureInterface: 'auto',
    captureInterval: 2,
    maxIdleTime: 2,
    outputDir: './ids_results',
    darkMode: true,
    enableNotifications: true,
    emailAlerts: false,
    emailAddress: '',
    historicalWindow: 60,
    debugMode: false,
    pcapCapture: true,
    consolidatedCsv: true,
    csvSaveInterval: 300
  });
  const [originalSettings, setOriginalSettings] = useState<IDSSettings | null>(null);

  const [interfaces, setInterfaces] = useState<string[]>(['auto']);
  const { toast } = useToast();

  useEffect(() => {
    // Fetch current settings and available interfaces
    fetchSettings();
  }, []);

  // Sync theme with settings (one-way: theme -> settings display only)
  useEffect(() => {
    if (theme) {
      // Handle system theme by checking actual applied theme
      const isDark = theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);
      setSettings(prev => ({
        ...prev,
        darkMode: isDark
      }));
    }
  }, [theme]);

  const fetchSettings = async () => {
    try {
      setLoading(true);

      // Fetch settings from backend
      const response = await fetch('http://localhost:8000/api/ids/settings', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        // Load settings but preserve current theme choice
        const loadedSettings = { ...data.settings };

        // Don't override theme - let the current theme state determine darkMode display
        // The useEffect will sync the darkMode display value with current theme
        delete loadedSettings.darkMode;

        const newSettings = {
          ...settings,
          ...loadedSettings
        };

        setSettings(newSettings);
        setOriginalSettings(newSettings);
        setHasUnsavedChanges(false);

        // Set available interfaces if provided
        if (data.available_interfaces) {
          setInterfaces(['auto', ...data.available_interfaces]);
        }
      } else {
        console.error('Failed to fetch settings:', response.statusText);
        toast({
          title: "Error",
          description: "Failed to load settings from backend",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast({
        title: "Connection Error",
        description: "Could not connect to the IDS backend",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSettingsChange = (field: keyof IDSSettings, value: any) => {
    // Validate numeric inputs with proper ranges
    if (field === 'captureInterval' || field === 'maxIdleTime') {
      const numValue = Number(value);
      // Allow empty string for editing, but validate when not empty
      if (value !== '' && (isNaN(numValue) || numValue < 0.1 || numValue > 60)) {
        console.warn(`Invalid value for ${field}: ${value}`);
        return; // Don't update with invalid values
      }
      value = value === '' ? '' : numValue;
    } else if (field === 'historicalWindow') {
      const numValue = Number(value);
      // Allow empty string for editing, but validate when not empty
      if (value !== '' && (isNaN(numValue) || numValue < 10 || numValue > 3600)) {
        console.warn(`Invalid value for ${field}: ${value}`);
        return; // Don't update with invalid values
      }
      value = value === '' ? '' : numValue;
    } else if (field === 'csvSaveInterval') {
      const numValue = Number(value);
      // Allow empty string for editing, but validate when not empty (60 seconds to 3600 seconds = 1 hour)
      if (value !== '' && (isNaN(numValue) || numValue < 60 || numValue > 3600)) {
        console.warn(`Invalid value for ${field}: ${value}`);
        return; // Don't update with invalid values
      }
      value = value === '' ? '' : numValue;
    }

    console.log(`Setting ${field} to:`, value);

    const newSettings = {
      ...settings,
      [field]: value
    };

    setSettings(newSettings);

    // Check if settings have changed from original (excluding darkMode for theme)
    if (originalSettings) {
      const { darkMode: _, ...currentWithoutTheme } = newSettings;
      const { darkMode: __, ...originalWithoutTheme } = originalSettings;
      const hasChanges = JSON.stringify(currentWithoutTheme) !== JSON.stringify(originalWithoutTheme);
      setHasUnsavedChanges(hasChanges);
    }

    // Handle theme change immediately
    if (field === 'darkMode') {
      setTheme(value ? 'dark' : 'light');
    }

    // Handle notification settings changes
    if (field === 'enableNotifications') {
      const newNotificationSettings = {
        ...notificationSettings,
        enableNotifications: value
      };
      setNotificationSettings(newNotificationSettings);
      notificationService.updateSettings(newNotificationSettings);
    }
  };

  const handleNotificationSettingChange = (field: string, value: boolean) => {
    const newNotificationSettings = {
      ...notificationSettings,
      [field]: value
    };
    setNotificationSettings(newNotificationSettings);
    notificationService.updateSettings(newNotificationSettings);
  };

  const handleReset = (tabName: string) => {
    // Show confirmation toast
    toast({
      title: "Settings Reset",
      description: `${tabName} settings have been reset to defaults.`
    });

    // In a real implementation, this would call the backend reset API
    // and then refresh the settings
    fetchSettings();
  };

  const handleSave = async (tabName: string) => {
    try {
      setSaving(true);

      // Validate that numeric fields are not empty before saving
      if (settings.captureInterval === '' || settings.maxIdleTime === '' || settings.historicalWindow === '' || settings.csvSaveInterval === '') {
        toast({
          title: "Validation Error",
          description: "Please fill in all numeric fields before saving.",
          variant: "destructive"
        });
        return;
      }

      // Prepare settings for backend (exclude theme-related settings)
      const { darkMode, ...backendSettings } = settings;

      // Save settings to backend
      const response = await fetch('http://localhost:8000/api/ids/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings: backendSettings })
      });

      if (response.ok) {
        // Update original settings and reset unsaved changes flag
        setOriginalSettings(settings);
        setHasUnsavedChanges(false);

        toast({
          title: "Settings Saved",
          description: `${tabName} settings have been saved successfully. Restart the IDS for settings to take effect.`
        });

        // Show a more prominent notification about restarting the IDS
        setTimeout(() => {
          toast({
            title: "Restart Required",
            description: "To apply the new settings, you need to restart the IDS from the Dashboard.",
            variant: "default"
          });
        }, 1000);
      } else {
        throw new Error(`Failed to save settings: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Save Error",
        description: String(error),
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleApplySettings = async (tabName: string) => {
    try {
      setApplying(true);

      // Validate that numeric fields are not empty before applying
      if (settings.captureInterval === '' || settings.maxIdleTime === '' || settings.historicalWindow === '' || settings.csvSaveInterval === '') {
        toast({
          title: "Validation Error",
          description: "Please fill in all numeric fields before applying.",
          variant: "destructive"
        });
        return;
      }

      // Prepare settings for backend (exclude theme-related settings)
      const { darkMode, ...backendSettings } = settings;

      // First, save the settings
      const saveResponse = await fetch('http://localhost:8000/api/ids/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings: backendSettings })
      });

      if (!saveResponse.ok) {
        throw new Error(`Failed to save settings: ${saveResponse.statusText}`);
      }

      // Check if IDS is currently running
      const statusResponse = await fetch('http://localhost:8000/api/ids/status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!statusResponse.ok) {
        throw new Error('Failed to check IDS status');
      }

      const statusData = await statusResponse.json();
      const isRunning = statusData.running;

      // If IDS is running, stop it first
      if (isRunning) {
        const stopResponse = await fetch('http://localhost:8000/api/ids/stop', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!stopResponse.ok) {
          throw new Error('Failed to stop IDS');
        }

        // Small delay to ensure IDS has stopped
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Start IDS with new settings
      const startResponse = await fetch('http://localhost:8000/api/ids/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          interface: settings.captureInterface,
          interval: settings.captureInterval
        })
      });

      if (!startResponse.ok) {
        throw new Error('Failed to start IDS with new settings');
      }

      // Update original settings and reset unsaved changes flag
      setOriginalSettings(settings);
      setHasUnsavedChanges(false);

      // Show success message
      toast({
        title: "Settings Applied",
        description: `${tabName} settings have been saved and applied successfully.`,
        variant: "default"
      });

    } catch (error) {
      console.error('Error applying settings:', error);
      toast({
        title: "Apply Error",
        description: String(error),
        variant: "destructive"
      });
    } finally {
      setApplying(false);
    }
  };



  return (
    <div className="min-h-screen flex">
      <Sidebar />

      <div className="flex-1 flex flex-col min-h-screen md:pl-64">
        <Navbar />

        <main className="flex-1 p-4 md:p-6">
          <div className="flex flex-col gap-2 mb-6">
            <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">
              Configure Flow Sentinel IDS settings
            </p>
            {hasUnsavedChanges && (
              <div className="flex items-center gap-2 text-sm text-orange-600 bg-orange-50 dark:bg-orange-950 dark:text-orange-400 px-3 py-2 rounded-md border border-orange-200 dark:border-orange-800">
                <AlertTriangle className="h-4 w-4" />
                You have unsaved changes. Click "Save Changes" to save them.
              </div>
            )}
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full"></div>
            </div>
          ) : (
            <Tabs defaultValue="general" className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-6">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="notifications">Notifications</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
                <TabsTrigger value="data">Data Management</TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>System Settings</CardTitle>
                    <CardDescription>
                      Configure global settings for Flow Sentinel
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="captureInterface">Network Interface</Label>
                      <Select
                        value={settings.captureInterface}
                        onValueChange={(value) => handleSettingsChange('captureInterface', value)}
                      >
                        <SelectTrigger id="captureInterface">
                          <SelectValue placeholder="Select network interface" />
                        </SelectTrigger>
                        <SelectContent>
                          {interfaces.map(iface => (
                            <SelectItem key={iface} value={iface}>
                              {iface === 'auto' ? 'Auto-detect' : iface}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-muted-foreground">
                        Select the network interface to monitor
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="captureInterval">Capture Interval (seconds)</Label>
                      <Input
                        id="captureInterval"
                        type="number"
                        value={settings.captureInterval}
                        onChange={(e) => handleSettingsChange('captureInterval', e.target.value)}
                        min="1"
                        max="60"
                        step="0.1"
                      />
                      <p className="text-sm text-muted-foreground">
                        Time interval in seconds for each packet capture window (1-60 seconds)
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="maxIdleTime">Max Idle Time (seconds)</Label>
                      <Input
                        id="maxIdleTime"
                        type="number"
                        value={settings.maxIdleTime}
                        onChange={(e) => handleSettingsChange('maxIdleTime', e.target.value)}
                        min="1"
                        max="60"
                        step="0.1"
                      />
                      <p className="text-sm text-muted-foreground">
                        Maximum idle time before considering a flow complete (1-60 seconds)
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="outputDir">Output Directory</Label>
                      <Input
                        id="outputDir"
                        value={settings.outputDir}
                        onChange={(e) => handleSettingsChange('outputDir', e.target.value)}
                      />
                      <p className="text-sm text-muted-foreground">
                        Directory to save IDS results and logs
                      </p>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="darkMode">Dark Mode</Label>
                        <p className="text-sm text-muted-foreground">
                          Use dark theme for the interface
                        </p>
                      </div>
                      <Switch
                        id="darkMode"
                        checked={settings.darkMode}
                        onCheckedChange={(checked) => handleSettingsChange('darkMode', checked)}
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() => handleReset('General')}
                      disabled={saving || applying}
                    >
                      Reset to Defaults
                    </Button>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleSave('General')}
                        disabled={saving || applying}
                        variant={hasUnsavedChanges ? "default" : "outline"}
                        className={hasUnsavedChanges ? "bg-orange-600 hover:bg-orange-700" : ""}
                      >
                        {saving ? 'Saving...' : hasUnsavedChanges ? 'Save Changes' : 'Save Settings'}
                      </Button>
                      <Button
                        onClick={() => handleApplySettings('General')}
                        disabled={saving || applying}
                      >
                        {applying ? 'Applying...' : 'Apply Settings'}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </TabsContent>



              <TabsContent value="notifications" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification Settings</CardTitle>
                    <CardDescription>
                      Configure alert notifications
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="enableNotifications">Enable Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Show toast notifications for new alerts
                        </p>
                      </div>
                      <Switch
                        id="enableNotifications"
                        checked={settings.enableNotifications}
                        onCheckedChange={(checked) => handleSettingsChange('enableNotifications', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="enableBrowserNotifications">Browser Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Show system notifications outside the browser
                        </p>
                      </div>
                      <Switch
                        id="enableBrowserNotifications"
                        checked={notificationSettings.enableBrowserNotifications}
                        onCheckedChange={(checked) => handleNotificationSettingChange('enableBrowserNotifications', checked)}
                        disabled={!notificationService.isBrowserNotificationSupported()}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="enableSounds">Notification Sounds</Label>
                        <p className="text-sm text-muted-foreground">
                          Play audio alerts for security incidents
                        </p>
                      </div>
                      <Switch
                        id="enableSounds"
                        checked={notificationSettings.enableSounds}
                        onCheckedChange={(checked) => handleNotificationSettingChange('enableSounds', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="emailAlerts">Email Alerts</Label>
                        <p className="text-sm text-muted-foreground">
                          Send email for critical alerts
                        </p>
                      </div>
                      <Switch
                        id="emailAlerts"
                        checked={settings.emailAlerts}
                        onCheckedChange={(checked) => handleSettingsChange('emailAlerts', checked)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="emailAddress">Email Address</Label>
                      <Input
                        id="emailAddress"
                        type="email"
                        placeholder="<EMAIL>"
                        value={settings.emailAddress}
                        onChange={(e) => handleSettingsChange('emailAddress', e.target.value)}
                        disabled={!settings.emailAlerts}
                      />
                      <p className="text-sm text-muted-foreground">
                        Where to send email notifications
                      </p>
                    </div>

                    <div className="pt-4 border-t">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Test Notifications</Label>
                          <p className="text-sm text-muted-foreground">
                            Send a test notification to verify your settings
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            await notificationService.showSystemNotification(
                              "Test Notification",
                              "This is a test security alert notification",
                              "warning"
                            );
                          }}
                        >
                          Test Alert
                        </Button>
                      </div>
                    </div>

                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() => handleReset('Notification')}
                      disabled={saving || applying}
                    >
                      Reset to Defaults
                    </Button>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleSave('Notification')}
                        disabled={saving || applying}
                        variant={hasUnsavedChanges ? "default" : "outline"}
                        className={hasUnsavedChanges ? "bg-orange-600 hover:bg-orange-700" : ""}
                      >
                        {saving ? 'Saving...' : hasUnsavedChanges ? 'Save Changes' : 'Save Settings'}
                      </Button>
                      <Button
                        onClick={() => handleApplySettings('Notification')}
                        disabled={saving || applying}
                      >
                        {applying ? 'Applying...' : 'Apply Settings'}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Advanced Settings</CardTitle>
                    <CardDescription>
                      Configure advanced system options
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="historicalWindow">Historical Window (seconds)</Label>
                      <Input
                        id="historicalWindow"
                        type="number"
                        value={settings.historicalWindow}
                        onChange={(e) => handleSettingsChange('historicalWindow', e.target.value)}
                        min="10"
                        max="3600"
                        step="1"
                      />
                      <p className="text-sm text-muted-foreground">
                        Window size for keeping historical alerts (10-3600 seconds)
                      </p>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="debugMode">Debug Mode</Label>
                        <p className="text-sm text-muted-foreground">
                          Enable verbose logging
                        </p>
                      </div>
                      <Switch
                        id="debugMode"
                        checked={settings.debugMode}
                        onCheckedChange={(checked) => handleSettingsChange('debugMode', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="pcapCapture">PCAP Capture</Label>
                        <p className="text-sm text-muted-foreground">
                          Save packet captures to .pcap files (can be opened with Wireshark)
                        </p>
                      </div>
                      <Switch
                        id="pcapCapture"
                        checked={settings.pcapCapture}
                        onCheckedChange={(checked) => handleSettingsChange('pcapCapture', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="consolidatedCsv">Consolidated CSV</Label>
                        <p className="text-sm text-muted-foreground">
                          Save all flows in a single CSV file instead of multiple small files
                        </p>
                      </div>
                      <Switch
                        id="consolidatedCsv"
                        checked={settings.consolidatedCsv}
                        onCheckedChange={(checked) => handleSettingsChange('consolidatedCsv', checked)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="csvSaveInterval">CSV Save Interval (seconds)</Label>
                      <Input
                        id="csvSaveInterval"
                        type="number"
                        value={settings.csvSaveInterval}
                        onChange={(e) => handleSettingsChange('csvSaveInterval', e.target.value)}
                        min="60"
                        max="3600"
                        step="60"
                        disabled={!settings.consolidatedCsv}
                      />
                      <p className="text-sm text-muted-foreground">
                        How often to save the consolidated CSV file (60-3600 seconds)
                      </p>
                    </div>

                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={() => handleReset('Advanced')}
                      disabled={saving || applying}
                    >
                      Reset to Defaults
                    </Button>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleSave('Advanced')}
                        disabled={saving || applying}
                        variant={hasUnsavedChanges ? "default" : "outline"}
                        className={hasUnsavedChanges ? "bg-orange-600 hover:bg-orange-700" : ""}
                      >
                        {saving ? 'Saving...' : hasUnsavedChanges ? 'Save Changes' : 'Save Settings'}
                      </Button>
                      <Button
                        onClick={() => handleApplySettings('Advanced')}
                        disabled={saving || applying}
                      >
                        {applying ? 'Applying...' : 'Apply Settings'}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="data" className="space-y-6">
                <DataManagementCard />
              </TabsContent>
            </Tabs>
          )}
        </main>
      </div>


    </div>
  );
}

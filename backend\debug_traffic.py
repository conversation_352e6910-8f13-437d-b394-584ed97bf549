#!/usr/bin/env python3
"""
Debug script for traffic data issues
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ids.ids_manager import IDSManager
from ids.views.data_views import IDSTrafficView
from django.test import RequestFactory
import requests

def debug_traffic_data():
    print("=== Debugging Traffic Data ===")
    
    # 1. Check IDS status
    ids_manager = IDSManager()
    print(f"IDS running: {ids_manager.is_running}")
    
    if ids_manager.is_running and ids_manager.ids:
        print(f"IDS instance exists: True")
        print(f"Flow history length: {len(ids_manager.ids.flow_history)}")
        
        if ids_manager.ids.flow_history:
            print("Recent flow records:")
            for i, record in enumerate(list(ids_manager.ids.flow_history)[-3:]):
                print(f"  {i}: {record['time_str']} - {record['total_flows']} flows")
        
        # Test get_flow_history method
        history = ids_manager.ids.get_flow_history('1h')
        print(f"1h history: {len(history)} records")
    else:
        print("IDS not running or no instance")
    
    # 2. Test the traffic view directly
    print("\n=== Testing Traffic View ===")
    factory = RequestFactory()
    request = factory.get('/api/ids/traffic?timeframe=1h')
    
    view = IDSTrafficView()
    response = view.get(request)
    
    print(f"Response status: {response.status_code}")
    if response.status_code == 200:
        data = response.data
        print(f"Data points returned: {len(data['traffic_data'])}")
        
        if data['traffic_data']:
            print("First 3 data points:")
            for i, point in enumerate(data['traffic_data'][:3]):
                print(f"  {i}: {point['time']} - {point['total_flows']} flows")
        else:
            print("No traffic data in response")
    else:
        print(f"Error response: {response.data}")
    
    # 3. Test API endpoint via HTTP
    print("\n=== Testing HTTP API ===")
    try:
        response = requests.get('http://localhost:8000/api/ids/traffic?timeframe=1h')
        print(f"HTTP status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"HTTP data points: {len(data['traffic_data'])}")
            
            if data['traffic_data']:
                print("HTTP first 3 data points:")
                for i, point in enumerate(data['traffic_data'][:3]):
                    print(f"  {i}: {point['time']} - {point['total_packets']} packets")
        else:
            print(f"HTTP error: {response.text}")
    except Exception as e:
        print(f"HTTP request failed: {e}")

if __name__ == "__main__":
    debug_traffic_data()

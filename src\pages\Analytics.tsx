
import { useState, useEffect } from "react";
import { Navbar } from "@/components/layout/Navbar";
import { Sidebar } from "@/components/layout/Sidebar";
import { useToast } from "@/hooks/use-toast";
import { AnalyticsHeader } from "@/components/analytics/AnalyticsHeader";
import { AnalyticsSummary } from "@/components/analytics/AnalyticsSummary";
import { NetworkPacketDashboard } from "@/components/network/NetworkFlowDashboard";

import { fetchAnalyticsData } from "@/services/analyticsService";
import { AnalyticsData } from "@/types/analytics";

const Analytics = () => {
  const { toast } = useToast();
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    threatStats: [],
    attackTrends: [],
    attackHours: [],
    geographicData: [],
    totalAttacks: 0,
    changePercent: 0
  });
  const [timeframe, setTimeframe] = useState("7d");

  // Fetch analytics data from backend
  useEffect(() => {
    const loadAnalyticsData = async (isInitial: boolean = false) => {
      try {
        if (isInitial) {
          setIsInitialLoad(true);
        } else {
          setIsUpdating(true);
        }

        const data = await fetchAnalyticsData(timeframe, Date.now());
        setAnalyticsData(data);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        toast({
          title: "Connection Error",
          description: "Could not connect to the backend server",
          variant: "destructive"
        });
      } finally {
        if (isInitial) {
          setIsInitialLoad(false);
        } else {
          setIsUpdating(false);
        }
      }
    };

    loadAnalyticsData(true);

    // Poll for updates every minute
    const intervalId = setInterval(() => loadAnalyticsData(false), 60000);

    return () => clearInterval(intervalId);
  }, [timeframe, toast]);

  const handleTimeframeChange = (value: string) => {
    setTimeframe(value);
  };

  const handleDownloadCSV = () => {
    try {
      // Convert analytics data to CSV format
      const csvData = generateCSVFromAnalyticsData(analyticsData, timeframe);

      // Create and download the CSV file
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics_${timeframe}_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "CSV Downloaded",
        description: "Analytics data has been exported to CSV file."
      });
    } catch (error) {
      console.error('Error generating CSV:', error);
      toast({
        title: "Download Failed",
        description: "Could not generate CSV file. Please try again.",
        variant: "destructive"
      });
    }
  };

  const generateCSVFromAnalyticsData = (data: AnalyticsData, timeframe: string): string => {
    const lines: string[] = [];

    // Add header with metadata
    lines.push(`# Flow Sentinel IDS Analytics Report`);
    lines.push(`# Generated: ${new Date().toISOString()}`);
    lines.push(`# Timeframe: ${timeframe}`);
    lines.push(`# Total Attacks: ${data.totalAttacks}`);
    lines.push(`# Change Percent: ${data.changePercent}%`);
    lines.push('');

    // Threat Statistics
    lines.push('## Threat Statistics');
    lines.push('Attack Type,Count,Percentage');
    data.threatStats.forEach(stat => {
      lines.push(`${stat.type},${stat.count},${stat.percentage}`);
    });
    lines.push('');

    // Attack Trends (if available)
    if (data.attackTrends && data.attackTrends.length > 0) {
      lines.push('## Attack Trends');
      lines.push('Time,Attacks');
      data.attackTrends.forEach(trend => {
        lines.push(`${trend.time},${trend.attacks}`);
      });
      lines.push('');
    }

    // Attack Hours (if available)
    if (data.attackHours && data.attackHours.length > 0) {
      lines.push('## Attack Distribution by Hour');
      lines.push('Hour,Count');
      data.attackHours.forEach(hour => {
        lines.push(`${hour.hour},${hour.count}`);
      });
      lines.push('');
    }

    // Geographic Data (if available)
    if (data.geographicData && data.geographicData.length > 0) {
      lines.push('## Geographic Distribution');
      lines.push('Country,Count');
      data.geographicData.forEach(geo => {
        lines.push(`${geo.country},${geo.count}`);
      });
    }

    return lines.join('\n');
  };

  return (
    <div className="min-h-screen flex">
      <Sidebar />

      <div className="flex-1 flex flex-col min-h-screen md:pl-64">
        <Navbar />

        <main className="flex-1 p-4 md:p-6 overflow-auto">
          <AnalyticsHeader
            timeframe={timeframe}
            onTimeframeChange={handleTimeframeChange}
            onDownloadCSV={handleDownloadCSV}
          />

          <AnalyticsSummary
            loading={isInitialLoad}
            isUpdating={isUpdating}
            threatStats={analyticsData.threatStats}
            totalAttacks={analyticsData.totalAttacks}
            changePercent={analyticsData.changePercent}
          />

          {/* Network Packet Dashboard */}
          <NetworkPacketDashboard className="mt-8" />

        </main>
      </div>
    </div>
  );
};

export default Analytics;

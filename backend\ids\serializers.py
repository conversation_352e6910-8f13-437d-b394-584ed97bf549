
from rest_framework import serializers
from .models import <PERSON><PERSON><PERSON><PERSON>, IDSLog, IDSSettings

class IDSAlertSerializer(serializers.ModelSerializer):
    class Meta:
        model = IDSAlert
        fields = '__all__'

class IDSLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = IDSLog
        fields = '__all__'

class IDSSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = IDSSettings
        fields = '__all__'

#!/usr/bin/env python3
"""
Offline Flow Extractor Script

This script captures network packets and extracts flows, saving them to CSV format.
It replicates the exact flow extraction methods used by the main IDS system.

Usage:
    python flow_extractor.py --interface wlan0 --duration 60 --output flows.csv
"""

import argparse
import time
import sys
import os
import signal
import logging
from collections import defaultdict
from typing import Dict, Any, Optional, Tuple, List
import pandas as pd
import numpy as np

# Network packet processing
from scapy.all import sniff, IP, TCP, UDP

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OfflineFlowExtractor:
    """
    Offline flow extractor that captures packets and extracts network flows.
    Based on the RealTimeIDS flow extraction logic.
    """
    
    def __init__(self, capture_interval=30):
        """
        Initialize the flow extractor.

        Args:
            capture_interval: Time window for flow aggregation (seconds)
        """
        self.capture_interval = capture_interval
        self.running = False

        # Flow storage - using consistent flow keys
        self.flows = defaultdict(lambda: {
            'start_time': None,
            'last_timestamp': None,
            'forward_packets': [],  # (timestamp, size, flags)
            'backward_packets': [], # (timestamp, size, flags)
            'protocol': None,
            'src_ip': None,
            'dst_ip': None,
            'src_port': None,
            'dst_port': None,
            'active_start': None,
            'active_times': [],
            'idle_start': None,
            'fwd_win_bytes': None,
            'bwd_win_bytes': None,
            'original_direction': None,  # Track original packet direction
        })

        # Completed flows ready for processing
        self.completed_flows = []

        # Enhanced connection tracking for Conn_Rate calculation
        self.port_connections = defaultdict(set)
        self.connection_timestamps = defaultdict(list)
        self.global_connection_rate = defaultdict(int)  # Global connection tracking

        # Attack pattern detection
        self.attack_indicators = {
            'syn_flood_count': 0,
            'connection_attempts': defaultdict(int),
            'rapid_connections': defaultdict(list)
        }

        # Statistics
        self.stats = {
            'total_packets': 0,
            'total_flows': 0,
            'protocol_distribution': defaultdict(int)
        }

        # Timing
        self.timestamp_first_packet = None
        self.capture_start_time = None

        # All extracted flows for final CSV
        self.all_flows = []

    def is_legitimate_traffic(self, src_ip: str, dst_ip: str, src_port: int, dst_port: int) -> bool:
        """
        Filter out legitimate traffic that shouldn't be analyzed.
        Made less restrictive to capture more flows for analysis.
        """
        # Only filter out very basic system traffic
        legitimate_ports = {
            53,    # DNS (already filtered by sniff filter)
            67, 68, # DHCP
            123,   # NTP
        }

        # Filter out traffic to/from legitimate ports
        if src_port in legitimate_ports or dst_port in legitimate_ports:
            return True

        # Filter out local network management traffic
        if (src_ip.startswith('169.254.') or dst_ip.startswith('169.254.') or  # Link-local
            src_ip.startswith('224.') or dst_ip.startswith('224.') or          # Multicast
            src_ip == '***************' or dst_ip == '***************'):      # Broadcast
            return True

        return False

    def get_flow_key(self, packet) -> Optional[Tuple]:
        """
        Generate a key for the flow based on the 5-tuple.
        Returns None if the packet should be filtered out.
        """
        if IP in packet:
            ip_src = packet[IP].src
            ip_dst = packet[IP].dst

            # Get port and protocol information
            src_port = 0
            dst_port = 0
            protocol = packet[IP].proto

            if TCP in packet:
                src_port = packet[TCP].sport
                dst_port = packet[TCP].dport
                protocol = 6  # TCP
            elif UDP in packet:
                src_port = packet[UDP].sport
                dst_port = packet[UDP].dport
                protocol = 17  # UDP

            # Filter out legitimate traffic
            if self.is_legitimate_traffic(ip_src, ip_dst, src_port, dst_port):
                return None

            # Create consistent flow key - always use actual source as first element
            # This fixes the source/destination IP switching issue
            return (ip_src, ip_dst, src_port, dst_port, protocol)

        return None

    def get_packet_direction(self, packet, flow_key) -> Optional[str]:
        """Determine if packet is forward or backward in the flow."""
        if IP not in packet:
            return None

        ip_src = packet[IP].src
        ip_dst = packet[IP].dst
        src_port = 0
        dst_port = 0

        if TCP in packet:
            src_port = packet[TCP].sport
            dst_port = packet[TCP].dport
        elif UDP in packet:
            src_port = packet[UDP].sport
            dst_port = packet[UDP].dport

        # Check if this packet matches the forward direction of the flow
        # Forward: packet source matches flow source
        if (ip_src, ip_dst, src_port, dst_port) == (flow_key[0], flow_key[1], flow_key[2], flow_key[3]):
            return 'forward'
        # Backward: packet destination matches flow source (reverse direction)
        elif (ip_dst, ip_src, dst_port, src_port) == (flow_key[0], flow_key[1], flow_key[2], flow_key[3]):
            return 'backward'
        else:
            return None

    def update_port_connections(self, packet, timestamp):
        """Enhanced port connection tracking for better Conn_Rate calculation."""
        if IP in packet and TCP in packet:
            src_ip = packet[IP].src
            dst_ip = packet[IP].dst
            dst_port = packet[TCP].dport
            tcp_flags = packet[TCP].flags

            # Track unique connections to this destination port
            connection_key = f"{src_ip}:{dst_port}"
            self.port_connections[dst_port].add(src_ip)
            self.connection_timestamps[dst_port].append(timestamp)

            # Enhanced attack detection - track SYN floods
            if tcp_flags & 0x02:  # SYN flag
                self.attack_indicators['syn_flood_count'] += 1
                self.attack_indicators['connection_attempts'][dst_port] += 1
                self.attack_indicators['rapid_connections'][dst_port].append(timestamp)

            # Global connection rate tracking (for entire capture period)
            self.global_connection_rate[dst_port] += 1

            # Clean old connections (older than 2 minutes for better attack detection)
            cutoff_time = timestamp - 120
            self.connection_timestamps[dst_port] = [
                ts for ts in self.connection_timestamps[dst_port] if ts > cutoff_time
            ]

            # Clean old rapid connections
            self.attack_indicators['rapid_connections'][dst_port] = [
                ts for ts in self.attack_indicators['rapid_connections'][dst_port] if ts > cutoff_time
            ]

    def extract_packet_features(self, packet, timestamp, flow_key, flow_data) -> None:
        """Extract features from a packet and update the flow data."""
        direction = self.get_packet_direction(packet, flow_key)
        if direction is None:
            return

        # Initialize flow data for new flows
        if flow_data['start_time'] is None:
            flow_data['start_time'] = timestamp
            flow_data['last_timestamp'] = timestamp
            flow_data['active_start'] = timestamp
            flow_data['protocol'] = flow_key[4]
            flow_data['src_ip'] = flow_key[0]
            flow_data['dst_ip'] = flow_key[1]
            flow_data['src_port'] = flow_key[2]
            flow_data['dst_port'] = flow_key[3]
            flow_data['original_direction'] = direction

        # Update last timestamp - ensure minimum duration
        prev_timestamp = flow_data['last_timestamp']
        flow_data['last_timestamp'] = timestamp

        # Ensure minimum flow duration for single-packet flows
        if timestamp == flow_data['start_time']:
            flow_data['last_timestamp'] = timestamp + 0.001  # Add 1ms minimum duration

        # Get packet size
        packet_size = len(packet)

        # Extract TCP flags and window sizes with better handling
        tcp_flags = 0
        if TCP in packet:
            tcp_flags = packet[TCP].flags
            window_size = packet[TCP].window

            # Always capture window sizes when available
            if direction == 'forward':
                if flow_data['fwd_win_bytes'] is None or flow_data['fwd_win_bytes'] == 0:
                    flow_data['fwd_win_bytes'] = window_size
            elif direction == 'backward':
                if flow_data['bwd_win_bytes'] is None or flow_data['bwd_win_bytes'] == 0:
                    flow_data['bwd_win_bytes'] = window_size

        # Store packet information with enhanced details
        packet_info = (timestamp, packet_size, tcp_flags)
        if direction == 'forward':
            flow_data['forward_packets'].append(packet_info)
        else:
            flow_data['backward_packets'].append(packet_info)

    def process_packet(self, packet) -> None:
        """Process a single packet and update flow records."""
        if not self.timestamp_first_packet:
            self.timestamp_first_packet = time.time()
        
        timestamp = time.time()
        
        # Update statistics
        self.stats['total_packets'] += 1
        
        # Track protocol distribution
        if IP in packet:
            if TCP in packet:
                self.stats['protocol_distribution']['TCP'] += 1
            elif UDP in packet:
                self.stats['protocol_distribution']['UDP'] += 1
            elif packet[IP].proto == 1:  # ICMP
                self.stats['protocol_distribution']['ICMP'] += 1
            else:
                self.stats['protocol_distribution']['Other'] += 1
        
        # Get flow key
        flow_key = self.get_flow_key(packet)
        if not flow_key:
            return
        
        # Update port connections for Conn_Rate calculation
        self.update_port_connections(packet, timestamp)
        
        # Update flow data
        self.extract_packet_features(packet, timestamp, flow_key, self.flows[flow_key])

    def compute_flow_features(self, flow_key, flow_data) -> Dict[str, Any]:
        """
        Enhanced feature computation with attack detection improvements.
        """
        features = {}

        # Basic flow information
        src_ip, dst_ip, src_port, dst_port, protocol = flow_key
        features['Src IP'] = src_ip
        features['Dst IP'] = dst_ip
        features['Src Port'] = src_port
        features['Dst Port'] = dst_port
        features['Protocol'] = protocol

        # Enhanced Flow Duration calculation
        if flow_data['start_time'] and flow_data['last_timestamp']:
            duration = flow_data['last_timestamp'] - flow_data['start_time']
            # Ensure minimum duration for attack detection
            if duration == 0:
                duration = 0.001  # 1ms minimum
            features['Flow Duration'] = int(duration * 1_000_000)  # Convert to microseconds
        else:
            features['Flow Duration'] = 1000  # 1ms default for single packets

        # Enhanced Connection Rate calculation
        # Use global connection rate for better attack detection
        global_conn_rate = self.global_connection_rate.get(dst_port, 0)
        window_conn_rate = len(self.port_connections.get(dst_port, set()))

        # Use the higher of global or window rate for attack detection
        features['Conn_Rate'] = max(global_conn_rate, window_conn_rate)

        # Attack-specific connection rate (rapid connections in last 30 seconds)
        current_time = time.time()
        rapid_conns = len([ts for ts in self.attack_indicators['rapid_connections'].get(dst_port, [])
                          if current_time - ts <= 30])
        if rapid_conns > features['Conn_Rate']:
            features['Conn_Rate'] = rapid_conns

        # Forward and backward packets
        fwd_packets = flow_data['forward_packets']
        bwd_packets = flow_data['backward_packets']

        # Enhanced forward packet statistics
        fwd_lens = [size for _, size, _ in fwd_packets]
        if fwd_lens:
            features['Fwd Seg Size Min'] = min(fwd_lens)
            features['TotLen Fwd Pkts'] = sum(fwd_lens)
        else:
            # Default values for attack patterns (many attacks have no forward data)
            features['Fwd Seg Size Min'] = 40  # Typical TCP header size
            features['TotLen Fwd Pkts'] = 40

        # Enhanced backward packet statistics
        bwd_lens = [size for _, size, _ in bwd_packets]
        if bwd_lens:
            features['Bwd Pkt Len Mean'] = sum(bwd_lens) / len(bwd_lens)
        else:
            # Default for attacks with no backward traffic
            features['Bwd Pkt Len Mean'] = 0.0

        # Enhanced window sizes with attack-aware defaults
        fwd_win = flow_data['fwd_win_bytes'] or 0
        bwd_win = flow_data['bwd_win_bytes'] or 0

        # For attack detection, use reasonable defaults if zero
        if fwd_win == 0 and len(fwd_packets) > 0:
            fwd_win = 65535  # Common default window size
        if bwd_win == 0 and len(bwd_packets) > 0:
            bwd_win = 65535

        features['Init Fwd Win Byts'] = fwd_win
        features['Init Bwd Win Byts'] = bwd_win

        # Enhanced Inter-arrival time statistics
        all_packets = [(ts, 'fwd') for ts, _, _ in fwd_packets] + [(ts, 'bwd') for ts, _, _ in bwd_packets]
        all_packets.sort()

        if len(all_packets) > 1:
            iats = [all_packets[i][0] - all_packets[i-1][0] for i in range(1, len(all_packets))]
            iats_microseconds = [max(iat * 1_000_000, 1) for iat in iats]  # Minimum 1 microsecond

            features['Flow IAT Mean'] = np.mean(iats_microseconds)
            features['Flow IAT Max'] = max(iats_microseconds)
        else:
            # Single packet flows - use small but non-zero values for attack detection
            features['Flow IAT Mean'] = 1000.0  # 1ms
            features['Flow IAT Max'] = 1000.0   # 1ms

        return features

    def process_flows(self):
        """
        Process completed flows and prepare them for CSV export.
        Returns dataframe with flows ready for export.
        """
        # Compute features for all completed flows
        flow_features = []

        for flow_key, flow_data in self.completed_flows:
            features = self.compute_flow_features(flow_key, flow_data)
            flow_features.append(features)

        # Reset completed flows for next window
        self.completed_flows = []

        if not flow_features:
            return None

        # Create dataframe with all flow features
        flows_df = pd.DataFrame(flow_features)

        # Add timestamp for when flows were processed
        flows_df['Processing_Timestamp'] = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

        return flows_df

    def complete_active_flows(self):
        """Mark all active flows as completed for processing."""
        current_time = time.time()

        # Move all active flows to completed flows
        for flow_key, flow_data in list(self.flows.items()):
            if flow_data['start_time'] is not None:
                self.completed_flows.append((flow_key, flow_data))

        # Clear active flows
        self.flows.clear()

        logger.info(f"Completed {len(self.completed_flows)} active flows")

    def capture_packets(self, interface, duration):
        """
        Enhanced packet capture with better attack detection timing.

        Args:
            interface: Network interface to capture from
            duration: Total capture duration in seconds
        """
        logger.info(f"Starting packet capture on interface {interface} for {duration} seconds")
        logger.info(f"Using {self.capture_interval}s flow aggregation windows")

        self.running = True
        self.capture_start_time = time.time()
        start_time = self.capture_start_time

        try:
            while self.running and (time.time() - start_time) < duration:
                window_start = time.time()

                # Capture packets for one window interval
                try:
                    sniff(
                        iface=interface,
                        prn=self.process_packet,
                        store=False,
                        timeout=self.capture_interval,
                        filter="not port 53 and not port 22"  # Filter out DNS and SSH
                    )
                except Exception as e:
                    logger.error(f"Error during packet capture: {str(e)}")
                    if not self.running:
                        break
                    time.sleep(1)
                    continue

                # Process flows from this window
                self.complete_active_flows()
                flows_df = self.process_flows()

                if flows_df is not None and len(flows_df) > 0:
                    self.all_flows.append(flows_df)
                    logger.info(f"Extracted {len(flows_df)} flows in this window")

                    # Log attack indicators
                    syn_count = self.attack_indicators['syn_flood_count']
                    if syn_count > 100:  # Potential SYN flood
                        logger.warning(f"High SYN count detected: {syn_count} packets")

                # Wait for remaining time in the interval
                elapsed = time.time() - window_start
                if elapsed < self.capture_interval:
                    time.sleep(self.capture_interval - elapsed)

        except KeyboardInterrupt:
            logger.info("Capture interrupted by user")
        finally:
            self.running = False

            # Process any remaining flows
            self.complete_active_flows()
            flows_df = self.process_flows()
            if flows_df is not None and len(flows_df) > 0:
                self.all_flows.append(flows_df)

        # Log final statistics
        total_duration = time.time() - start_time
        logger.info(f"Packet capture completed. Total packets: {self.stats['total_packets']}")
        logger.info(f"Total SYN packets: {self.attack_indicators['syn_flood_count']}")
        logger.info(f"Capture duration: {total_duration:.2f} seconds")

    def save_flows_to_csv(self, output_file):
        """
        Save all extracted flows to a CSV file.

        Args:
            output_file: Path to the output CSV file
        """
        if not self.all_flows:
            logger.warning("No flows to save")
            return

        # Combine all flow dataframes
        combined_df = pd.concat(self.all_flows, ignore_index=True)

        # Save to CSV
        combined_df.to_csv(output_file, index=False)

        logger.info(f"Saved {len(combined_df)} flows to {output_file}")

        # Print summary statistics
        print(f"\n=== Flow Extraction Summary ===")
        print(f"Total packets captured: {self.stats['total_packets']}")
        print(f"Total flows extracted: {len(combined_df)}")
        print(f"Protocol distribution: {dict(self.stats['protocol_distribution'])}")
        print(f"Output file: {output_file}")
        print(f"CSV columns: {list(combined_df.columns)}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False


def main():
    """Main function for the offline flow extractor."""
    parser = argparse.ArgumentParser(description='Offline Network Flow Extractor')

    parser.add_argument('--interface', '-i',
                       default='wlan0',
                       help='Network interface to capture from (default: wlan0)')

    parser.add_argument('--duration', '-d',
                       type=int, default=60,
                       help='Capture duration in seconds (default: 60)')

    parser.add_argument('--interval', '-t',
                       type=int, default=10,
                       help='Flow aggregation interval in seconds (default: 10)')

    parser.add_argument('--output', '-o',
                       default='extracted_flows.csv',
                       help='Output CSV file (default: extracted_flows.csv)')

    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create flow extractor
    extractor = OfflineFlowExtractor(capture_interval=args.interval)

    # Set up signal handlers
    signal.signal(signal.SIGINT, extractor.signal_handler)
    signal.signal(signal.SIGTERM, extractor.signal_handler)

    try:
        # Capture packets and extract flows
        extractor.capture_packets(args.interface, args.duration)

        # Save flows to CSV
        extractor.save_flows_to_csv(args.output)

        return 0

    except Exception as e:
        logger.error(f"Error during flow extraction: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

#!/bin/bash

# IDS Demonstration Script
# This script demonstrates the complete IDS workflow with guaranteed DDoS detection

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${CYAN}================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================${NC}"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Main demonstration
main() {
    print_header "IDS DEMONSTRATION - DDoS DETECTION"
    
    echo -e "${YELLOW}This demonstration will show:${NC}"
    echo "1. Flow extraction from network traffic"
    echo "2. ML-based threat classification"
    echo "3. DDoS correlation analysis"
    echo "4. Attack pattern detection"
    echo ""
    
    read -p "Press Enter to start the demonstration..."
    echo ""
    
    # Step 1: Show current directory contents
    print_info "Step 1: Checking available data..."
    if [[ -f "DDoS horrayyyyyyy/flows.csv" ]]; then
        DEMO_FLOWS=$(tail -n +2 "DDoS horrayyyyyyy/flows.csv" | wc -l)
        print_success "Found demo DDoS data with $DEMO_FLOWS flows"
    else
        print_error "Demo data not found. Please ensure 'DDoS horrayyyyyyy/flows.csv' exists."
        exit 1
    fi
    echo ""
    
    # Step 2: Run the IDS with demo data
    print_info "Step 2: Running IDS with demo DDoS data..."
    print_warning "Using pre-captured DDoS attack data for demonstration"
    echo ""
    
    # Use the demo mode
    if ./run_offline_ids.sh --demo; then
        print_success "IDS analysis completed successfully!"
    else
        print_error "IDS analysis failed"
        exit 1
    fi
    
    echo ""
    
    # Step 3: Show results analysis
    print_info "Step 3: Analyzing results..."
    
    if [[ -f "results.csv" ]]; then
        # Show threat summary
        print_info "Threat Analysis Summary:"
        python3 -c "
import pandas as pd
import sys

try:
    df = pd.read_csv('results.csv')
    print(f'  Total flows analyzed: {len(df)}')
    
    if 'Threat' in df.columns:
        threat_counts = df['Threat'].value_counts()
        for threat, count in threat_counts.items():
            print(f'  {threat}: {count} flows')
    
    if 'Prediction' in df.columns:
        print(f'\\n  Detailed Classifications:')
        pred_counts = df['Prediction'].value_counts()
        for pred, count in pred_counts.items():
            print(f'    {pred}: {count} flows')
            
    # Show DDoS specific analysis
    ddos_flows = df[df['Prediction'] == 'DDoS attack'] if 'Prediction' in df.columns else pd.DataFrame()
    if len(ddos_flows) > 0:
        print(f'\\n  🚨 DDoS ATTACK DETECTED!')
        print(f'    DDoS flows: {len(ddos_flows)}')
        
        # Analyze attack sources and targets
        if 'Src IP' in ddos_flows.columns and 'Dst IP' in ddos_flows.columns:
            unique_sources = ddos_flows['Src IP'].nunique()
            unique_targets = ddos_flows['Dst IP'].nunique()
            print(f'    Attacking sources: {unique_sources}')
            print(f'    Targeted victims: {unique_targets}')
            
            print(f'\\n    Attack Sources:')
            for src in ddos_flows['Src IP'].unique():
                count = len(ddos_flows[ddos_flows['Src IP'] == src])
                print(f'      {src}: {count} malicious flows')
                
            print(f'\\n    Attack Targets:')
            for dst in ddos_flows['Dst IP'].unique():
                count = len(ddos_flows[ddos_flows['Dst IP'] == dst])
                sources = ddos_flows[ddos_flows['Dst IP'] == dst]['Src IP'].nunique()
                print(f'      {dst}: {count} flows from {sources} sources')

except Exception as e:
    print(f'Error analyzing results: {e}')
    sys.exit(1)
"
        
        echo ""
        print_success "✅ DEMONSTRATION COMPLETED SUCCESSFULLY!"
        echo ""
        print_info "Files generated:"
        echo "  - flows.csv (network flows)"
        echo "  - results.csv (threat analysis results)"
        echo ""
        print_info "The system successfully detected and analyzed the DDoS attack!"
        
    else
        print_error "Results file not found"
        exit 1
    fi
}

# Check if script is executable
if [[ ! -x "./run_offline_ids.sh" ]]; then
    print_warning "Making run_offline_ids.sh executable..."
    chmod +x run_offline_ids.sh
fi

# Run main function
main "$@"

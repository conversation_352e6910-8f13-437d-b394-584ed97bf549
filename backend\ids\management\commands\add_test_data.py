from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from ids.models import ID<PERSON>lert, IDSLog
import random

class Command(BaseCommand):
    help = 'Add test data to the IDS database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--alerts',
            type=int,
            default=10,
            help='Number of test alerts to create (default: 10)'
        )
        parser.add_argument(
            '--logs',
            type=int,
            default=20,
            help='Number of test logs to create (default: 20)'
        )

    def handle(self, *args, **options):
        alerts_count = options['alerts']
        logs_count = options['logs']

        self.stdout.write(f'Creating {alerts_count} test alerts and {logs_count} test logs...')

        # Create test alerts
        attack_types = ['DoS', 'DDoS', 'Botnet', 'Brute Force']

        for i in range(alerts_count):
            # Create alerts with timestamps spread over the last 24 hours
            hours_ago = random.randint(0, 24)
            timestamp = timezone.now() - timedelta(hours=hours_ago)

            IDSAlert.objects.create(
                timestamp=timestamp,
                src_ip=f"192.168.1.{random.randint(100, 200)}",
                src_port=random.randint(1024, 65535),
                dst_ip=f"10.0.0.{random.randint(1, 50)}",
                dst_port=random.choice([80, 443, 22, 21, 25, 53]),
                protocol=random.choice(['TCP', 'UDP', 'ICMP']),
                attack_type=random.choice(attack_types),
                description=f"Test {random.choice(attack_types)} attack detected"
            )

        # Create test logs
        log_levels = ['debug', 'info', 'warning', 'error']
        log_messages = [
            'IDS system started',
            'Packet capture initialized',
            'Model loaded successfully',
            'Processing network flow',
            'Alert threshold exceeded',
            'Connection timeout',
            'Database updated',
            'System health check passed'
        ]

        for i in range(logs_count):
            hours_ago = random.randint(0, 48)
            timestamp = timezone.now() - timedelta(hours=hours_ago)
            
            IDSLog.objects.create(
                timestamp=timestamp,
                level=random.choice(log_levels),
                message=random.choice(log_messages)
            )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {alerts_count} alerts and {logs_count} logs')
        )

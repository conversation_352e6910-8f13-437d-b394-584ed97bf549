import { useState, useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Shield, ShieldAlert, ShieldCheck } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { notificationService } from "@/services/notificationService";

interface SecurityStatusCardProps {
  className?: string;
}

export function SecurityStatusCard({ className }: SecurityStatusCardProps) {
  const [isUnderAttack, setIsUnderAttack] = useState(false);
  const [attackTypes, setAttackTypes] = useState<string[]>([]);
  const [attackDuration, setAttackDuration] = useState<number>(0);
  const wsRef = useRef<WebSocket | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Check initial attack session status from backend
    fetchAttackSessionStatus();

    // Connect to WebSocket for real-time updates
    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, []);

  const fetchAttackSessionStatus = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/ids/status');
      if (response.ok) {
        const data = await response.json();
        if (data.attack_session) {
          setIsUnderAttack(data.attack_session.is_active);
          setAttackTypes(data.attack_session.attack_types || []);

          if (data.attack_session.is_active && data.attack_session.duration) {
            setAttackDuration(data.attack_session.duration * 1000); // Convert to ms
            startDurationTimer();
          }
        }
      }
    } catch (error) {
      console.error('Error fetching attack session status:', error);
    }
  };

  const connectWebSocket = () => {
    // Close any existing connection
    if (wsRef.current) {
      wsRef.current.close();
    }

    const ws = new WebSocket('ws://localhost:8000/ws/ids/');

    ws.onopen = () => {
      console.log('SecurityStatusCard WebSocket connected');
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        // Handle real-time alert messages
        if (data.type === 'alert') {
          // Process the alert through notification service for session tracking
          notificationService.processAttackAlert(
            data.attack_type,
            data.src_ip,
            data.dst_ip
          );
          
          // Update local state
          updateSecurityStatus();
        }
        
        // Handle attack session status updates
        if (data.type === 'attack_session_status') {
          setIsUnderAttack(data.isActive);
          setAttackTypes(data.attackTypes || []);
          
          if (data.isActive) {
            startDurationTimer();
          } else {
            stopDurationTimer();
            setAttackDuration(0);
          }
        }
      } catch (e) {
        console.error("Failed to parse WebSocket message:", e);
      }
    };

    ws.onclose = () => {
      console.log('SecurityStatusCard WebSocket disconnected');
      // Try to reconnect after a delay
      setTimeout(connectWebSocket, 5000);
    };

    ws.onerror = (error) => {
      console.error("SecurityStatusCard WebSocket error:", error);
    };

    wsRef.current = ws;
  };

  const updateSecurityStatus = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/ids/status');
      if (response.ok) {
        const data = await response.json();
        if (data.attack_session) {
          setIsUnderAttack(data.attack_session.is_active);
          setAttackTypes(data.attack_session.attack_types || []);

          if (data.attack_session.is_active && data.attack_session.duration) {
            setAttackDuration(data.attack_session.duration * 1000); // Convert to ms
            startDurationTimer();
          } else {
            stopDurationTimer();
            setAttackDuration(0);
          }
        }
      }
    } catch (error) {
      console.error('Error updating security status:', error);
    }
  };

  const startDurationTimer = () => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
    }

    durationIntervalRef.current = setInterval(async () => {
      try {
        const response = await fetch('http://localhost:8000/api/ids/status');
        if (response.ok) {
          const data = await response.json();
          if (data.attack_session && data.attack_session.is_active && data.attack_session.duration) {
            setAttackDuration(data.attack_session.duration * 1000); // Convert to ms
          } else {
            stopDurationTimer();
            setAttackDuration(0);
          }
        }
      } catch (error) {
        console.error('Error updating attack duration:', error);
      }
    }, 1000);
  };

  const stopDurationTimer = () => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
  };

  const formatDuration = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getUniqueAttackTypes = (): string[] => {
    return Array.from(new Set(attackTypes));
  };

  return (
    <Card className={`${className} ${isUnderAttack ? 'border-destructive bg-destructive/5' : 'border-green-500 bg-green-50 dark:bg-green-950/20'}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {isUnderAttack ? (
              <ShieldAlert className="h-6 w-6 text-destructive animate-pulse" />
            ) : (
              <ShieldCheck className="h-6 w-6 text-green-600" />
            )}
            <CardTitle className={`text-lg ${isUnderAttack ? 'text-destructive' : 'text-green-700 dark:text-green-400'}`}>
              Security Status
            </CardTitle>
          </div>
          <Badge 
            variant={isUnderAttack ? "destructive" : "default"}
            className={`px-3 py-1 text-sm font-medium ${
              isUnderAttack 
                ? 'bg-destructive text-destructive-foreground' 
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isUnderAttack ? "UNDER ATTACK" : "SECURE"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-center">
            <div className={`text-2xl font-bold ${isUnderAttack ? 'text-destructive' : 'text-green-700 dark:text-green-400'}`}>
              {isUnderAttack ? "You Are Being Attacked" : "You Are Safe"}
            </div>
            <p className={`text-sm mt-1 ${isUnderAttack ? 'text-destructive/80' : 'text-green-600/80 dark:text-green-400/80'}`}>
              {isUnderAttack 
                ? "Active security threats detected on your network" 
                : "No active threats detected on your network"
              }
            </p>
          </div>

          {isUnderAttack && (
            <div className="space-y-3 pt-2 border-t border-destructive/20">
              {attackDuration > 0 && (
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Attack Duration:</span>
                  <span className="font-mono text-destructive font-medium">
                    {formatDuration(attackDuration)}
                  </span>
                </div>
              )}
              
              {getUniqueAttackTypes().length > 0 && (
                <div className="space-y-2">
                  <span className="text-sm text-muted-foreground">Attack Types:</span>
                  <div className="flex flex-wrap gap-1">
                    {getUniqueAttackTypes().map((type, index) => (
                      <Badge 
                        key={index} 
                        variant="destructive" 
                        className="text-xs px-2 py-1"
                      >
                        {type}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

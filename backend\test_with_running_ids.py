#!/usr/bin/env python3
"""
Test script to verify traffic API with running IDS
"""

import os
import sys
import django
import time
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'flow_sentinel.settings')
django.setup()

from ids.ids_manager import IDSManager
from ids.models import IDSSettings

def test_with_running_ids():
    """Test the traffic API with a running IDS"""
    
    print("=== Testing Traffic API with Running IDS ===")
    
    # Start IDS
    ids_manager = IDSManager()
    if ids_manager.is_running:
        print("Stopping existing IDS...")
        ids_manager.stop_ids()
        time.sleep(2)

    print("Starting IDS...")
    settings = IDSSettings.get_settings()
    result = ids_manager.start_ids_with_settings(settings)
    print(f"Start result: {result['status']}")

    if result['status'] == 'success':
        print("IDS started. Waiting 20 seconds for flow data...")
        time.sleep(20)
        
        print("Checking flow history...")
        if hasattr(ids_manager.ids, 'flow_history'):
            print(f"Flow history length: {len(ids_manager.ids.flow_history)}")
            
            if ids_manager.ids.flow_history:
                print("Recent flow records:")
                for i, record in enumerate(list(ids_manager.ids.flow_history)[-5:]):
                    print(f"  {i}: {record['time_str']} - {record['total_flows']} flows")
        
        # Test the traffic API via HTTP
        print("\n=== Testing HTTP API ===")
        try:
            response = requests.get('http://localhost:8000/api/ids/traffic?timeframe=1h')
            print(f"HTTP status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"HTTP data points: {len(data['traffic_data'])}")
                
                if data['traffic_data']:
                    print("HTTP first 5 data points:")
                    for i, point in enumerate(data['traffic_data'][:5]):
                        print(f"  {i}: {point['time']} - {point['total_flows']} flows")
                    
                    # Check if all values are the same (flat line)
                    total_flows_values = [point['total_flows'] for point in data['traffic_data']]
                    unique_values = set(total_flows_values)
                    print(f"Unique total_flows values: {len(unique_values)}")
                    if len(unique_values) == 1:
                        print(f"❌ FLAT LINE DETECTED - all values are {list(unique_values)[0]}")
                    else:
                        print(f"✅ VARYING DATA - values range from {min(total_flows_values)} to {max(total_flows_values)}")
            else:
                print(f"HTTP error: {response.text}")
        except Exception as e:
            print(f"HTTP request failed: {e}")
        
        print("\nStopping IDS...")
        ids_manager.stop_ids()
    else:
        print(f"Failed to start: {result['message']}")

    print("Test complete")

if __name__ == "__main__":
    test_with_running_ids()

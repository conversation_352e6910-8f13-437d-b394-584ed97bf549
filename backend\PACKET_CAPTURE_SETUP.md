# Packet Capture Permission Setup for Flow Sentinel IDS

This document explains how to fix the packet capture permission issues on Kali Linux.

## Problem

The Flow Sentinel IDS requires raw socket access to capture network packets on interfaces like `wlan0`. On Linux systems, this requires elevated privileges, which causes the error:

```
Error during packet capture: wlan0: You don't have permission to perform this capture on that device
(socket: Operation not permitted)
```

## Solutions

### Option 1: Run with sudo (Recommended for Development)

**Pros:** Simple, works immediately
**Cons:** Runs entire Django server as root (less secure)

```bash
# Stop the current server (Ctrl+C)
cd backend
sudo ./start_server.sh
```

### Option 2: Set Capabilities (Recommended for Production)

**Pros:** More secure, only grants packet capture permissions
**Cons:** Requires one-time setup, needs to be redone if Python is updated

```bash
# One-time setup (run once)
cd backend
sudo ./setup_packet_capture.sh

# Then start server normally
./start_server_regular.sh
```

### Option 3: Manual Capability Setup

If you prefer to set capabilities manually:

```bash
# Find your Python executable
cd backend
source venv/bin/activate
which python

# Set capabilities (replace /path/to/python with actual path)
sudo setcap cap_net_raw,cap_net_admin=eip /path/to/python

# Also set capabilities on daphne if using ASGI
sudo setcap cap_net_raw,cap_net_admin=eip /path/to/daphne

# Verify capabilities
getcap /path/to/python
getcap /path/to/daphne

# Start server normally
./venv/bin/daphne flow_sentinel.asgi:application -b 0.0.0.0 -p 8000
```

## Available Scripts

- `start_server.sh` - Starts server with sudo (requires root)
- `setup_packet_capture.sh` - One-time setup for capabilities (requires root)
- `start_server_regular.sh` - Starts server as regular user (after capabilities setup)

## Verification

After setting up permissions, you should see:

1. No more "Operation not permitted" errors
2. Packet capture working in the IDS logs
3. Network traffic being analyzed

## Troubleshooting

### If capabilities don't work:

1. Check if your system supports capabilities:
   ```bash
   cat /proc/filesystems | grep cap
   ```

2. Verify the capability was set:
   ```bash
   getcap /path/to/your/python
   ```

3. If using a virtual environment, make sure you set capabilities on the venv Python, not system Python.

### If you still get permission errors:

1. Try running with sudo (Option 1)
2. Check if your user is in the correct groups:
   ```bash
   groups $USER
   ```
3. Ensure the network interface exists:
   ```bash
   ip link show
   ```

## Security Notes

- **Option 1 (sudo)**: Convenient for development but runs entire server as root
- **Option 2 (capabilities)**: More secure, only grants specific network permissions
- For production deployments, consider using a dedicated capture service or container with appropriate permissions

## Interface Selection

Make sure to select the correct network interface in the IDS settings:
- `wlan0` - Wireless interface
- `eth0` - Ethernet interface  
- `lo` - Loopback interface (for testing)

Use `ip link show` or `ifconfig` to see available interfaces.

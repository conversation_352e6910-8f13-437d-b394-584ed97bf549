<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flow Sentinel - Dashboard Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .animate-pulse-slow {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .status-indicator {
            position: relative;
        }
        
        .status-indicator::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
        }
        
        .status-safe::after {
            background-color: rgb(34, 197, 94);
            opacity: 0.75;
        }
        
        @keyframes ping {
            75%, 100% {
                transform: scale(2);
                opacity: 0;
            }
        }
        
        .metric-card {
            transition: transform 0.2s ease-in-out;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .chart-container {
            height: 300px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .chart-grid {
            position: absolute;
            inset: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .chart-canvas {
            position: relative;
            z-index: 2;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="shield" class="w-5 h-5 text-white"></i>
                    </div>
                    <h1 class="text-xl font-semibold text-gray-900">Flow Sentinel - Dashboard</h1>
                </div>
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2 text-sm text-gray-600">
                        <div class="w-2 h-2 bg-green-500 rounded-full status-indicator status-safe"></div>
                        <span>All Systems Operational</span>
                    </div>
                    <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg">
                        <i data-lucide="refresh-cw" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="p-6 max-w-7xl mx-auto">
        <!-- Welcome Section -->
        <div class="mb-6 p-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold mb-2">Welcome to Flow Sentinel</h2>
                    <p class="text-blue-100">Your network is being actively monitored and protected</p>
                </div>
                <div class="text-right">
                    <div class="text-3xl font-bold" id="current-time">14:25:30</div>
                    <div class="text-blue-100">System Time</div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="metric-card bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Security Score</p>
                        <p id="security-score" class="text-3xl font-bold text-green-600">98.7</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="shield-check" class="w-6 h-6 text-green-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-green-600 font-medium">Excellent</span>
                    <span class="text-gray-600 ml-2">Security Rating</span>
                </div>
            </div>

            <div class="metric-card bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Threats Detected</p>
                        <p id="threats-blocked" class="text-3xl font-bold text-red-600">1,247</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="shield-alert" class="w-6 h-6 text-red-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-red-600 font-medium">+23</span>
                    <span class="text-gray-600 ml-1">in last hour</span>
                </div>
            </div>

            <div class="metric-card bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Data Processed</p>
                        <p id="data-processed" class="text-3xl font-bold text-blue-600">2.4TB</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="database" class="w-6 h-6 text-blue-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-blue-600 font-medium">847 GB/day</span>
                    <span class="text-gray-600 ml-1">average</span>
                </div>
            </div>

            <div class="metric-card bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">System Uptime</p>
                        <p id="system-uptime" class="text-3xl font-bold text-purple-600">99.9%</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="clock" class="w-6 h-6 text-purple-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-purple-600 font-medium">72h 15m</span>
                    <span class="text-gray-600 ml-1">continuous</span>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Network Traffic Overview -->
            <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-900">Network Traffic Overview</h2>
                        <div class="flex items-center gap-2 text-sm text-gray-600">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <span>Real-time</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="chart-container">
                        <!-- Grid Background -->
                        <svg class="chart-grid">
                            <defs>
                                <pattern id="trafficGrid" width="40" height="32" patternUnits="userSpaceOnUse">
                                    <path d="M 40 0 L 0 0 0 32" fill="none" stroke="#e2e8f0" stroke-width="1"/>
                                </pattern>
                            </defs>
                            <rect width="100%" height="100%" fill="url(#trafficGrid)" />
                        </svg>
                        <canvas id="trafficChart" class="chart-canvas"></canvas>
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center pointer-events-none">
                            <div class="text-2xl font-bold text-gray-700" id="current-traffic">847</div>
                            <div class="text-sm text-gray-500">Mbps Current</div>
                        </div>
                    </div>
                    <div class="mt-4 grid grid-cols-3 gap-4 text-sm">
                        <div class="text-center">
                            <div class="font-medium text-gray-900">Peak Today</div>
                            <div class="text-blue-600">1.2 Gbps</div>
                        </div>
                        <div class="text-center">
                            <div class="font-medium text-gray-900">Average</div>
                            <div class="text-green-600">654 Mbps</div>
                        </div>
                        <div class="text-center">
                            <div class="font-medium text-gray-900">Baseline</div>
                            <div class="text-gray-600">423 Mbps</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Status -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <button class="w-full flex items-center gap-3 p-3 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="play" class="w-4 h-4 text-white"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">Start Deep Scan</div>
                                <div class="text-sm text-gray-600">Comprehensive network analysis</div>
                            </div>
                        </button>

                        <button class="w-full flex items-center gap-3 p-3 text-left bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                            <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="download" class="w-4 h-4 text-white"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">Export Report</div>
                                <div class="text-sm text-gray-600">Generate security report</div>
                            </div>
                        </button>

                        <button class="w-full flex items-center gap-3 p-3 text-left bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
                            <div class="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="settings" class="w-4 h-4 text-white"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">Configure Rules</div>
                                <div class="text-sm text-gray-600">Adjust detection settings</div>
                            </div>
                        </button>

                        <button class="w-full flex items-center gap-3 p-3 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                            <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="eye" class="w-4 h-4 text-white"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">View Live Feed</div>
                                <div class="text-sm text-gray-600">Real-time monitoring</div>
                            </div>
                        </button>
                    </div>

                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center gap-2 mb-2">
                            <i data-lucide="info" class="w-4 h-4 text-blue-600"></i>
                            <span class="text-sm font-medium text-gray-800">System Status</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">IDS Engine</span>
                                <span class="text-green-600 font-medium">Active</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Database</span>
                                <span class="text-green-600 font-medium">Connected</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Network Interface</span>
                                <span class="text-green-600 font-medium">wlan0</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Last Update</span>
                                <span class="text-gray-600">2 seconds ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity & Threat Summary -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Security Events -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Recent Security Events</h2>
                    <p class="text-sm text-gray-600">Latest detected threats and activities</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex items-start gap-3 p-3 bg-red-50 rounded-lg">
                            <div class="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="shield-alert" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium text-red-800">DDoS Attack Detected</div>
                                <div class="text-sm text-red-700">High-volume traffic from 203.0.113.15</div>
                                <div class="text-xs text-red-600 mt-1">2 minutes ago</div>
                            </div>
                        </div>

                        <div class="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                            <div class="w-8 h-8 bg-orange-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="bot" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium text-orange-800">Botnet Activity</div>
                                <div class="text-sm text-orange-700">Suspicious patterns from 198.51.100.42</div>
                                <div class="text-xs text-orange-600 mt-1">5 minutes ago</div>
                            </div>
                        </div>

                        <div class="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                            <div class="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="key" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium text-yellow-800">Brute Force Attempt</div>
                                <div class="text-sm text-yellow-700">Multiple login attempts detected</div>
                                <div class="text-xs text-yellow-600 mt-1">8 minutes ago</div>
                            </div>
                        </div>

                        <div class="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                            <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                                <i data-lucide="check" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <div class="font-medium text-green-800">Threat Mitigated</div>
                                <div class="text-sm text-green-700">DoS attack successfully blocked</div>
                                <div class="text-xs text-green-600 mt-1">12 minutes ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Threat Distribution -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Threat Distribution</h2>
                    <p class="text-sm text-gray-600">Attack types detected today</p>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">DDoS Attacks</span>
                                <span class="text-sm text-gray-600">45%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-red-600 h-2 rounded-full" style="width: 45%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">DoS Attacks</span>
                                <span class="text-sm text-gray-600">28%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-orange-500 h-2 rounded-full" style="width: 28%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Botnet Activity</span>
                                <span class="text-sm text-gray-600">15%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 15%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Brute Force</span>
                                <span class="text-sm text-gray-600">12%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 12%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 text-center">
                        <div class="text-2xl font-bold text-gray-900">1,247</div>
                        <div class="text-sm text-gray-600">Total Threats Blocked Today</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Dashboard metrics tracking
        let metrics = {
            securityScore: 98.7,
            threatsBlocked: 1247,
            dataProcessed: 2.4,
            systemUptime: 99.9,
            currentTraffic: 847
        };

        // Chart data storage
        let trafficData = [];
        let timeLabels = [];
        let trafficChart;

        // Initialize traffic chart
        function initializeTrafficChart() {
            // Generate initial data points (60 seconds)
            const now = Date.now();
            for (let i = 59; i >= 0; i--) {
                timeLabels.push('');
                trafficData.push(Math.random() * 400 + 600); // Random data between 600-1000 Mbps
            }

            // Traffic Chart
            const trafficCtx = document.getElementById('trafficChart').getContext('2d');
            trafficChart = new Chart(trafficCtx, {
                type: 'line',
                data: {
                    labels: timeLabels,
                    datasets: [{
                        data: trafficData,
                        borderColor: '#3b82f6',
                        backgroundColor: function(context) {
                            const chart = context.chart;
                            const {ctx, chartArea} = chart;
                            if (!chartArea) return null;

                            const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom);
                            gradient.addColorStop(0, 'rgba(59, 130, 246, 0.3)');
                            gradient.addColorStop(0.5, 'rgba(59, 130, 246, 0.2)');
                            gradient.addColorStop(1, 'rgba(59, 130, 246, 0.1)');
                            return gradient;
                        },
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: '#ffffff',
                            titleColor: '#1f2937',
                            bodyColor: '#1f2937',
                            borderColor: '#e5e7eb',
                            borderWidth: 1,
                            displayColors: false,
                            callbacks: {
                                label: function(context) {
                                    return Math.round(context.parsed.y) + ' Mbps';
                                }
                            }
                        }
                    },
                    scales: {
                        x: { display: false },
                        y: {
                            display: false,
                            min: 0,
                            max: 1200
                        }
                    },
                    elements: {
                        line: { borderJoinStyle: 'round' }
                    },
                    animation: false
                }
            });
        }

        function updateTrafficChart() {
            // Add new data point
            trafficData.push(metrics.currentTraffic);
            timeLabels.push('');

            // Remove old data (keep last 60 points)
            if (trafficData.length > 60) {
                trafficData.shift();
                timeLabels.shift();
            }

            // Update chart
            trafficChart.update('none');
        }

        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', { hour12: false });
            document.getElementById('current-time').textContent = timeString;
        }

        function updateMetrics() {
            // Simulate slight fluctuations in metrics
            metrics.securityScore += (Math.random() - 0.5) * 0.2;
            metrics.securityScore = Math.max(95, Math.min(100, metrics.securityScore));

            // Threats blocked increases occasionally
            if (Math.random() < 0.3) {
                metrics.threatsBlocked += Math.floor(Math.random() * 3) + 1;
            }

            metrics.dataProcessed += (Math.random() * 0.1);

            metrics.currentTraffic += (Math.random() - 0.5) * 100;
            metrics.currentTraffic = Math.max(400, Math.min(1200, metrics.currentTraffic));

            // Update display
            document.getElementById('security-score').textContent = metrics.securityScore.toFixed(1);
            document.getElementById('threats-blocked').textContent = metrics.threatsBlocked.toLocaleString();
            document.getElementById('data-processed').textContent = metrics.dataProcessed.toFixed(1) + 'TB';
            document.getElementById('current-traffic').textContent = Math.round(metrics.currentTraffic);

            // Update traffic chart
            updateTrafficChart();
        }

        function updateLastUpdateTime() {
            const statusElements = document.querySelectorAll('[data-last-update]');
            statusElements.forEach(element => {
                const seconds = Math.floor(Math.random() * 10) + 1;
                element.textContent = `${seconds} seconds ago`;
            });
        }

        // Start the simulation
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize traffic chart
            initializeTrafficChart();

            // Update time every second
            setInterval(updateCurrentTime, 1000);

            // Update metrics every 3 seconds
            setInterval(updateMetrics, 3000);

            // Update last update time every 3 seconds
            setInterval(updateLastUpdateTime, 3000);

            // Initial updates
            updateCurrentTime();
            updateMetrics();
        });
    </script>
</body>
</html>

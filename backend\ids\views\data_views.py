
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta
from django.utils import timezone
from django.http import HttpResponse
import csv
import io
import psutil

from ..models import ID<PERSON>lert, IDSLog
from ..serializers import IDSLogSerializer
from ..ids_manager import IDSManager

class IDSAlertsView(APIView):
    def get(self, request):
        # Get query parameters
        status_filter = request.GET.get('status', 'new')  # Default to 'new' alerts only
        limit = int(request.GET.get('limit', 30))
        since_id = request.GET.get('since_id')  # Get alerts newer than this ID

        # Build query
        query = IDSAlert.objects.all()

        # Filter by status if specified
        if status_filter and status_filter != 'all':
            query = query.filter(status=status_filter)

        # Filter by ID if since_id is provided (for incremental updates)
        if since_id:
            try:
                query = query.filter(id__gt=int(since_id))
            except (ValueError, TypeError):
                pass  # Ignore invalid since_id

        # Order and limit
        alerts = query.order_by('-timestamp')[:limit]

        # Transform to the format expected by the frontend
        alert_data = []
        for alert in alerts:
            alert_data.append({
                'id': alert.id,
                'timestamp': alert.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'type': alert.attack_type,
                'source': f"{alert.src_ip}:{alert.src_port}",
                'target': f"{alert.dst_ip}:{alert.dst_port}",
                'status': alert.status
            })

        return Response({'alerts': alert_data}, status=status.HTTP_200_OK)

    def patch(self, request):
        """Update alert status (e.g., mark as acknowledged)"""
        try:
            alert_ids = request.data.get('alert_ids', [])
            new_status = request.data.get('status', 'acknowledged')

            if not alert_ids:
                return Response({'error': 'No alert IDs provided'}, status=status.HTTP_400_BAD_REQUEST)

            # Update alerts
            updated_count = IDSAlert.objects.filter(id__in=alert_ids).update(status=new_status)

            return Response({
                'success': True,
                'updated_count': updated_count,
                'message': f'Updated {updated_count} alerts to {new_status}'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class IDSLogsView(APIView):
    def get(self, request):
        logs = IDSLog.objects.all().order_by('-timestamp')[:100]  # Get the 100 most recent logs
        serializer = IDSLogSerializer(logs, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)



class IDSDataCleanView(APIView):
    """API endpoint to clean IDS dataset (alerts and logs)"""

    def post(self, request):
        """Clean dataset based on provided parameters"""
        try:
            # Get parameters from request
            clean_type = request.data.get('type', 'all')  # 'all', 'alerts', 'logs', 'by_date'
            alerts_days = request.data.get('alerts_days', 30)
            logs_days = request.data.get('logs_days', 7)

            result = {
                'success': True,
                'message': '',
                'deleted': {
                    'alerts': 0,
                    'logs': 0
                },
                'remaining': {
                    'alerts': 0,
                    'logs': 0
                }
            }

            if clean_type == 'all':
                # Delete all alerts and logs
                alerts_count = IDSAlert.objects.count()
                logs_count = IDSLog.objects.count()

                IDSAlert.objects.all().delete()
                IDSLog.objects.all().delete()

                result['deleted']['alerts'] = alerts_count
                result['deleted']['logs'] = logs_count
                result['message'] = f'Deleted all data: {alerts_count} alerts and {logs_count} logs'

            elif clean_type == 'alerts':
                # Delete all alerts only
                alerts_count = IDSAlert.objects.count()
                IDSAlert.objects.all().delete()

                result['deleted']['alerts'] = alerts_count
                result['remaining']['logs'] = IDSLog.objects.count()
                result['message'] = f'Deleted all alerts: {alerts_count} alerts'

            elif clean_type == 'logs':
                # Delete all logs only
                logs_count = IDSLog.objects.count()
                IDSLog.objects.all().delete()

                result['deleted']['logs'] = logs_count
                result['remaining']['alerts'] = IDSAlert.objects.count()
                result['message'] = f'Deleted all logs: {logs_count} logs'

            elif clean_type == 'by_date':
                # Delete by date criteria
                now = timezone.now()
                alerts_cutoff = now - timedelta(days=alerts_days)
                logs_cutoff = now - timedelta(days=logs_days)

                # Build alert query
                alert_query = IDSAlert.objects.filter(timestamp__lt=alerts_cutoff)

                # Build log query
                log_query = IDSLog.objects.filter(timestamp__lt=logs_cutoff)

                alerts_deleted = alert_query.count()
                logs_deleted = log_query.count()

                # Delete the data
                alert_query.delete()
                log_query.delete()

                result['deleted']['alerts'] = alerts_deleted
                result['deleted']['logs'] = logs_deleted
                result['message'] = f'Deleted {alerts_deleted} old alerts and {logs_deleted} old logs'

            # Get remaining counts
            result['remaining']['alerts'] = IDSAlert.objects.count()
            result['remaining']['logs'] = IDSLog.objects.count()

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error cleaning dataset: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, request):
        """Get current dataset statistics"""
        try:
            alerts_count = IDSAlert.objects.count()
            logs_count = IDSLog.objects.count()

            # Get oldest and newest alert timestamps
            oldest_alert = IDSAlert.objects.order_by('timestamp').first()
            newest_alert = IDSAlert.objects.order_by('-timestamp').first()

            # Get oldest and newest log timestamps
            oldest_log = IDSLog.objects.order_by('timestamp').first()
            newest_log = IDSLog.objects.order_by('-timestamp').first()

            # Get attack type distribution
            attack_type_stats = {}
            attack_types = IDSAlert.objects.values_list('attack_type', flat=True).distinct()
            for attack_type in attack_types:
                attack_type_stats[attack_type] = IDSAlert.objects.filter(attack_type=attack_type).count()

            response_data = {
                'alerts': {
                    'total': alerts_count,
                    'oldest': oldest_alert.timestamp.isoformat() if oldest_alert else None,
                    'newest': newest_alert.timestamp.isoformat() if newest_alert else None,
                    'attack_type_distribution': attack_type_stats
                },
                'logs': {
                    'total': logs_count,
                    'oldest': oldest_log.timestamp.isoformat() if oldest_log else None,
                    'newest': newest_log.timestamp.isoformat() if newest_log else None
                }
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Error getting dataset statistics: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class IDSTrafficView(APIView):
    def get(self, request):
        """
        Return traffic data for the traffic chart
        """
        # Get timeframe from query parameters (default to '1h')
        timeframe = request.query_params.get('timeframe', '1h')

        # Generate realistic traffic data for the requested timeframe
        now = datetime.now()
        traffic_data = []

        # Determine number of data points and interval based on timeframe
        if timeframe == '1h':
            # 12 data points at 5-minute intervals
            num_points = 12
            interval_minutes = 5
        elif timeframe == '6h':
            # 12 data points at 30-minute intervals
            num_points = 12
            interval_minutes = 30
        elif timeframe == '24h':
            # 24 data points at 1-hour intervals
            num_points = 24
            interval_minutes = 60
        elif timeframe == '7d':
            # 7 data points at 1-day intervals
            num_points = 7
            interval_minutes = 1440  # 24 hours * 60 minutes
        else:
            # Default to 1 hour (12 points at 5-minute intervals)
            num_points = 12
            interval_minutes = 5

        # Get IDS manager for real data
        ids_manager = IDSManager()

        # Use real flow history if IDS is running
        if ids_manager.is_running and ids_manager.ids is not None:
            # Get real flow history from IDS
            flow_history = ids_manager.ids.get_flow_history(timeframe)

            if flow_history:
                # Use real historical data
                for record in flow_history:
                    # Calculate realistic bandwidth utilization
                    bandwidth_util = self._calculate_bandwidth_utilization(record['flow_rate'])

                    traffic_data.append({
                        'time': record['time_str'],
                        'timestamp': record['timestamp'].isoformat(),
                        'traffic': record['total_flows'],
                        'anomaly': record['malicious_flows'],
                        'packet_rate': record['flow_rate'],
                        'bandwidth_utilization': round(bandwidth_util, 2),
                        'total_packets': record['total_flows']
                    })

                # If we have real data, use it and skip simulation
                if traffic_data:
                    pass  # We have real data, continue to response
                else:
                    # No historical data yet, generate minimal current data
                    stats = ids_manager.ids.stats
                    current_time = now.strftime('%H:%M')

                    traffic_data.append({
                        'time': current_time,
                        'timestamp': now.isoformat(),
                        'traffic': stats.get('total_flows', 0),
                        'anomaly': stats.get('malicious_flows', 0),
                        'packet_rate': 0,
                        'bandwidth_utilization': 0,
                        'total_packets': stats.get('total_flows', 0)
                    })
            else:
                # No flow history yet, show current stats
                stats = ids_manager.ids.stats
                current_time = now.strftime('%H:%M')

                traffic_data.append({
                    'time': current_time,
                    'timestamp': now.isoformat(),
                    'traffic': stats.get('total_flows', 0),
                    'anomaly': stats.get('malicious_flows', 0),
                    'packet_rate': 0,
                    'bandwidth_utilization': 0,
                    'total_packets': stats.get('total_flows', 0)
                })
        else:
            # IDS not running - generate realistic demo data for visualization
            import random
            for i in range(min(num_points, 12)):  # Show more points for better visualization
                time_point = now - timedelta(minutes=(11-i)*5)  # 5-minute intervals
                time_str = time_point.strftime('%H:%M')

                # Generate realistic demo values
                base_packets = 150 + random.randint(-50, 100)  # Base packet count
                packet_rate = 25 + random.randint(-10, 15)  # Packets per second
                anomaly_count = random.randint(0, 5)  # Small number of anomalies
                bandwidth_util = random.uniform(5, 25)  # 5-25% bandwidth utilization

                traffic_data.append({
                    'time': time_str,
                    'timestamp': time_point.isoformat(),
                    'traffic': base_packets,
                    'anomaly': anomaly_count,
                    'packet_rate': packet_rate,
                    'bandwidth_utilization': round(bandwidth_util, 2),
                    'total_packets': base_packets
                })

        # Get real protocol distribution data
        protocol_data = self._get_real_protocol_distribution(ids_manager)

        # Calculate packet statistics
        packet_stats = self._calculate_packet_statistics(traffic_data, ids_manager)

        return Response({
            'traffic_data': traffic_data,
            'protocol_data': protocol_data,
            'packet_stats': packet_stats,
            'timeframe': timeframe,
            'total_points': len(traffic_data)
        }, status=status.HTTP_200_OK)

    def _get_real_protocol_distribution(self, ids_manager):
        """Get real protocol distribution from IDS statistics"""
        protocol_data = []

        if ids_manager.is_running and ids_manager.ids is not None:
            # Get real protocol stats from IDS
            stats = ids_manager.ids.stats
            protocol_counts = stats.get('protocol_distribution', {})

            if protocol_counts:
                # Use real data
                total_packets = sum(protocol_counts.values())

                for protocol, count in protocol_counts.items():
                    percent = round((count / total_packets) * 100) if total_packets > 0 else 0
                    protocol_data.append({
                        'name': protocol.upper(),
                        'value': count,
                        'percent': percent
                    })
            else:
                # If no protocol data yet, return empty distribution
                protocol_data = [
                    {'name': 'TCP', 'value': 0, 'percent': 0},
                    {'name': 'UDP', 'value': 0, 'percent': 0},
                    {'name': 'ICMP', 'value': 0, 'percent': 0},
                    {'name': 'Other', 'value': 0, 'percent': 0}
                ]
        else:
            # IDS not running - return demo distribution
            import random
            tcp_count = random.randint(800, 1200)
            udp_count = random.randint(200, 400)
            icmp_count = random.randint(10, 50)
            other_count = random.randint(5, 25)
            total = tcp_count + udp_count + icmp_count + other_count

            protocol_data = [
                {'name': 'TCP', 'value': tcp_count, 'percent': round((tcp_count/total)*100)},
                {'name': 'UDP', 'value': udp_count, 'percent': round((udp_count/total)*100)},
                {'name': 'ICMP', 'value': icmp_count, 'percent': round((icmp_count/total)*100)},
                {'name': 'Other', 'value': other_count, 'percent': round((other_count/total)*100)}
            ]

        return protocol_data

    def _calculate_packet_statistics(self, traffic_data, ids_manager):
        """Calculate packet statistics for the current dataset"""
        if not traffic_data:
            return {
                'avg_packet_rate': 0,
                'peak_packet_rate': 0,
                'total_packets_period': 0,
                'avg_bandwidth_util': 0,
                'peak_bandwidth_util': 0,
                'packet_distribution': {
                    'tcp_percent': 0,
                    'udp_percent': 0,
                    'icmp_percent': 0,
                    'other_percent': 0
                }
            }

        # Calculate statistics from traffic data
        packet_rates = [point['packet_rate'] for point in traffic_data]
        bandwidth_utils = [point['bandwidth_utilization'] for point in traffic_data]
        total_packets = sum(point['total_packets'] for point in traffic_data)



        return {
            'avg_packet_rate': round(sum(packet_rates) / len(packet_rates), 2) if packet_rates else 0,
            'peak_packet_rate': round(max(packet_rates), 2) if packet_rates else 0,
            'total_packets_period': total_packets,
            'avg_bandwidth_util': round(sum(bandwidth_utils) / len(bandwidth_utils), 2) if bandwidth_utils else 0,
            'peak_bandwidth_util': round(max(bandwidth_utils), 2) if bandwidth_utils else 0,
            'packet_distribution': {
                'tcp_percent': 70,  # Demo values
                'udp_percent': 25,
                'icmp_percent': 3,
                'other_percent': 2
            }
        }

    def _calculate_bandwidth_utilization(self, packet_rate):
        """Calculate realistic bandwidth utilization based on packet rate"""
        try:
            # Estimate bytes per flow (average packet size * packets per flow)
            avg_packet_size = 1500  # Average Ethernet frame size in bytes
            packets_per_flow = 10   # Estimated packets per flow
            bytes_per_flow = avg_packet_size * packets_per_flow

            # Calculate bytes per second from packet rate
            packets_per_second = packet_rate / 60.0  # Convert from packets/min to packets/sec
            bytes_per_second = packets_per_second * avg_packet_size

            # Try to get actual network interface capacity
            interface_capacity_bytes_per_sec = self._get_network_interface_capacity()

            # Calculate bandwidth utilization percentage
            if interface_capacity_bytes_per_sec > 0:
                bandwidth_util = (bytes_per_second / interface_capacity_bytes_per_sec) * 100
                # Cap at 100% and add some realistic variation
                bandwidth_util = min(100, bandwidth_util)
            else:
                # Fallback: use a more realistic calculation
                # For typical home/office networks, show lower utilization
                bandwidth_util = min(25, packet_rate * 0.5)  # Much more conservative

            return round(bandwidth_util, 2)
        except Exception:
            # Fallback to conservative estimate
            return round(min(25, packet_rate * 0.3), 2)

    def _get_network_interface_capacity(self):
        """Get network interface capacity in bytes per second"""
        try:
            # Try to get network interface stats
            net_if_stats = psutil.net_if_stats()

            # Find the first active interface with speed info
            for interface_name, stats in net_if_stats.items():
                if stats.isup and stats.speed > 0:
                    # Convert from Mbps to bytes per second
                    # speed is in Mbps, convert to bytes/sec: Mbps * 1,000,000 / 8
                    capacity_bytes_per_sec = (stats.speed * 1_000_000) // 8
                    return capacity_bytes_per_sec

            # Fallback: assume 100 Mbps (typical for many networks)
            return 12_500_000  # 100 Mbps in bytes/sec
        except Exception:
            # Fallback: assume 100 Mbps
            return 12_500_000


class AlertsExportView(APIView):
    """Export alerts data in CSV format"""

    def get(self, request):
        """Export alerts data as CSV"""
        try:
            # Get query parameters
            status_filter = request.query_params.get('status', 'all')
            limit = request.query_params.get('limit', '1000')  # Default to 1000 alerts
            export_format = request.query_params.get('format', 'csv')

            if export_format != 'csv':
                return Response(
                    {"error": "Only CSV format is currently supported"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Parse limit
            try:
                limit = int(limit)
                if limit <= 0:
                    limit = 1000
            except (ValueError, TypeError):
                limit = 1000

            # Get alerts data
            alerts_data = self._get_alerts_data(status_filter, limit)

            # Generate CSV content
            csv_content = self._generate_csv(alerts_data)

            # Create HTTP response with CSV content
            response = HttpResponse(csv_content, content_type='text/csv')
            filename = f'security_alerts_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            import logging
            logger = logging.getLogger('django')
            logger.error(f"Error in alerts export: {str(e)}")

            return Response(
                {"error": "An error occurred while exporting alerts data"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _get_alerts_data(self, status_filter, limit):
        """Get alerts data for export"""
        # Build query
        query = IDSAlert.objects.all()

        # Filter by status if specified
        if status_filter and status_filter != 'all':
            query = query.filter(status=status_filter)

        # Order by timestamp (newest first) and limit
        alerts = query.order_by('-timestamp')[:limit]

        # Convert to list of dictionaries
        alerts_data = []
        for alert in alerts:
            alerts_data.append({
                'id': alert.id,
                'timestamp': alert.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'attack_type': alert.attack_type,
                'src_ip': alert.src_ip,
                'src_port': alert.src_port,
                'dst_ip': alert.dst_ip,
                'dst_port': alert.dst_port,
                'status': alert.status,
                'created_at': alert.created_at.strftime('%Y-%m-%d %H:%M:%S') if hasattr(alert, 'created_at') and alert.created_at else ''
            })

        return alerts_data

    def _generate_csv(self, alerts_data):
        """Generate CSV content from alerts data"""
        output = io.StringIO()

        # Write header information
        output.write(f"# Flow Sentinel IDS Security Alerts Report\n")
        output.write(f"# Generated: {datetime.now().isoformat()}\n")
        output.write(f"# Total Alerts: {len(alerts_data)}\n")
        output.write("\n")

        # Write CSV headers
        fieldnames = [
            'ID', 'Timestamp', 'Attack Type', 'Source IP', 'Source Port',
            'Destination IP', 'Destination Port', 'Status', 'Created At'
        ]

        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()

        # Write alert data
        for alert in alerts_data:
            writer.writerow({
                'ID': alert['id'],
                'Timestamp': alert['timestamp'],
                'Attack Type': alert['attack_type'],
                'Source IP': alert['src_ip'],
                'Source Port': alert['src_port'],
                'Destination IP': alert['dst_ip'],
                'Destination Port': alert['dst_port'],
                'Status': alert['status'],
                'Created At': alert['created_at']
            })

        return output.getvalue()




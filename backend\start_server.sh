#!/bin/bash

# Flow Sentinel IDS Server Startup Script
# This script starts the Django server with the necessary permissions for packet capture

echo "=== Flow Sentinel IDS Server Startup ==="
echo "Starting Django server with packet capture permissions..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "This script needs to be run with sudo for packet capture permissions."
    echo "Usage: sudo ./start_server.sh"
    exit 1
fi

# Get the original user (the one who called sudo)
ORIGINAL_USER=${SUDO_USER:-$USER}
ORIGINAL_HOME=$(eval echo ~$ORIGINAL_USER)

echo "Running as root for packet capture permissions"
echo "Original user: $ORIGINAL_USER"

# Change to the backend directory
cd "$(dirname "$0")"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
else
    echo "Warning: Virtual environment not found. Using system Python."
fi

# Set environment variables
export DJANGO_SETTINGS_MODULE=flow_sentinel.settings

# Check if database needs migration
echo "Checking database..."
python manage.py migrate --check 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Running database migrations..."
    python manage.py migrate
fi

# Start the Django development server
echo "Starting Django server on 0.0.0.0:8000..."
echo "Access the application at: http://localhost:8000"
echo "Press Ctrl+C to stop the server"
echo ""

# Run the server with proper permissions using daphne (ASGI server)
./venv/bin/daphne flow_sentinel.asgi:application -b 0.0.0.0 -p 8000

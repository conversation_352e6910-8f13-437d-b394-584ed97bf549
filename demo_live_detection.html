<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flow Sentinel - Live Real-Time Detection Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .animate-pulse-slow {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .animate-bounce-slow {
            animation: bounce 3s infinite;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .alert-item {
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .status-indicator {
            position: relative;
        }
        
        .status-indicator::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
        }
        
        .status-safe::after {
            background-color: rgb(34, 197, 94);
            opacity: 0.75;
        }
        
        .status-danger::after {
            background-color: rgb(239, 68, 68);
            opacity: 0.75;
        }
        
        @keyframes ping {
            75%, 100% {
                transform: scale(2);
                opacity: 0;
            }
        }
        
        .threat-bar {
            transition: width 0.8s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="shield" class="w-5 h-5 text-white"></i>
                    </div>
                    <h1 class="text-xl font-semibold text-gray-900">Flow Sentinel</h1>
                </div>
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2 text-sm text-gray-600">
                        <div class="w-2 h-2 bg-green-500 rounded-full status-indicator status-safe"></div>
                        <span>Live Monitoring Active</span>
                    </div>
                    <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg">
                        <i data-lucide="bell" class="w-5 h-5"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="p-6 max-w-7xl mx-auto">
        <!-- Security Status Alert -->
        <div id="security-status" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <i data-lucide="shield-alert" class="w-6 h-6 text-red-600 animate-pulse-slow"></i>
                    <div>
                        <h3 class="text-lg font-semibold text-red-800">Security Status: UNDER ATTACK</h3>
                        <p class="text-red-700">Multiple attack types detected: DDoS, DoS, Botnet, and Brute Force</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-full">
                        UNDER ATTACK
                    </div>
                    <div class="text-sm text-red-600 mt-1">
                        Duration: <span id="attack-duration">00:02:34</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white p-6 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Alerts</p>
                        <p id="total-alerts" class="text-2xl font-bold text-gray-900">247</p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="alert-triangle" class="w-6 h-6 text-red-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-red-600 font-medium">+12</span>
                    <span class="text-gray-600 ml-1">in last minute</span>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Active Threats</p>
                        <p id="active-threats" class="text-2xl font-bold text-gray-900">4</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="shield-alert" class="w-6 h-6 text-orange-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-orange-600 font-medium">DDoS, DoS, Botnet</span>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Packets Analyzed</p>
                        <p id="packets-analyzed" class="text-2xl font-bold text-gray-900">1.2M</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="activity" class="w-6 h-6 text-blue-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-green-600 font-medium">+15.2K/sec</span>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Detection Rate</p>
                        <p id="detection-rate" class="text-2xl font-bold text-gray-900">98.7%</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="target" class="w-6 h-6 text-green-600"></i>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm">
                    <span class="text-green-600 font-medium">High Accuracy</span>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Real-Time Alerts Panel -->
            <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-900">Real-Time Alerts</h2>
                        <div class="flex items-center gap-2 text-sm text-gray-600">
                            <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                            <span>Live Feed</span>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div id="alerts-container" class="space-y-3 max-h-96 overflow-y-auto">
                        <!-- Alerts will be dynamically added here -->
                    </div>
                </div>
            </div>

            <!-- Threat Detection Panel -->
            <div class="bg-white rounded-lg border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Threat Analysis</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">DDoS Attacks</span>
                                <span class="text-sm text-gray-600">45%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-red-600 h-2 rounded-full threat-bar" style="width: 45%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">DoS Attacks</span>
                                <span class="text-sm text-gray-600">28%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-orange-500 h-2 rounded-full threat-bar" style="width: 28%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Botnet Activity</span>
                                <span class="text-sm text-gray-600">15%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full threat-bar" style="width: 15%"></div>
                            </div>
                        </div>

                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Brute Force</span>
                                <span class="text-sm text-gray-600">12%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full threat-bar" style="width: 12%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-red-50 rounded-lg">
                        <div class="flex items-center gap-2 mb-2">
                            <i data-lucide="alert-circle" class="w-4 h-4 text-red-600"></i>
                            <span class="text-sm font-medium text-red-800">Critical Alert</span>
                        </div>
                        <p class="text-sm text-red-700">
                            Multiple attack vectors detected: DDoS, DoS, and Botnet activity.
                            Automatic mitigation protocols activated.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Network Activity Monitor -->
        <div class="mt-6 bg-white rounded-lg border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Network Activity Monitor</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600" id="packets-per-sec">15,247</div>
                        <div class="text-sm text-gray-600">Packets/sec</div>
                        <div class="mt-2 text-xs text-green-600">↗ +12% from baseline</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-orange-600" id="bandwidth-usage">847</div>
                        <div class="text-sm text-gray-600">Mbps</div>
                        <div class="mt-2 text-xs text-red-600">↗ +340% from baseline</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600" id="unique-ips">1,234</div>
                        <div class="text-sm text-gray-600">Unique IPs</div>
                        <div class="mt-2 text-xs text-red-600">↗ +89% from baseline</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Sample alert data for simulation
        const alertTypes = [
            { type: 'DDoS Attack', severity: 'high', icon: 'shield-alert' },
            { type: 'DoS Attack', severity: 'high', icon: 'shield-x' },
            { type: 'Botnet Activity', severity: 'high', icon: 'bot' },
            { type: 'Brute Force', severity: 'high', icon: 'key' }
        ];

        const sourceIPs = [
            '***********00', '*********', '***********', '************',
            '*************', '**********', '*********', '************'
        ];

        const targetIPs = [
            '***********', '********', '**********', '************'
        ];

        // Statistics tracking
        let stats = {
            totalAlerts: 247,
            activeThreats: 4,
            packetsAnalyzed: 1200000,
            detectionRate: 98.7,
            packetsPerSec: 15247,
            bandwidthUsage: 847,
            uniqueIPs: 1234
        };

        // Attack duration timer
        let attackStartTime = Date.now() - (2 * 60 + 34) * 1000; // Started 2:34 ago

        function updateAttackDuration() {
            const elapsed = Date.now() - attackStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('attack-duration').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        function getSeverityColor(severity) {
            switch(severity) {
                case 'high': return 'border-red-200 bg-red-50';
                case 'medium': return 'border-orange-200 bg-orange-50';
                case 'low': return 'border-yellow-200 bg-yellow-50';
                default: return 'border-gray-200 bg-gray-50';
            }
        }

        function getSeverityTextColor(severity) {
            switch(severity) {
                case 'high': return 'text-red-800';
                case 'medium': return 'text-orange-800';
                case 'low': return 'text-yellow-800';
                default: return 'text-gray-800';
            }
        }

        function getSeverityBadgeColor(severity) {
            switch(severity) {
                case 'high': return 'bg-red-600 text-white';
                case 'medium': return 'bg-orange-600 text-white';
                case 'low': return 'bg-yellow-600 text-white';
                default: return 'bg-gray-600 text-white';
            }
        }

        function generateAlert() {
            const alert = alertTypes[Math.floor(Math.random() * alertTypes.length)];
            const sourceIP = sourceIPs[Math.floor(Math.random() * sourceIPs.length)];
            const targetIP = targetIPs[Math.floor(Math.random() * targetIPs.length)];
            const timestamp = new Date().toLocaleTimeString();

            return {
                ...alert,
                sourceIP,
                targetIP,
                timestamp,
                id: Date.now() + Math.random()
            };
        }

        function addAlert(alert) {
            const container = document.getElementById('alerts-container');
            const alertElement = document.createElement('div');
            alertElement.className = `alert-item p-4 border rounded-lg ${getSeverityColor(alert.severity)}`;

            alertElement.innerHTML = `
                <div class="flex items-start justify-between">
                    <div class="flex items-start gap-3">
                        <div class="w-8 h-8 ${getSeverityBadgeColor(alert.severity)} rounded-lg flex items-center justify-center">
                            <i data-lucide="${alert.icon}" class="w-4 h-4"></i>
                        </div>
                        <div>
                            <div class="font-medium ${getSeverityTextColor(alert.severity)}">${alert.type}</div>
                            <div class="text-sm text-gray-600 mt-1">
                                From: <span class="font-mono">${alert.sourceIP}</span> →
                                To: <span class="font-mono">${alert.targetIP}</span>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-xs text-gray-500">${alert.timestamp}</div>
                        <div class="text-xs px-2 py-1 rounded-full ${getSeverityBadgeColor(alert.severity)} mt-1">
                            ${alert.severity.toUpperCase()}
                        </div>
                    </div>
                </div>
            `;

            container.insertBefore(alertElement, container.firstChild);

            // Re-initialize icons for the new element
            lucide.createIcons();

            // Remove old alerts to keep the list manageable
            const alerts = container.children;
            if (alerts.length > 10) {
                container.removeChild(alerts[alerts.length - 1]);
            }

            // Update stats
            stats.totalAlerts++;
            document.getElementById('total-alerts').textContent = stats.totalAlerts;
        }

        function updateNetworkStats() {
            // Simulate fluctuating network statistics
            stats.packetsPerSec += Math.floor(Math.random() * 2000) - 1000;
            stats.packetsPerSec = Math.max(10000, Math.min(25000, stats.packetsPerSec));

            stats.bandwidthUsage += Math.floor(Math.random() * 100) - 50;
            stats.bandwidthUsage = Math.max(500, Math.min(1200, stats.bandwidthUsage));

            stats.uniqueIPs += Math.floor(Math.random() * 20) - 10;
            stats.uniqueIPs = Math.max(1000, Math.min(1500, stats.uniqueIPs));

            stats.packetsAnalyzed += stats.packetsPerSec;

            // Update display
            document.getElementById('packets-per-sec').textContent = stats.packetsPerSec.toLocaleString();
            document.getElementById('bandwidth-usage').textContent = stats.bandwidthUsage;
            document.getElementById('unique-ips').textContent = stats.uniqueIPs.toLocaleString();
            document.getElementById('packets-analyzed').textContent =
                (stats.packetsAnalyzed / 1000000).toFixed(1) + 'M';
        }

        // Initialize with some sample alerts
        function initializeAlerts() {
            const initialAlerts = [
                { type: 'DDoS Attack', severity: 'high', icon: 'shield-alert', sourceIP: '************', targetIP: '***********', timestamp: '14:23:45' },
                { type: 'DoS Attack', severity: 'high', icon: 'shield-x', sourceIP: '*************', targetIP: '************', timestamp: '14:23:12' },
                { type: 'Brute Force', severity: 'high', icon: 'key', sourceIP: '**********', targetIP: '***********', timestamp: '14:22:58' },
                { type: 'Botnet Activity', severity: 'high', icon: 'bot', sourceIP: '*********', targetIP: '***********', timestamp: '14:22:34' },
                { type: 'DDoS Attack', severity: 'high', icon: 'shield-alert', sourceIP: '************', targetIP: '***********', timestamp: '14:22:01' }
            ];

            const container = document.getElementById('alerts-container');
            initialAlerts.forEach(alert => {
                const alertElement = document.createElement('div');
                alertElement.className = `alert-item p-4 border rounded-lg ${getSeverityColor(alert.severity)}`;

                alertElement.innerHTML = `
                    <div class="flex items-start justify-between">
                        <div class="flex items-start gap-3">
                            <div class="w-8 h-8 ${getSeverityBadgeColor(alert.severity)} rounded-lg flex items-center justify-center">
                                <i data-lucide="${alert.icon}" class="w-4 h-4"></i>
                            </div>
                            <div>
                                <div class="font-medium ${getSeverityTextColor(alert.severity)}">${alert.type}</div>
                                <div class="text-sm text-gray-600 mt-1">
                                    From: <span class="font-mono">${alert.sourceIP}</span> →
                                    To: <span class="font-mono">${alert.targetIP}</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-xs text-gray-500">${alert.timestamp}</div>
                            <div class="text-xs px-2 py-1 rounded-full ${getSeverityBadgeColor(alert.severity)} mt-1">
                                ${alert.severity.toUpperCase()}
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(alertElement);
            });

            // Re-initialize icons
            lucide.createIcons();
        }

        // Start the simulation
        document.addEventListener('DOMContentLoaded', function() {
            initializeAlerts();

            // Update attack duration every second
            setInterval(updateAttackDuration, 1000);

            // Add new alerts every 3-8 seconds
            setInterval(() => {
                const alert = generateAlert();
                addAlert(alert);
            }, Math.random() * 5000 + 3000);

            // Update network stats every 2 seconds
            setInterval(updateNetworkStats, 2000);

            // Initial stats update
            updateNetworkStats();
        });
    </script>
</body>
</html>

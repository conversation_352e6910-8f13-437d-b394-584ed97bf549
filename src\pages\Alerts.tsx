
import { useState, useEffect } from "react";
import { Navbar } from "@/components/layout/Navbar";
import { Sidebar } from "@/components/layout/Sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, Clock, ExternalLink, Filter, RefreshCw, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Alert {
  id: number;
  timestamp: string;
  type: string;
  source: string;
  sourcePort: string;
  target: string;
  targetPort: string;
  status: string;
  details: string;
}

export default function Alerts() {
  const [selectedAlert, setSelectedAlert] = useState<number | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchAlerts();
  }, []);

  // Format timestamp to a user-friendly format with current timezone
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch (e) {
      return timestamp; // Fallback to original format if parsing fails
    }
  };

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      // Fetch all alerts (including acknowledged ones) for the alerts page
      const response = await fetch('http://localhost:8000/api/ids/alerts?status=all&limit=50', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();

        // Transform API response to expected format
        const formattedAlerts: Alert[] = (data.alerts || []).map((alert: any) => {
          // Parse source and target into parts
          const [src, srcPort] = alert.source.split(':');
          const [tgt, tgtPort] = alert.target.split(':');

          return {
            id: alert.id,
            timestamp: formatTimestamp(alert.timestamp),
            type: alert.type,
            source: src,
            sourcePort: srcPort || 'random',
            target: tgt,
            targetPort: tgtPort || 'multiple',
            status: 'new', // Default to new since backend provides status
            details: `Detected ${alert.type} attack from ${alert.source} to ${alert.target}`
          };
        });

        setAlerts(formattedAlerts);

        // Select first alert if available and none selected
        if (formattedAlerts.length > 0 && selectedAlert === null) {
          setSelectedAlert(formattedAlerts[0].id);
        }
      } else {
        console.error('Failed to fetch alerts:', response.statusText);
        toast({
          title: "Error",
          description: "Failed to fetch alerts from the backend",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error fetching alerts:', error);
      toast({
        title: "Connection Error",
        description: "Could not connect to the IDS backend",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchAlerts();
    toast({
      title: "Refreshing",
      description: "Updating alerts from IDS backend"
    });
  };

  const handleDownloadCSV = async () => {
    try {
      if (alerts.length > 0) {
        // Use frontend generation for current alerts
        const csvData = generateCSVFromAlerts(alerts);

        // Create and download the CSV file
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security_alerts_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        toast({
          title: "CSV Downloaded",
          description: `Exported ${alerts.length} security alerts to CSV file.`
        });
      } else {
        // Check if there are any alerts in the backend database
        const checkResponse = await fetch(`http://localhost:8000/api/ids/alerts?status=all&limit=1`);

        if (checkResponse.ok) {
          const checkData = await checkResponse.json();

          if (checkData.alerts && checkData.alerts.length > 0) {
            // There are alerts in the database, download from backend
            const response = await fetch(`http://localhost:8000/api/ids/alerts/export?status=all&format=csv&limit=1000`);

            if (!response.ok) {
              throw new Error('Failed to download CSV from backend');
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security_alerts_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            toast({
              title: "CSV Downloaded",
              description: "All security alerts data has been exported from database."
            });
          } else {
            // No alerts in database either
            toast({
              title: "No Data to Export",
              description: "There are currently no security alerts to export. Start the IDS to begin detecting threats.",
              variant: "default"
            });
          }
        } else {
          throw new Error('Failed to check for alerts in database');
        }
      }
    } catch (error) {
      console.error('Error generating CSV:', error);
      toast({
        title: "Download Failed",
        description: "Could not generate CSV file. Please try again.",
        variant: "destructive"
      });
    }
  };

  const generateCSVFromAlerts = (alertsData: Alert[]): string => {
    const lines: string[] = [];

    // Add header with metadata
    lines.push(`# Flow Sentinel IDS Security Alerts Report`);
    lines.push(`# Generated: ${new Date().toISOString()}`);
    lines.push(`# Total Alerts: ${alertsData.length}`);
    lines.push('');

    // CSV headers
    lines.push('ID,Timestamp,Attack Type,Source IP,Source Port,Destination IP,Destination Port,Status,Details');

    // Add alert data
    alertsData.forEach(alert => {
      // Escape commas and quotes in CSV data
      const escapeCSV = (value: string) => {
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      };

      lines.push([
        alert.id,
        escapeCSV(alert.timestamp),
        escapeCSV(alert.type),
        escapeCSV(alert.source),
        escapeCSV(alert.sourcePort),
        escapeCSV(alert.target),
        escapeCSV(alert.targetPort),
        escapeCSV(alert.status),
        escapeCSV(alert.details)
      ].join(','));
    });

    return lines.join('\n');
  };







  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return <div className="h-2 w-2 rounded-full bg-security-danger animate-pulse"></div>;
      case 'acknowledged':
        return <div className="h-2 w-2 rounded-full bg-security-warning"></div>;
      case 'resolved':
        return <div className="h-2 w-2 rounded-full bg-security-safe"></div>;
      default:
        return <div className="h-2 w-2 rounded-full bg-muted"></div>;
    }
  };

  return (
    <div className="min-h-screen flex">
      <Sidebar />

      <div className="flex-1 flex flex-col min-h-screen md:pl-64">
        <Navbar />

        <main className="flex-1 p-4 md:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Security Alerts</h1>
              <p className="text-muted-foreground">
                Security threats and incidents
              </p>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownloadCSV}
                disabled={loading}
                title="Download alerts as CSV"
              >
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>

              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Security Alerts</CardTitle>
                <CardDescription>
                  {alerts.length} security alerts detected
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full"></div>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <div className="grid grid-cols-[auto_1fr_1fr_auto] gap-2 p-4 text-xs font-medium text-muted-foreground border-b">
                      <div>Status</div>
                      <div>Type</div>
                      <div>Source → Target</div>
                      <div></div>
                    </div>

                    <div className="max-h-[600px] overflow-auto">
                      {alerts.length === 0 ? (
                        <div className="py-8 text-center text-muted-foreground">
                          No alerts detected
                        </div>
                      ) : (
                        alerts.map((alert) => (
                          <div
                            key={`alert-item-${alert.id}`}
                            className={`grid grid-cols-[auto_1fr_1fr_auto] gap-2 p-4 text-sm border-b hover:bg-muted/50 cursor-pointer ${selectedAlert === alert.id ? 'bg-muted' : ''}`}
                            onClick={() => setSelectedAlert(alert.id)}
                          >
                            <div className="flex items-center">
                              {getStatusIcon(alert.status)}
                            </div>
                            <div className="font-medium">{alert.type}</div>
                            <div className="text-muted-foreground">
                              {alert.source}:{alert.sourcePort} → {alert.target}:{alert.targetPort}
                            </div>
                            <div>
                              <Button variant="ghost" size="icon" className="h-7 w-7">
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Alert Details</CardTitle>
                <CardDescription>
                  {selectedAlert ? 'Selected alert information' : 'Select an alert to view details'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full"></div>
                  </div>
                ) : selectedAlert ? (
                  <div className="space-y-4">
                    {(() => {
                      const alert = alerts.find(a => a.id === selectedAlert);
                      if (!alert) return <div>Alert not found</div>;

                      return (
                        <>
                          <div className="space-y-1">
                            <h3 className="text-lg font-medium flex items-center gap-2">
                              <AlertTriangle className="h-5 w-5 text-destructive" />
                              {alert.type}
                            </h3>
                            <div className="flex items-center text-xs text-muted-foreground">
                              <Clock className="h-3 w-3 mr-1" />
                              {alert.timestamp}
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div className="space-y-1">
                              <div className="text-xs text-muted-foreground">Source</div>
                              <div className="font-medium">{alert.source}</div>
                            </div>
                            <div className="space-y-1">
                              <div className="text-xs text-muted-foreground">Target</div>
                              <div className="font-medium">{alert.target}</div>
                            </div>
                            <div className="space-y-1">
                              <div className="text-xs text-muted-foreground">Source Port</div>
                              <div className="font-medium">{alert.sourcePort}</div>
                            </div>
                            <div className="space-y-1">
                              <div className="text-xs text-muted-foreground">Target Port</div>
                              <div className="font-medium">{alert.targetPort}</div>
                            </div>
                            <div className="space-y-1">
                              <div className="text-xs text-muted-foreground">Status</div>
                              <div className="font-medium capitalize">{alert.status}</div>
                            </div>
                          </div>

                          <div className="space-y-1">
                            <div className="text-xs text-muted-foreground">Details</div>
                            <div className="text-sm">{alert.details}</div>
                          </div>

                          <div className="pt-4 flex gap-2">
                            <Button variant="outline" size="sm" className="w-full">
                              Investigate
                            </Button>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-[300px] text-center p-4">
                    <AlertTriangle className="h-10 w-10 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">No Alert Selected</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      Select an alert from the list to view its details
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}

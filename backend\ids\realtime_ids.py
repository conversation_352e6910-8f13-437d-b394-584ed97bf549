import argparse
import os
import sys
import time
import datetime
from datetime import timed<PERSON><PERSON>
import uuid
import pandas as pd
import numpy as np
import ipaddress
import joblib
import threading
import matplotlib.pyplot as plt
import seaborn as sns
from scapy.all import sniff, wrpcap, IP, TCP, UDP, rdpcap, conf
from collections import defaultdict, Counter, deque
from typing import Dict, List, Tuple, Any, Optional, Set
import warnings
from sklearn.metrics import confusion_matrix
import logging
import queue
import signal
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

# Increase recursion limit to avoid Scapy recursion errors (optional)
sys.setrecursionlimit(10000)
# Disable Scapy's built-in DNS parsing to prevent decompression loops
conf.use_pcap = True

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('real_time_ids.log')
    ]
)
logger = logging.getLogger('RealTimeIDS')

warnings.filterwarnings('ignore')

class RealTimeIDS:
    """
    Real-time Network Intrusion Detection System that combines traffic capture
    with immediate model prediction.
    """

    def __init__(self,
                 model_path: str = './DecisionTree_model.pkl',
                 label_array_path: str = './label_array.npy',
                 feature_columns_path: str = './DecisionTree_feature_columns.txt',
                 scaler_path: str = './scaler.pkl',
                 capture_interval: float = 5.0,
                 max_idle_time: float = 1.0,  # Reduced from 2.0 to 1.0 for faster flow timeout
                 output_dir: str = './ids_results',
                 conn_rate_window: float = 5.0,
                 alert_threshold: float = 0.7,
                 historical_window: int = 60,
                 pcap_capture: bool = True,
                 pcap_save_interval: float = 600.0,
                 consolidated_csv: bool = True,
                 csv_save_interval: float = 300.0):
        """
        Initialize the real-time IDS.

        Args:
            model_path: Path to the saved Decision Tree model
            label_array_path: Path to the saved label array
            feature_columns_path: Path to the feature columns file
            scaler_path: Path to the saved scaler from model training
            capture_interval: Time in seconds for each capture window
            max_idle_time: Maximum time in seconds before a flow is considered complete
            output_dir: Base directory to save output files
            conn_rate_window: Window size in seconds for Conn_Rate calculation
            alert_threshold: Confidence threshold for alerting (0-1)
            historical_window: Window size in seconds for keeping historical alerts
            pcap_capture: Whether to save PCAP files of captured packets
            pcap_save_interval: Time in seconds between PCAP file saves (default: 600 = 10 minutes)
            consolidated_csv: Whether to save all flows in a single CSV file (default: True)
            csv_save_interval: Time in seconds between consolidated CSV saves (default: 300 = 5 minutes)
        """
        self.capture_interval = capture_interval
        self.max_idle_time = max_idle_time
        self.conn_rate_window = conn_rate_window
        self.output_dir = output_dir
        self.alert_threshold = alert_threshold
        self.historical_window = historical_window
        self.pcap_capture = pcap_capture
        self.pcap_save_interval = pcap_save_interval
        self.consolidated_csv = consolidated_csv
        self.csv_save_interval = csv_save_interval

        # Create a unique run ID and output folder
        self.run_id = f"rt_ids_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:6]}"
        self.result_dir = os.path.join(output_dir, self.run_id)
        os.makedirs(self.result_dir, exist_ok=True)

        # Load the model and related components
        self.model, self.label_array, self.feature_columns = self._load_model(
            model_path, label_array_path, feature_columns_path)

        # Load the scaler
        self.scaler = None
        try:
            if scaler_path and os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
                logger.info(f"Loaded scaler from {scaler_path}")
            else:
                logger.warning(f"Scaler not found at {scaler_path}. Features will not be scaled!")
        except Exception as e:
            logger.error(f"Error loading scaler: {str(e)}")
            logger.warning("Will proceed without scaling, but model accuracy may be affected.")

        # Store captured packets for the current window
        self.captured_packets = []

        # Dictionary to store active flows
        self.flows = defaultdict(lambda: {
            'start_time': None,
            'last_timestamp': None,
            'forward_packets': [],  # (timestamp, size, flags)
            'backward_packets': [], # (timestamp, size, flags)
            'protocol': None,
            'src_ip': None,
            'dst_ip': None,
            'src_port': None,
            'dst_port': None,
            'active_start': None,
            'active_times': [],
            'idle_start': None,
            'fwd_win_bytes': None,
            'bwd_win_bytes': None,
        })

        # Keep track of completed flows for the current window
        self.completed_flows = []
        self.timestamp_first_packet = None

        # Port connection tracking for Conn_Rate calculation
        self.port_connections = defaultdict(list)

        # Alert history for reporting
        self.alert_history = deque(maxlen=1000)  # Store recent alerts

        # Flow history for traffic graphs (store last 24 hours of data)
        self.flow_history = deque(maxlen=1440)  # 1440 minutes = 24 hours

        # PCAP saving management
        self.last_pcap_save_time = time.time()
        self.accumulated_packets = []  # Store packets across multiple windows

        # CSV flow accumulation management
        self.last_csv_save_time = time.time()
        self.accumulated_flows = []  # Store flows across multiple windows for consolidated CSV
        self.consolidated_csv_path = os.path.join(self.result_dir, f"all_flows_{self.run_id}.csv")
        self.csv_header_written = False

        # Statistics
        self.stats = {
            'windows_processed': 0,
            'total_packets': 0,
            'total_flows': 0,
            'total_alerts': 0,
            'benign_flows': 0,
            'malicious_flows': 0,
            'attack_types': Counter(),
            'protocol_distribution': Counter(),
            'start_time': time.time(),
        }

        # Attack session tracking
        self.attack_session = {
            'is_active': False,
            'start_time': 0,
            'last_activity_time': 0,
            'attack_types': set(),
            'session_timeout': 10  # 10 seconds of no activity = session ended (reduced from 30)
        }

        # Control flags
        self.running = False
        self.capture_thread = None
        self.analysis_thread = None

        # Thread communication
        self.flow_queue = queue.Queue(maxsize=100)  # Buffer between capture and analysis

        logger.info(f"Real-time IDS initialized with:")
        logger.info(f"  - Capture interval: {capture_interval}s")
        logger.info(f"  - Max idle time: {self.max_idle_time}s")
        logger.info(f"  - PCAP capture: {'ENABLED' if self.pcap_capture else 'DISABLED'}")
        if self.pcap_capture:
            logger.info(f"  - PCAP save interval: {self.pcap_save_interval}s ({self.pcap_save_interval/60:.1f} minutes)")
        logger.info(f"  - Consolidated CSV: {'ENABLED' if self.consolidated_csv else 'DISABLED'}")
        if self.consolidated_csv:
            logger.info(f"  - CSV save interval: {self.csv_save_interval}s ({self.csv_save_interval/60:.1f} minutes)")
        logger.info(f"  - Results directory: {self.result_dir}")

    def _load_model(self, model_path, label_array_path, feature_columns_path):
        """Load the Decision Tree model, label array, and feature columns."""
        # Load model
        try:
            model = joblib.load(model_path)
            logger.info(f"Loaded Decision Tree model from {model_path}")
        except Exception as e:
            raise RuntimeError(f"Error loading model: {str(e)}")

        # Load label array
        try:
            label_array = np.load(label_array_path, allow_pickle=True)
            logger.info(f"Loaded label array with {len(label_array)} classes")
        except Exception as e:
            raise RuntimeError(f"Error loading label array: {str(e)}")

        # Load feature columns
        try:
            with open(feature_columns_path, 'r') as f:
                feature_columns = [line.strip() for line in f.readlines()]
            logger.info(f"Loaded {len(feature_columns)} feature columns")
        except Exception as e:
            raise RuntimeError(f"Error loading feature columns: {str(e)}")

        return model, label_array, feature_columns

    def is_legitimate_traffic(self, src_ip: str, dst_ip: str, src_port: int = 0, dst_port: int = 0) -> bool:
        """
        Check if the traffic is legitimate and should be excluded from attack detection.
        Returns True if traffic should be ignored (legitimate), False if it should be analyzed.
        """
        try:
            src_addr = ipaddress.ip_address(src_ip)
            dst_addr = ipaddress.ip_address(dst_ip)

            # Exclude multicast traffic (*********/4)
            multicast_range = ipaddress.ip_network('*********/4')
            if dst_addr in multicast_range:
                return True

            # Exclude broadcast and system addresses
            if dst_ip in ['0.0.0.0', '***************']:
                return True

            # Exclude SSDP multicast (***************)
            if dst_ip == '***************':
                return True

            # Exclude local broadcast addresses
            if dst_ip.endswith('.255'):
                return True

            # Exclude loopback traffic
            if src_addr.is_loopback or dst_addr.is_loopback:
                return True

            # Exclude link-local addresses (***********/16)
            link_local = ipaddress.ip_network('***********/16')
            if src_addr in link_local or dst_addr in link_local:
                return True

            # Exclude common legitimate multicast ranges
            legitimate_multicast = [
                '*********',    # All Systems multicast
                '*********',    # All Routers multicast
                '*********2',   # IGMP
                '***********',  # mDNS
                '***********',  # LLMNR
                '***************'  # SSDP
            ]
            if dst_ip in legitimate_multicast:
                return True

            # Exclude common legitimate service ports that generate high connection rates
            legitimate_ports = {
                53,    # DNS
                67,    # DHCP Server
                68,    # DHCP Client
                123,   # NTP
                137,   # NetBIOS Name Service
                138,   # NetBIOS Datagram Service
                139,   # NetBIOS Session Service
                445,   # SMB
                1900,  # SSDP
                5353,  # mDNS
                5355,  # LLMNR
            }
            if dst_port in legitimate_ports or src_port in legitimate_ports:
                return True

        except ValueError:
            # Invalid IP address, let it through for analysis
            pass

        return False

    def get_flow_key(self, packet) -> Optional[Tuple]:
        """
        Generate a key for the flow based on the 5-tuple.
        Returns None if the packet is not IP or doesn't have TCP/UDP or is legitimate.
        """
        if IP in packet:
            ip_src = packet[IP].src
            ip_dst = packet[IP].dst

            # Get port information for filtering
            src_port = 0
            dst_port = 0
            if TCP in packet:
                src_port = packet[TCP].sport
                dst_port = packet[TCP].dport
            elif UDP in packet:
                src_port = packet[UDP].sport
                dst_port = packet[UDP].dport

            # Filter out legitimate traffic that shouldn't be analyzed
            if self.is_legitimate_traffic(ip_src, ip_dst, src_port, dst_port):
                return None

            # Try to determine direction based on IP addresses
            src_ip_int = int(ipaddress.ip_address(ip_src))
            dst_ip_int = int(ipaddress.ip_address(ip_dst))

            if TCP in packet:
                proto = 6  # TCP
                sport = packet[TCP].sport
                dport = packet[TCP].dport

                if (src_ip_int > dst_ip_int) or (src_ip_int == dst_ip_int and sport > dport):
                    return (ip_dst, ip_src, dport, sport, proto)
                return (ip_src, ip_dst, sport, dport, proto)

            elif UDP in packet:
                proto = 17  # UDP
                sport = packet[UDP].sport
                dport = packet[UDP].dport

                if (src_ip_int > dst_ip_int) or (src_ip_int == dst_ip_int and sport > dport):
                    return (ip_dst, ip_src, dport, sport, proto)
                return (ip_src, ip_dst, sport, dport, proto)

            else:
                proto = packet[IP].proto
                if src_ip_int > dst_ip_int:
                    return (ip_dst, ip_src, 0, 0, proto)
                return (ip_src, ip_dst, 0, 0, proto)

        return None

    def get_packet_direction(self, packet, flow_key) -> Optional[str]:
        """Determine if packet is in forward or backward direction."""
        if IP in packet:
            src_ip = packet[IP].src

            if src_ip == flow_key[0]:  # Source IP matches first element of key (forward direction)
                return 'forward'
            elif src_ip == flow_key[1]:  # Source IP matches second element of key (backward direction)
                return 'backward'

        return None

    def update_port_connections(self, packet, timestamp):
        """
        Update port connections for Conn_Rate calculation.
        This is critical for the Decision Tree model as Conn_Rate is the most important feature.
        """
        if IP in packet and (TCP in packet or UDP in packet):
            port = packet[TCP].dport if TCP in packet else packet[UDP].dport
            self.port_connections[port].append(timestamp)

            # Clean up old connections outside the window
            window_start = timestamp - self.conn_rate_window
            self.port_connections[port] = [ts for ts in self.port_connections[port] if ts >= window_start]

    def extract_packet_features(self, packet, timestamp, flow_key, flow_data) -> None:
        """Extract features from a packet and update the flow data."""
        direction = self.get_packet_direction(packet, flow_key)
        if direction is None:
            return

        # Initialize start time for new flows
        if flow_data['start_time'] is None:
            flow_data['start_time'] = timestamp
            flow_data['last_timestamp'] = timestamp
            flow_data['active_start'] = timestamp
            flow_data['protocol'] = flow_key[4]
            flow_data['src_ip'] = flow_key[0]
            flow_data['dst_ip'] = flow_key[1]
            flow_data['src_port'] = flow_key[2]
            flow_data['dst_port'] = flow_key[3]

        # Calculate inter-arrival time
        iat = timestamp - flow_data['last_timestamp'] if flow_data['last_timestamp'] else 0
        flow_data['last_timestamp'] = timestamp

        # Check if we're switching from idle to active
        if flow_data['idle_start'] and iat > 1.0:  # 1 second threshold for activity
            idle_time = timestamp - flow_data['idle_start']
            flow_data['idle_start'] = None
            flow_data['active_start'] = timestamp

        # Check if we're switching from active to idle
        if iat > 1.0 and flow_data['active_start']:
            active_time = flow_data['last_timestamp'] - flow_data['active_start']
            flow_data['active_times'].append(active_time)
            flow_data['active_start'] = None
            flow_data['idle_start'] = timestamp

        # Get TCP flags if present
        flags = {}
        if TCP in packet:
            tcp_flags = packet[TCP].flags
            flags = {
                'FIN': 1 if tcp_flags & 0x01 else 0,
                'SYN': 1 if tcp_flags & 0x02 else 0,
                'RST': 1 if tcp_flags & 0x04 else 0,
                'PSH': 1 if tcp_flags & 0x08 else 0,
                'ACK': 1 if tcp_flags & 0x10 else 0,
                'URG': 1 if tcp_flags & 0x20 else 0,
            }

            # Record initial window sizes (critical for the Decision Tree model)
            if flow_data['fwd_win_bytes'] is None and direction == 'forward':
                flow_data['fwd_win_bytes'] = packet[TCP].window
            if flow_data['bwd_win_bytes'] is None and direction == 'backward':
                flow_data['bwd_win_bytes'] = packet[TCP].window

        # Get packet length (excluding Ethernet header)
        if IP in packet:
            packet_len = len(packet[IP])
        else:
            packet_len = len(packet.payload)

        # Store packet data based on direction
        if direction == 'forward':
            flow_data['forward_packets'].append((timestamp, packet_len, flags))
        elif direction == 'backward':
            flow_data['backward_packets'].append((timestamp, packet_len, flags))

    def process_packet(self, packet) -> None:
        """Process a packet and update flow records."""
        if not self.timestamp_first_packet:
            self.timestamp_first_packet = time.time()

        timestamp = time.time()

        # Store packet for PCAP file only if PCAP capture is enabled
        if self.pcap_capture:
            self.captured_packets.append(packet)  # For current window
            self.accumulated_packets.append(packet)  # For PCAP saving
            if len(self.accumulated_packets) % 500 == 0:  # Log every 500 packets
                logger.debug(f"PCAP capture: {len(self.accumulated_packets)} packets accumulated")

        # Update statistics
        self.stats['total_packets'] += 1

        # Track protocol distribution
        if IP in packet:
            if TCP in packet:
                self.stats['protocol_distribution']['TCP'] += 1
            elif UDP in packet:
                self.stats['protocol_distribution']['UDP'] += 1
            elif packet[IP].proto == 1:  # ICMP
                self.stats['protocol_distribution']['ICMP'] += 1
            else:
                self.stats['protocol_distribution']['Other'] += 1

        # Get flow key
        flow_key = self.get_flow_key(packet)
        if not flow_key:
            return

        # Update port connections for Conn_Rate calculation
        self.update_port_connections(packet, timestamp)

        # Update flow data
        self.extract_packet_features(packet, timestamp, flow_key, self.flows[flow_key])

    def check_expired_flows(self, current_time, aggressive=False) -> None:
        """Check for expired flows and move them to completed flows."""
        expired_keys = []

        # Use shorter timeout when being aggressive (e.g., when no new packets)
        timeout_threshold = self.max_idle_time / 2 if aggressive else self.max_idle_time

        for flow_key, flow_data in self.flows.items():
            # Skip if the flow hasn't started yet
            if flow_data['start_time'] is None:
                continue

            # Check if flow has been idle for too long
            if (current_time - flow_data['last_timestamp']) > timeout_threshold:
                expired_keys.append(flow_key)

        # Process expired flows
        for key in expired_keys:
            flow_data = self.flows[key]
            # Finalize active time if flow ends during an active period
            if flow_data['active_start']:
                active_time = flow_data['last_timestamp'] - flow_data['active_start']
                flow_data['active_times'].append(active_time)

            # Add to completed flows and remove from active flows
            self.completed_flows.append((key, flow_data))
            del self.flows[key]

    def compute_flow_features(self, flow_key, flow_data) -> Dict[str, Any]:
        """
        Compute features from flow data, focusing on the critical features for the Decision Tree model.
        """
        features = {}

        # Basic flow info - critical for the Decision Tree model
        src_ip, dst_ip, src_port, dst_port, protocol = flow_key
        features['Src IP'] = src_ip
        features['Dst IP'] = dst_ip
        features['Src Port'] = src_port
        features['Dst Port'] = dst_port
        features['Protocol'] = protocol

        # Flow Duration - a critical feature for the Decision Tree model
        if flow_data['start_time'] and flow_data['last_timestamp']:
            duration = flow_data['last_timestamp'] - flow_data['start_time']
            features['Flow Duration'] = int(duration * 1_000_000)  # Convert to microseconds
        else:
            features['Flow Duration'] = 0

        # Calculate Conn_Rate - THE MOST IMPORTANT FEATURE for the model
        # This is calculated as connections per second to the destination port
        if dst_port in self.port_connections:
            window_start = flow_data['last_timestamp'] - self.conn_rate_window
            conn_count = sum(1 for ts in self.port_connections[dst_port] if ts >= window_start)
            features['Conn_Rate'] = conn_count / self.conn_rate_window
        else:
            features['Conn_Rate'] = 0

        # Forward and backward packets
        fwd_packets = flow_data['forward_packets']
        bwd_packets = flow_data['backward_packets']

        # Forward packet length stats - Fwd Seg Size Min is critical
        fwd_lens = [size for _, size, _ in fwd_packets]
        if fwd_lens:
            features['Fwd Seg Size Min'] = min(fwd_lens)
            features['TotLen Fwd Pkts'] = sum(fwd_lens)
        else:
            features['Fwd Seg Size Min'] = 0
            features['TotLen Fwd Pkts'] = 0

        # Backward packet length stats - Bwd Pkt Len Mean is critical
        bwd_lens = [size for _, size, _ in bwd_packets]
        if bwd_lens:
            features['Bwd Pkt Len Mean'] = sum(bwd_lens) / len(bwd_lens)
        else:
            features['Bwd Pkt Len Mean'] = 0

        # Flow IAT statistics - Mean and Max are critical
        all_timestamps = sorted([ts for ts, _, _ in fwd_packets] + [ts for ts, _, _ in bwd_packets])
        if len(all_timestamps) > 1:
            iats = [all_timestamps[i] - all_timestamps[i - 1] for i in range(1, len(all_timestamps))]
            features['Flow IAT Mean'] = sum(iats) / len(iats) * 1_000_000  # µs
            features['Flow IAT Max'] = max(iats) * 1_000_000              # µs
        else:
            features['Flow IAT Mean'] = 0
            features['Flow IAT Max'] = 0

        # Window sizes - critical features
        features['Init Fwd Win Byts'] = flow_data.get('fwd_win_bytes', 0) or 0
        features['Init Bwd Win Byts'] = flow_data.get('bwd_win_bytes', 0) or 0

        return features

    def process_flows(self):
        """
        Process completed flows and prepare them for model prediction.
        Returns dataframe with flows ready for model prediction.
        """
        # Compute features for all completed flows
        flow_features = []

        for flow_key, flow_data in self.completed_flows:
            features = self.compute_flow_features(flow_key, flow_data)
            flow_features.append(features)

        # Reset completed flows for next window
        self.completed_flows = []

        if not flow_features:
            return None

        # Create raw dataframe with all columns
        raw_df = pd.DataFrame(flow_features)

        # Create a new dataframe for model-ready data
        model_df = pd.DataFrame()

        # First add the network identifiers and protocol information
        for col in ['Src IP', 'Dst IP', 'Src Port', 'Dst Port', 'Protocol']:
            if col in raw_df.columns:
                model_df[col] = raw_df[col]

        # Make sure all required DT features exist in raw_df with correct defaults
        for col in self.feature_columns:
            if col not in raw_df.columns:
                raw_df[col] = 0

        # Create a feature matrix in the exact order expected by the model
        feature_matrix = np.zeros((len(raw_df), len(self.feature_columns)))
        for i, col in enumerate(self.feature_columns):
            feature_matrix[:, i] = raw_df[col].values

        # Apply scaler if available
        if self.scaler:
            try:
                # Apply scaling to the feature matrix
                scaled_features = self.scaler.transform(feature_matrix)

                # Add scaled features to model_df in the correct order
                for i, col in enumerate(self.feature_columns):
                    model_df[col] = scaled_features[:, i]
            except Exception as e:
                logger.error(f"Error applying scaler: {str(e)}")
                logger.warning("Using unscaled features may affect model prediction accuracy.")

                # Add unscaled features if scaling fails
                for i, col in enumerate(self.feature_columns):
                    model_df[col] = feature_matrix[:, i]
        else:
            # Add unscaled features if no scaler
            for i, col in enumerate(self.feature_columns):
                model_df[col] = feature_matrix[:, i]

        # Add placeholder labels (will be predicted by the model)
        model_df['Label'] = "Unknown"
        model_df['Threat'] = "Unknown"

        return model_df

    def predict_flows(self, flows_df):
        """
        Predict threats in flows using the loaded Decision Tree model.

        Args:
            flows_df: DataFrame with flows to evaluate

        Returns:
            results_df: DataFrame with prediction results
        """
        if flows_df is None or len(flows_df) == 0:
            return None

        # Extract features for prediction
        X = flows_df[self.feature_columns].values

        # Make predictions
        try:
            y_pred_idx = self.model.predict(X)

            # Convert indices to labels
            y_pred_labels = [self.label_array[int(idx)] for idx in y_pred_idx]

            # Add binary threat classification
            threats = ['Malicious' if label != 'Benign' else 'Benign' for label in y_pred_labels]

            # Add predictions to the dataframe
            results_df = flows_df.copy()
            results_df['Prediction'] = y_pred_labels
            results_df['Threat'] = threats

            # Update statistics
            malicious_flows = sum(1 for t in threats if t == 'Malicious')
            benign_flows = len(threats) - malicious_flows

            self.stats['malicious_flows'] += malicious_flows
            self.stats['benign_flows'] += benign_flows
            self.stats['total_flows'] += len(flows_df)

            # Update attack type counts
            for label in y_pred_labels:
                if label != 'Benign':
                    self.stats['attack_types'][label] += 1

            return results_df

        except Exception as e:
            logger.error(f"Error during model prediction: {str(e)}")
            return None

    def process_alerts(self, results_df):
        """
        Process prediction results and generate alerts for malicious flows.

        Args:
            results_df: DataFrame with prediction results
        """
        if results_df is None or len(results_df) == 0:
            return

        # Filter malicious flows
        malicious_df = results_df[results_df['Threat'] == 'Malicious']
        if len(malicious_df) == 0:
            return

        current_time = time.time()
        channel_layer = get_channel_layer()

        for _, row in malicious_df.iterrows():
            alert = {
                'timestamp': current_time,
                'src_ip': row['Src IP'],
                'dst_ip': row['Dst IP'],
                'src_port': int(row['Src Port']),
                'dst_port': int(row['Dst Port']),
                'attack_type': row['Prediction']
            }

            # Update attack session tracking
            self.update_attack_session(alert['attack_type'])

            # Save to database
            self._save_alert_to_database(alert)

            # Save to history
            self.alert_history.append(alert)
            self.stats['total_alerts'] += 1

            # Log locally
            logger.warning(f"ALERT: {alert['attack_type']} attack detected from "
                           f"{alert['src_ip']}:{alert['src_port']} to {alert['dst_ip']}:{alert['dst_port']}")

            # Send over WebSocket
            async_to_sync(channel_layer.group_send)(
                'ids_alerts',
                {
                    'type': 'send_alert',
                    **alert,
                    'timestamp': datetime.datetime.fromtimestamp(alert['timestamp']).isoformat()
                }
            )

    def _save_alert_to_database(self, alert):
        """Save alert to Django database"""
        try:
            # Import Django models here to avoid circular imports
            import django
            from django.conf import settings

            # Initialize Django if not already done
            if not settings.configured:
                import os
                os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'flow_sentinel.settings')
                django.setup()

            from ids.models import IDSAlert
            from django.utils import timezone

            # Create alert in database with all required fields
            IDSAlert.objects.create(
                timestamp=timezone.datetime.fromtimestamp(alert['timestamp']),
                src_ip=alert['src_ip'],
                src_port=alert['src_port'],
                dst_ip=alert['dst_ip'],
                dst_port=alert['dst_port'],
                protocol=alert.get('protocol', 'TCP'),  # Default to TCP if not specified
                attack_type=alert['attack_type'],
                status='new',  # Default status for new alerts
                description=f"{alert['attack_type']} attack detected from {alert['src_ip']}:{alert['src_port']} to {alert['dst_ip']}:{alert['dst_port']}"
            )

        except Exception as e:
            logger.error(f"Error saving alert to database: {str(e)}")

    def update_attack_session(self, attack_type):
        """
        Update attack session tracking when a new attack is detected.

        Args:
            attack_type: Type of attack detected
        """
        current_time = time.time()
        was_active = self.attack_session['is_active']

        # Update session activity
        self.attack_session['last_activity_time'] = current_time
        self.attack_session['attack_types'].add(attack_type)

        # Check if this is a new attack session
        if not self.attack_session['is_active']:
            self.attack_session['is_active'] = True
            self.attack_session['start_time'] = current_time

            # Broadcast attack session started
            self._broadcast_attack_session_status()
            logger.warning(f"Attack session started: {attack_type}")

        # Schedule session timeout check
        self._schedule_session_timeout_check()

    def _schedule_session_timeout_check(self):
        """
        Schedule a check to see if the attack session has timed out.
        """
        import threading

        def check_timeout():
            time.sleep(self.attack_session['session_timeout'] + 1)
            current_time = time.time()
            time_since_last_activity = current_time - self.attack_session['last_activity_time']

            if (self.attack_session['is_active'] and
                time_since_last_activity >= self.attack_session['session_timeout']):
                self._end_attack_session()

        timeout_thread = threading.Thread(target=check_timeout)
        timeout_thread.daemon = True
        timeout_thread.start()

    def _end_attack_session(self):
        """
        End the current attack session.
        """
        if not self.attack_session['is_active']:
            return

        attack_types = list(self.attack_session['attack_types'])
        self.attack_session['is_active'] = False
        self.attack_session['attack_types'].clear()

        # Broadcast attack session ended
        self._broadcast_attack_session_status()
        logger.info(f"Attack session ended. Attack types detected: {', '.join(attack_types)}")

    def _broadcast_attack_session_status(self):
        """
        Broadcast attack session status via WebSocket.
        """
        try:
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync

            channel_layer = get_channel_layer()
            if channel_layer:
                async_to_sync(channel_layer.group_send)(
                    'ids_alerts',
                    {
                        'type': 'send_attack_session_status',
                        'isActive': self.attack_session['is_active'],
                        'attackTypes': list(self.attack_session['attack_types']),
                        'startTime': self.attack_session['start_time'] if self.attack_session['is_active'] else None
                    }
                )
        except Exception as e:
            logger.error(f"Error broadcasting attack session status: {str(e)}")

    def get_attack_session_status(self):
        """
        Get current attack session status.

        Returns:
            dict: Attack session status information
        """
        return {
            'is_active': self.attack_session['is_active'],
            'attack_types': list(self.attack_session['attack_types']),
            'start_time': self.attack_session['start_time'] if self.attack_session['is_active'] else None,
            'duration': (time.time() - self.attack_session['start_time']) if self.attack_session['is_active'] else None
        }

    def save_window_results(self, results_df, window_packets=None):
        """Save the results from the current window for later analysis."""
        if results_df is None or len(results_df) == 0:
            return

        current_time = time.time()

        # Handle CSV saving based on configuration
        if self.consolidated_csv:
            # Add flows to accumulated list for consolidated CSV
            self.accumulated_flows.append(results_df.copy())

            # Check if it's time to save consolidated CSV file
            time_since_last_csv_save = current_time - self.last_csv_save_time

            if time_since_last_csv_save >= self.csv_save_interval:
                self._save_consolidated_csv()
        else:
            # Save individual window CSV (original behavior)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            results_path = os.path.join(self.result_dir, f"flows_{timestamp}.csv")
            results_df.to_csv(results_path, index=False)
            logger.debug(f"Individual window CSV saved: {results_path} ({len(results_df)} flows)")

        # Handle PCAP saving (unchanged logic)
        time_since_last_pcap_save = current_time - self.last_pcap_save_time

        if self.pcap_capture and time_since_last_pcap_save >= self.pcap_save_interval:
            if self.accumulated_packets:
                pcap_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                pcap_path = os.path.join(self.result_dir, f"packets_{pcap_timestamp}.pcap")
                try:
                    wrpcap(pcap_path, self.accumulated_packets)
                    logger.info(f"PCAP file saved: {pcap_path} ({len(self.accumulated_packets)} packets, {time_since_last_pcap_save/60:.1f} minutes)")

                    # Reset accumulated packets and update last save time
                    self.accumulated_packets = []
                    self.last_pcap_save_time = current_time
                except Exception as e:
                    logger.error(f"Error saving PCAP file: {str(e)}")
            else:
                logger.debug(f"No packets accumulated for PCAP save after {time_since_last_pcap_save/60:.1f} minutes")
        elif not self.pcap_capture:
            logger.debug("PCAP capture disabled - skipping packet file save")

    def _save_consolidated_csv(self):
        """Save all accumulated flows to a single consolidated CSV file."""
        if not self.accumulated_flows:
            logger.debug("No flows accumulated for consolidated CSV save")
            return

        try:
            # Combine all accumulated flow DataFrames
            combined_df = pd.concat(self.accumulated_flows, ignore_index=True)

            # Add timestamp column for when each flow was processed
            if 'Processing_Timestamp' not in combined_df.columns:
                combined_df['Processing_Timestamp'] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Save to consolidated CSV file
            if not self.csv_header_written:
                # First time writing - include header
                combined_df.to_csv(self.consolidated_csv_path, index=False, mode='w')
                self.csv_header_written = True
                logger.info(f"Consolidated CSV created: {self.consolidated_csv_path}")
            else:
                # Append without header
                combined_df.to_csv(self.consolidated_csv_path, index=False, mode='a', header=False)

            total_flows = len(combined_df)
            logger.info(f"Consolidated CSV updated: {total_flows} flows saved to {self.consolidated_csv_path}")

            # Reset accumulated flows and update last save time
            self.accumulated_flows = []
            self.last_csv_save_time = time.time()

        except Exception as e:
            logger.error(f"Error saving consolidated CSV: {str(e)}")

    def record_flow_history(self, results_df):
        """Record flow data for traffic graphs"""
        current_time = time.time()
        timestamp = datetime.datetime.now()

        logger.info(f"Recording flow history - results_df length: {len(results_df) if results_df is not None else 0}")


        benign_count = 0
        malicious_count = 0

        if results_df is not None and len(results_df) > 0:
            # Count threat levels
            for _, row in results_df.iterrows():
                threat = row.get('Threat', 'Benign')
                if threat == 'Benign':
                    benign_count += 1
                else:
                    malicious_count += 1

        # Calculate flow rate (flows per minute)
        total_flows = benign_count + malicious_count
        flow_rate = total_flows / (self.capture_interval / 60.0) if self.capture_interval > 0 else 0

        # Record this window's data
        flow_record = {
            'timestamp': timestamp,
            'time_str': timestamp.strftime('%H:%M'),
            'total_flows': total_flows,
            'benign_flows': benign_count,
            'malicious_flows': malicious_count,
            'flow_rate': round(flow_rate, 2),
            'capture_interval': self.capture_interval
        }

        # Add to history
        self.flow_history.append(flow_record)

        logger.info(f"Recorded flow history: {total_flows} flows (benign: {benign_count}, malicious: {malicious_count})")
        logger.info(f"Flow history now has {len(self.flow_history)} records")

    def get_flow_history(self, timeframe='1h'):
        """Get flow history for the specified timeframe"""
        if not self.flow_history:
            return []

        now = datetime.datetime.now()

        # Determine how far back to look
        if timeframe == '1h':
            cutoff = now - timedelta(hours=1)
        elif timeframe == '6h':
            cutoff = now - timedelta(hours=6)
        elif timeframe == '24h':
            cutoff = now - timedelta(hours=24)
        elif timeframe == '7d':
            cutoff = now - timedelta(days=7)
        else:
            cutoff = now - timedelta(hours=1)

        # Filter history to timeframe
        filtered_history = [
            record for record in self.flow_history
            if record['timestamp'] >= cutoff
        ]

        return filtered_history



    def generate_status_report(self):
        """Generate a status report with current statistics."""
        run_time = time.time() - self.stats['start_time']
        hours, remainder = divmod(run_time, 3600)
        minutes, seconds = divmod(remainder, 60)

        report = (
            f"\n{'='*60}\n"
            f"REAL-TIME IDS STATUS REPORT\n"
            f"{'='*60}\n"
            f"Run time: {int(hours)}h {int(minutes)}m {int(seconds)}s\n"
            f"Capture windows processed: {self.stats['windows_processed']}\n"
            f"Total packets processed: {self.stats['total_packets']}\n"
            f"Total flows analyzed: {self.stats['total_flows']}\n"
            f"Benign flows: {self.stats['benign_flows']} "
            f"({self.stats['benign_flows']/max(1, self.stats['total_flows'])*100:.2f}%)\n"
            f"Malicious flows: {self.stats['malicious_flows']} "
            f"({self.stats['malicious_flows']/max(1, self.stats['total_flows'])*100:.2f}%)\n"
            f"Total alerts generated: {self.stats['total_alerts']}\n"
        )

        # Add attack type distribution
        if self.stats['attack_types']:
            report += "\nAttack type distribution:\n"
            for attack_type, count in self.stats['attack_types'].most_common():
                report += f"  {attack_type}: {count} "
                report += f"({count/max(1, self.stats['total_flows'])*100:.2f}%)\n"

        # Add recent alerts
        if self.alert_history:
            report += "\nRecent alerts (last 5):\n"
            sorted_alerts = sorted(list(self.alert_history), key=lambda x: x['timestamp'], reverse=True)
            for alert in sorted_alerts[:5]:
                alert_time = datetime.datetime.fromtimestamp(alert['timestamp']).strftime('%H:%M:%S')
                report += (f"  {alert_time} - {alert['attack_type']} from "
                           f"{alert['src_ip']}:{alert['src_port']} to {alert['dst_ip']}:{alert['dst_port']}\n")

        report += f"{'='*60}\n"
        return report

    def capture_worker(self, interface):
        """Worker thread for continuous packet capture."""
        logger.info(f"Starting capture thread on interface {interface}")

        while self.running:
            start_time = time.time()

            # Reset packets for this window
            self.captured_packets = []

            try:
                # Capture packets for the specified interval and drop DNS
                sniff(
                    iface=interface,
                    prn=self.process_packet,
                    store=False,
                    timeout=self.capture_interval,
                    filter="not port 53"
                )
            except Exception as e:
                logger.error(f"Error during packet capture: {str(e)}")
                if not self.running:
                    break
                time.sleep(1)
                continue

            # Check for expired flows at the end of the window
            current_time = time.time()
            self.check_expired_flows(current_time)

            # If no packets were captured in this window, be more aggressive about flow cleanup
            if len(self.captured_packets) == 0:
                # Force cleanup of flows that have been idle for even shorter periods
                self.check_expired_flows(current_time, aggressive=True)

            # Process flows and put them in the queue for analysis
            flows_df = self.process_flows()
            if flows_df is not None and len(flows_df) > 0:
                try:
                    # Include captured packets with the flows for this window
                    window_data = {
                        'flows_df': flows_df,
                        'captured_packets': self.captured_packets.copy() if self.pcap_capture else []
                    }
                    self.flow_queue.put(window_data, block=True, timeout=1)
                except queue.Full:
                    logger.warning("Analysis thread is not keeping up, dropping a window of flows")

            # Wait if there's time left in the interval
            elapsed = time.time() - start_time
            if elapsed < self.capture_interval:
                time.sleep(self.capture_interval - elapsed)

    def analysis_worker(self):
        """Worker thread for continuous flow analysis."""
        logger.info("Starting analysis thread")

        last_report_time = time.time()
        channel_layer = get_channel_layer()

        while self.running:
            try:
                # Get the next batch of window data
                window_data = self.flow_queue.get(block=True, timeout=1)

                # Handle both old format (just flows_df) and new format (dict with flows and packets)
                if isinstance(window_data, dict):
                    flows_df = window_data['flows_df']
                    window_packets = window_data['captured_packets']
                else:
                    # Backward compatibility
                    flows_df = window_data
                    window_packets = []

                # Predict and alert
                results_df = self.predict_flows(flows_df)
                if results_df is not None:
                    self.process_alerts(results_df)

                    # Record flow history for traffic graphs
                    self.record_flow_history(results_df)

                    # Save window results (including PCAP files if enabled)
                    self.save_window_results(results_df, window_packets)

                    # Send basic log over WebSocket
                    async_to_sync(channel_layer.group_send)(
                        'ids_logs',
                        {
                            'type': 'send_log',
                            'message': f"Processed {len(results_df)} flows - {results_df['Threat'].value_counts().to_dict()}"
                        }
                    )

                # Update window stats
                self.stats['windows_processed'] += 1

                # Periodic status report
                current_time = time.time()
                if (current_time - last_report_time) >= 60:
                    report = self.generate_status_report()
                    print(report)
                    last_report_time = current_time

                self.flow_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in analysis thread: {str(e)}")
                async_to_sync(channel_layer.group_send)(
                    'ids_logs',
                    {
                        'type': 'send_log',
                        'message': f"Error in analysis thread: {str(e)}"
                    }
                )
                time.sleep(1)

    def start(self, interface=None):
        """
        Start the real-time IDS.

        Args:
            interface: Network interface to capture from
        """
        logger.info(f"Starting Real-Time IDS on interface {interface or 'default'}")

        os.makedirs(self.result_dir, exist_ok=True)
        self.running = True

        # Register signal handlers for graceful shutdown
        if threading.current_thread() == threading.main_thread():
            try:
                signal.signal(signal.SIGINT, self.signal_handler)
                signal.signal(signal.SIGTERM, self.signal_handler)
            except Exception as e:
                logger.warning(f"Signal handler not set (probably running in web mode): {e}")

        # Start capture thread
        self.capture_thread = threading.Thread(
            target=self.capture_worker,
            args=(interface,),
            daemon=True
        )
        self.capture_thread.start()

        # Start analysis thread
        self.analysis_thread = threading.Thread(
            target=self.analysis_worker,
            daemon=True
        )
        self.analysis_thread.start()

        logger.info("Real-Time IDS is running. Press Ctrl+C to stop.")

        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Shutdown requested...")
        finally:
            self.stop()

    def stop(self):
        """Stop the real-time IDS gracefully."""
        logger.info("Stopping Real-Time IDS...")

        self.running = False

        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=3)

        if self.analysis_thread and self.analysis_thread.is_alive():
            self.analysis_thread.join(timeout=3)

        # Save any remaining accumulated flows before stopping
        if self.consolidated_csv and self.accumulated_flows:
            try:
                self._save_consolidated_csv()
                logger.info(f"Final consolidated CSV saved with remaining flows")
            except Exception as e:
                logger.error(f"Error saving final consolidated CSV: {str(e)}")

        # Save any remaining accumulated packets before stopping
        if self.pcap_capture and self.accumulated_packets:
            pcap_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            pcap_path = os.path.join(self.result_dir, f"packets_final_{pcap_timestamp}.pcap")
            try:
                wrpcap(pcap_path, self.accumulated_packets)
                logger.info(f"Final PCAP file saved: {pcap_path} ({len(self.accumulated_packets)} packets)")
            except Exception as e:
                logger.error(f"Error saving final PCAP file: {str(e)}")

        final_report = self.generate_status_report()
        print(final_report)

        report_path = os.path.join(self.result_dir, "final_report.txt")
        with open(report_path, 'w') as f:
            f.write(final_report)

        logger.info(f"Final report saved to {report_path}")
        logger.info(f"All results saved to {self.result_dir}")
        logger.info("Real-Time IDS stopped.")

    def signal_handler(self, sig, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {sig}, shutting down...")
        self.stop()


def main():
    """Main function to parse arguments and run the real-time IDS."""
    parser = argparse.ArgumentParser(
        description='Real-Time Network Intrusion Detection System')

    # Capture options
    parser.add_argument('-i', '--interface', default=None,
                        help='Network interface to capture from (e.g., eth0, Wi-Fi)')
    parser.add_argument('-t', '--interval', type=float, default=2.0,
                        help='Capture interval in seconds (default: 2.0)')

    # Model options
    parser.add_argument('-m', '--model', default='./DecisionTree_model.pkl',
                        help='Path to the Decision Tree model file')
    parser.add_argument('-l', '--labels', default='./label_array.npy',
                        help='Path to the label array file')
    parser.add_argument('-f', '--features', default='./DecisionTree_feature_columns.txt',
                        help='Path to the feature columns file')
    parser.add_argument('-s', '--scaler', default='./ids/model_files/scaler.pkl',
                        help='Path to the scaler file')

    # Output options
    parser.add_argument('-o', '--output', default='./ids_results',
                        help='Output directory for result files')

    args = parser.parse_args()

    ids = RealTimeIDS(
        model_path=args.model,
        label_array_path=args.labels,
        feature_columns_path=args.features,
        scaler_path=args.scaler,
        capture_interval=args.interval,
        output_dir=args.output
    )

    try:
        ids.start(interface=args.interface)
    except Exception as e:
        logger.error(f"Error starting IDS: {str(e)}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
#!/bin/bash

# Flow Sentinel IDS Server Startup Script (Regular User)
# This script starts the Django server as a regular user
# Note: Requires packet capture capabilities to be set first using setup_packet_capture.sh

echo "=== Flow Sentinel IDS Server Startup ==="
echo "Starting Django server..."

# Change to the backend directory
cd "$(dirname "$0")"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
else
    echo "Warning: Virtual environment not found. Using system Python."
fi

# Set environment variables
export DJANGO_SETTINGS_MODULE=flow_sentinel.settings

# Check if database needs migration
echo "Checking database..."
python manage.py migrate --check 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Running database migrations..."
    python manage.py migrate
fi

# Check if packet capture capabilities are set
PYTHON_EXEC=$(which python)
echo "Checking packet capture capabilities for: $PYTHON_EXEC"
getcap "$PYTHON_EXEC" 2>/dev/null

if [ $? -ne 0 ]; then
    echo ""
    echo "⚠️  Warning: Packet capture capabilities not detected."
    echo "   If you encounter permission errors, run:"
    echo "   sudo ./setup_packet_capture.sh"
    echo ""
fi

# Start the Django development server
echo "Starting Django server on 0.0.0.0:8000..."
echo "Access the application at: http://localhost:8000"
echo "Press Ctrl+C to stop the server"
echo ""

# Run the server with daphne (ASGI server)
./venv/bin/daphne flow_sentinel.asgi:application -b 0.0.0.0 -p 8000

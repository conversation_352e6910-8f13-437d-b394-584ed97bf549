import { useState, useEffect } from "react";
import { Navbar } from "@/components/layout/Navbar";
import { Sidebar } from "@/components/layout/Sidebar";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { 
  Monitor, 
  Cpu, 
  MemoryStick, 
  HardDrive, 
  Network, 
  AlertTriangle,
  Activity,
  Zap,
  Server,
  RefreshCw
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  fetchSystemResources,
  SystemResourceData,
  getUsageColor,
  formatBytes,
  formatUptime,
  clearSystemResourceCache
} from "@/services/systemResourceService";
import { ResourceOverview } from "@/components/system/ResourceOverview";
import { ResourceCharts } from "@/components/system/ResourceCharts";
import { SystemInfo } from "@/components/system/SystemInfo";
import { ProcessList } from "@/components/system/ProcessList";

const SystemResources = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [resourceData, setResourceData] = useState<SystemResourceData | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch system resource data
  const loadResourceData = async (isRefresh: boolean = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const data = await fetchSystemResources();
      setResourceData(data);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching system resources:', error);
      toast({
        title: "Connection Error",
        description: "Could not connect to the backend server",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    // Clear cache to ensure fresh data on page load
    clearSystemResourceCache();
    loadResourceData();

    // Auto-refresh every 2 seconds for real-time monitoring (coordinated with ResourceCharts)
    const intervalId = setInterval(() => loadResourceData(true), 2000);

    return () => clearInterval(intervalId);
  }, []);

  const handleRefresh = () => {
    // Clear cache to force fresh data
    clearSystemResourceCache();
    loadResourceData(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex">
        <Sidebar />
        <div className="flex-1 flex flex-col min-h-screen md:pl-64">
          <Navbar />
          <main className="flex-1 p-4 md:p-6 overflow-auto">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full"></div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      <Sidebar />

      <div className="flex-1 flex flex-col min-h-screen md:pl-64">
        <Navbar />

        <main className="flex-1 p-4 md:p-6 overflow-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
                <Monitor className="h-6 w-6" />
                System Resources
              </h1>
              <p className="text-muted-foreground">
                Real-time system resource monitoring and performance metrics
              </p>
            </div>

            <div className="flex items-center gap-2">
              {lastUpdated && (
                <span className="text-sm text-muted-foreground">
                  Last updated: {lastUpdated.toLocaleTimeString()}
                </span>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                className="gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>

          {/* Resource Alerts - Filter out API/system internal alerts */}
          {resourceData?.alerts && resourceData.alerts.length > 0 && (
            <div className="mb-6 space-y-2">
              {resourceData.alerts
                .filter(alert => {
                  // Hide API timeout, cached data, and other internal system alerts
                  const message = alert.message?.toLowerCase() || '';
                  return !message.includes('api timeout') &&
                         !message.includes('cached data') &&
                         !message.includes('failure #') &&
                         !message.includes('backend is slow') &&
                         !message.includes('using cached') &&
                         alert.type !== 'system' ||
                         (alert.type === 'system' && !message.includes('api'));
                })
                .map((alert, index) => (
                  <Alert
                    key={index}
                    variant={alert.level === 'critical' ? 'destructive' : 'default'}
                  >
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle className="capitalize">{alert.level} Alert</AlertTitle>
                    <AlertDescription>{alert.message}</AlertDescription>
                  </Alert>
                ))}
            </div>
          )}



          {/* Main Content Tabs */}
          <Tabs defaultValue="charts" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-6">
              <TabsTrigger value="charts" className="gap-2">
                <Activity className="h-4 w-4" />
                Charts
              </TabsTrigger>
              <TabsTrigger value="overview" className="gap-2">
                <Monitor className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="processes" className="gap-2">
                <Zap className="h-4 w-4" />
                Processes
              </TabsTrigger>
              <TabsTrigger value="system" className="gap-2">
                <Server className="h-4 w-4" />
                System Info
              </TabsTrigger>
            </TabsList>

            <TabsContent value="charts">
              {resourceData && <ResourceCharts />}
            </TabsContent>

            <TabsContent value="overview">
              {resourceData && <ResourceOverview data={resourceData} />}
            </TabsContent>

            <TabsContent value="processes">
              {resourceData && <ProcessList processes={resourceData.top_processes} />}
            </TabsContent>

            <TabsContent value="system">
              {resourceData && <SystemInfo data={resourceData.system} />}
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default SystemResources;

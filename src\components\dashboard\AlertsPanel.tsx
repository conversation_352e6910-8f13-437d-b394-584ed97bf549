
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState, useEffect, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { notificationService } from "@/services/notificationService";

interface Alert {
  id: number;
  timestamp: string;
  type: string;
  source: string;
  target: string;
  status?: string;
}

interface AlertsPanelProps {
  className?: string;
}

export function AlertsPanel({ className = "" }: AlertsPanelProps) {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(() => {
    // Load notification preference from localStorage
    const saved = localStorage.getItem('alertNotificationsEnabled');
    return saved !== null ? JSON.parse(saved) : true;
  });
  const { toast } = useToast();
  const wsRef = useRef<WebSocket | null>(null);
  const alertCountRef = useRef<number>(0);
  const lastToastTimeRef = useRef<number>(0);
  const batchedAlertsRef = useRef<number>(0);
  const seenAlertIdsRef = useRef<Set<number>>(new Set()); // Track seen alert IDs
  const fetchingRef = useRef(false); // Prevent overlapping requests
  const toastCooldownMs = 10000; // 10 seconds between toast notifications

  // Helper function to clean up old seen alert IDs to prevent memory leaks
  const cleanupSeenAlerts = (currentAlerts: Alert[]) => {
    const currentAlertIds = new Set(currentAlerts.map(alert => alert.id));
    const seenIds = seenAlertIdsRef.current;

    // Remove seen IDs that are no longer in the current alerts list
    for (const id of seenIds) {
      if (!currentAlertIds.has(id)) {
        seenIds.delete(id);
      }
    }
  };

  // Helper function to show notifications for new alerts only
  const showToastForNewAlert = async (alert: Alert) => {
    // Check if we've already shown a notification for this alert
    if (seenAlertIdsRef.current.has(alert.id)) {
      return;
    }

    // Mark this alert as seen
    seenAlertIdsRef.current.add(alert.id);

    // Don't show notifications if disabled
    if (!notificationsEnabled) {
      return;
    }

    // Process the alert through the notification service for session tracking
    // This will handle showing appropriate notifications based on attack sessions
    await notificationService.processAttackAlert(
      alert.type,
      alert.source,
      alert.target
    );
  };

  // Save notification preference to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('alertNotificationsEnabled', JSON.stringify(notificationsEnabled));
  }, [notificationsEnabled]);

  useEffect(() => {
    // Initialize notification service with toast function
    notificationService.setToastFunction(toast);

    // Initial fetch of alerts
    fetchAlerts(true);

    // Connect to WebSocket for real-time updates
    connectWebSocket();

    // Setup polling as fallback (reduced to 30 seconds to minimize server load)
    // WebSocket should handle most real-time updates
    const intervalId = setInterval(() => fetchAlerts(false), 30000);

    // Listen for dataset cleanup events
    const handleDatasetClean = () => {
      console.log('Dataset cleaned, refreshing alerts...');
      // Clear local state
      setAlerts([]);
      seenAlertIdsRef.current.clear();
      // Fetch fresh data
      fetchAlerts(true);
    };

    // Listen for custom events from other components
    window.addEventListener('dataset-cleaned', handleDatasetClean);

    return () => {
      clearInterval(intervalId);
      window.removeEventListener('dataset-cleaned', handleDatasetClean);
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const connectWebSocket = () => {
    // Close any existing connection
    if (wsRef.current) {
      wsRef.current.close();
    }
    
    const ws = new WebSocket('ws://localhost:8000/ws/ids/');
    
    ws.onopen = () => {
      console.log('AlertsPanel WebSocket connected');
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        // Handle real-time alert messages
        if (data.type === 'alert') {
          const newAlert = {
            id: data.id,
            timestamp: data.timestamp,
            type: data.attack_type,
            source: data.source,
            target: data.target
          };

          // Add to the beginning of the array (most recent first)
          setAlerts(prev => {
            // Check if alert with this ID already exists to avoid duplicates
            if (prev.some(a => a.id === newAlert.id)) {
              return prev;
            }
            const newAlerts = [newAlert, ...prev];
            // Limit to most recent 30 alerts
            return newAlerts.slice(0, 30);
          });

          // Process alert through notification service for session tracking
          // Only show toast if notifications are enabled
          if (notificationsEnabled) {
            showToastForNewAlert(newAlert);
          }
        }
      } catch (e) {
        console.error("Failed to parse WebSocket message:", e);
      }
    };
    
    ws.onclose = () => {
      console.log('AlertsPanel WebSocket disconnected');
      // Try to reconnect after a delay
      setTimeout(connectWebSocket, 5000);
    };
    
    ws.onerror = (error) => {
      console.error("AlertsPanel WebSocket error:", error);
    };
    
    wsRef.current = ws;
  };

  const fetchAlerts = async (isInitial: boolean = false) => {
    // Prevent overlapping requests
    if (fetchingRef.current) {
      console.log('AlertsPanel: Skipping fetch - request already in progress');
      return;
    }

    try {
      fetchingRef.current = true;
      if (isInitial) {
        setIsInitialLoad(true);
      } else {
        setIsUpdating(true);
      }

      // Build URL with parameters
      const params = new URLSearchParams();
      params.append('t', Date.now().toString()); // Cache busting

      if (isInitial) {
        // On initial load, get only new alerts (limit 10 to avoid overwhelming)
        params.append('status', 'new');
        params.append('limit', '10');
      } else {
        // On updates, get only new alerts since last fetch
        params.append('status', 'new');
        params.append('limit', '5');
        if (alerts.length > 0) {
          params.append('since_id', Math.max(...alerts.map(a => a.id)).toString());
        }
      }

      // Always fetch fresh data from backend - no caching
      const response = await fetch(`http://localhost:8000/api/ids/alerts?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();

        // Format timestamps to localize them
        const formattedAlerts = data.alerts.map((alert: Alert) => ({
          ...alert,
          timestamp: formatTimestamp(alert.timestamp)
        }));

        if (isInitial) {
          // On initial load, replace all alerts
          setAlerts(formattedAlerts || []);
          // Mark all existing alerts as seen without showing toasts
          formattedAlerts.forEach((alert: Alert) => {
            seenAlertIdsRef.current.add(alert.id);
          });
        } else {
          // On updates, add new alerts to the beginning if any
          if (formattedAlerts.length > 0) {
            setAlerts(prev => {
              // Filter out any duplicates and add new alerts to the beginning
              const existingIds = new Set(prev.map(a => a.id));
              const newAlerts = formattedAlerts.filter(alert => !existingIds.has(alert.id));

              if (newAlerts.length > 0) {
                // Show toast notifications for new alerts
                newAlerts.forEach((alert: Alert) => {
                  showToastForNewAlert(alert);
                });

                // Combine and limit to 30 most recent
                const combined = [...newAlerts, ...prev];
                return combined.slice(0, 30);
              }

              return prev;
            });
          }
        }

        // Clean up old seen alert IDs
        cleanupSeenAlerts(formattedAlerts || []);
      } else {
        console.error('Failed to fetch alerts:', response.statusText);
        // If fetch fails, clear alerts to show empty state
        setAlerts([]);
      }
    } catch (error) {
      console.error('Error fetching alerts:', error);
      // If fetch fails, clear alerts to show empty state
      setAlerts([]);
    } finally {
      fetchingRef.current = false;
      if (isInitial) {
        setIsInitialLoad(false);
      } else {
        setIsUpdating(false);
      }
    }
  };
  
  // Format timestamp to a user-friendly format with current timezone
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch (e) {
      return timestamp; // Fallback to original format if parsing fails
    }
  };

  const handleViewAlert = (alertId: number) => {
    // In a real app, this would navigate to alert details
    toast({
      title: "Alert Details",
      description: `Viewing details for alert #${alertId}`
    });
  };

  return (
    <Card className={`row-span-2 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ShieldAlert className="h-5 w-5 text-destructive" />
            <CardTitle>Recent Alerts</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                const newState = !notificationsEnabled;
                setNotificationsEnabled(newState);
                toast({
                  title: newState ? "Notifications Enabled" : "Notifications Disabled",
                  description: newState
                    ? "You will receive alerts for new security incidents"
                    : "Alert notifications have been turned off",
                  variant: newState ? "default" : "destructive"
                });
              }}
              className="h-6 w-6 p-0"
              title={notificationsEnabled ? "Disable alert notifications" : "Enable alert notifications"}
            >
              {notificationsEnabled ? (
                <Bell className="h-3 w-3" />
              ) : (
                <BellOff className="h-3 w-3 text-muted-foreground" />
              )}
            </Button>
            <Badge variant="destructive" className="px-2 py-1 text-xs rounded-full">
              {alerts.length} {alerts.length === 1 ? "Alert" : "Alerts"}
            </Badge>
          </div>
        </div>
        <CardDescription>
          Security incidents requiring attention
        </CardDescription>
      </CardHeader>
      <CardContent className="px-2">
        <ScrollArea className="h-[380px] pr-4">
          {isInitialLoad ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin h-5 w-5 border-t-2 border-primary rounded-full"></div>
            </div>
          ) : alerts.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
              <p>No alerts detected</p>
              <p className="text-xs mt-1">Your network appears secure</p>
            </div>
          ) : (
            <div className="space-y-3">
              {alerts.map((alert) => (
                <div
                  key={`alert-${alert.id}`}
                  className="flex items-start justify-between rounded-md p-3 border-l-4 border-destructive bg-destructive/5 transition-colors hover:bg-muted/20"
                >
                  <div className="grid gap-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{alert.type}</span>
                      <Badge variant="destructive" className="text-xs">
                        Threat
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {alert.timestamp}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      <span className="font-mono">{alert.source} → {alert.target}</span>
                    </div>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-7 w-7 shrink-0"
                    onClick={() => handleViewAlert(alert.id)}
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span className="sr-only">View details</span>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
      <CardFooter>
        <Button asChild variant="outline" className="w-full gap-2">
          <Link to="/alerts">
            <Bell className="h-4 w-4" />
            View All Alerts
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

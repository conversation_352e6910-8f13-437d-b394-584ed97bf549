
import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import ids.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'flow_sentinel.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            ids.routing.websocket_urlpatterns
        )
    ),
})

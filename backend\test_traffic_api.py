#!/usr/bin/env python3
"""
Test script to debug traffic API
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'flow_sentinel.settings')
django.setup()

from ids.ids_manager import IDSManager
from ids.views.data_views import IDSTrafficView
from ids.models import IDSSettings
from django.test import RequestFactory
from rest_framework.test import APIRequestFactory
import time

def test_traffic_api():
    """Test the traffic API directly"""
    
    print("=== Testing Traffic API ===")
    
    # Check IDS status
    ids_manager = IDSManager()
    print(f"IDS running: {ids_manager.is_running}")
    
    if ids_manager.is_running and ids_manager.ids:
        print(f"IDS instance exists: True")
        if hasattr(ids_manager.ids, 'flow_history'):
            print(f"Flow history length: {len(ids_manager.ids.flow_history)}")
            
            if ids_manager.ids.flow_history:
                print("Recent flow records:")
                for i, record in enumerate(list(ids_manager.ids.flow_history)[-3:]):
                    print(f"  {i}: {record['time_str']} - {record['total_flows']} flows")
        else:
            print("No flow_history attribute")
    else:
        print("IDS not running or no instance")
    
    # Test the traffic view directly
    print("\n=== Testing Traffic View ===")
    factory = APIRequestFactory()
    request = factory.get('/api/ids/traffic', {'timeframe': '1h'})

    view = IDSTrafficView()
    response = view.get(request)
    
    print(f"Response status: {response.status_code}")
    if response.status_code == 200:
        data = response.data
        print(f"Data points returned: {len(data['traffic_data'])}")
        
        if data['traffic_data']:
            print("First 3 data points:")
            for i, point in enumerate(data['traffic_data'][:3]):
                print(f"  {i}: {point['time']} - {point['total_packets']} packets")

            # Check if all values are the same (flat line)
            total_packets_values = [point['total_packets'] for point in data['traffic_data']]
            unique_values = set(total_packets_values)
            print(f"Unique total_packets values: {len(unique_values)}")
            if len(unique_values) == 1:
                print(f"❌ FLAT LINE DETECTED - all values are {list(unique_values)[0]}")
            else:
                print(f"✅ VARYING DATA - values range from {min(total_packets_values)} to {max(total_packets_values)}")
        else:
            print("No traffic data in response")
    else:
        print(f"Error response: {response.data}")

if __name__ == "__main__":
    test_traffic_api()


from datetime import datetime, timedelta
from typing import Dict, List, Any, Union, Optional

def validate_timeframe(timeframe: str) -> bool:
    """
    Validate that the timeframe parameter is one of the accepted values.
    
    Args:
        timeframe: String representing the timeframe (e.g., '24h', '7d')
        
    Returns:
        bool: True if valid, False if invalid
    """
    valid_timeframes = ['24h', '7d', '30d', '90d']
    return timeframe in valid_timeframes

def validate_pagination_params(page: str, page_size: str) -> Dict[str, Union[bool, str, int]]:
    """
    Validate and parse pagination parameters.
    
    Args:
        page: Page number as string
        page_size: Page size as string
        
    Returns:
        Dict containing validation result and parsed values
    """
    result = {
        'valid': True,
        'message': '',
        'page': 1,
        'page_size': 10
    }
    
    try:
        if page:
            page_num = int(page)
            if page_num < 1:
                result['valid'] = False
                result['message'] = 'Page number must be greater than or equal to 1'
            else:
                result['page'] = page_num
    except ValueError:
        result['valid'] = False
        result['message'] = 'Page number must be an integer'
    
    try:
        if page_size:
            size = int(page_size)
            if size < 1:
                result['valid'] = False
                result['message'] = 'Page size must be greater than or equal to 1'
            elif size > 100:
                result['valid'] = False
                result['message'] = 'Page size must be less than or equal to 100'
            else:
                result['page_size'] = size
    except ValueError:
        result['valid'] = False
        result['message'] = 'Page size must be an integer'
    
    return result

def calculate_time_threshold(timeframe: str) -> Optional[datetime]:
    """
    Calculate a time threshold based on the timeframe.
    
    Args:
        timeframe: String representing the timeframe (e.g., '24h', '7d')
        
    Returns:
        datetime: The calculated threshold, or None if invalid timeframe
    """
    if timeframe == '24h':
        return datetime.now() - timedelta(hours=24)
    elif timeframe == '7d':
        return datetime.now() - timedelta(days=7)
    elif timeframe == '30d':
        return datetime.now() - timedelta(days=30)
    elif timeframe == '90d':
        return datetime.now() - timedelta(days=90)
    return None

def safe_get_param(request, param_name, default=None):
    """
    Safely get a parameter from a request with a default value.
    
    Args:
        request: The request object
        param_name: Name of the parameter to get
        default: Default value if parameter is not found
        
    Returns:
        The parameter value or default
    """
    return request.query_params.get(param_name, default)

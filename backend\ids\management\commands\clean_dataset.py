from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from ids.models import IDSAlert, IDSLog
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Clean the IDS dataset by removing old alerts and logs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--alerts-days',
            type=int,
            default=30,
            help='Keep alerts from the last N days (default: 30)'
        )
        parser.add_argument(
            '--logs-days',
            type=int,
            default=7,
            help='Keep logs from the last N days (default: 7)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Delete ALL alerts and logs (use with caution!)'
        )
        parser.add_argument(
            '--severity',
            choices=['low', 'medium', 'high'],
            help='Only delete alerts with specific severity level'
        )
        parser.add_argument(
            '--attack-type',
            type=str,
            help='Only delete alerts of specific attack type'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting dataset cleanup...')
        )

        if options['all']:
            self._clean_all_data(options['dry_run'])
        else:
            self._clean_by_date(
                options['alerts_days'],
                options['logs_days'],
                options['dry_run'],
                options.get('severity'),
                options.get('attack_type')
            )

        self.stdout.write(
            self.style.SUCCESS('Dataset cleanup completed!')
        )

    def _clean_all_data(self, dry_run):
        """Delete all alerts and logs"""
        alerts_count = IDSAlert.objects.count()
        logs_count = IDSLog.objects.count()

        self.stdout.write(
            self.style.WARNING(f'This will delete ALL data:')
        )
        self.stdout.write(f'  - {alerts_count} alerts')
        self.stdout.write(f'  - {logs_count} logs')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN: No data was actually deleted')
            )
            return

        # Confirm deletion
        confirm = input('Are you sure you want to delete ALL data? (yes/no): ')
        if confirm.lower() != 'yes':
            self.stdout.write('Operation cancelled')
            return

        IDSAlert.objects.all().delete()
        IDSLog.objects.all().delete()

        self.stdout.write(
            self.style.SUCCESS(f'Deleted {alerts_count} alerts and {logs_count} logs')
        )

    def _clean_by_date(self, alerts_days, logs_days, dry_run, severity_filter, attack_type_filter):
        """Delete old alerts and logs based on date criteria"""
        now = timezone.now()
        alerts_cutoff = now - timedelta(days=alerts_days)
        logs_cutoff = now - timedelta(days=logs_days)

        # Build alert query - prioritize cleaning resolved/acknowledged alerts first
        alert_query = IDSAlert.objects.filter(timestamp__lt=alerts_cutoff)

        # Note: severity filter removed as severity field no longer exists
        # if severity_filter:
        #     alert_query = alert_query.filter(severity=severity_filter)

        if attack_type_filter:
            alert_query = alert_query.filter(attack_type__icontains=attack_type_filter)

        # Build log query
        log_query = IDSLog.objects.filter(timestamp__lt=logs_cutoff)

        alerts_to_delete = alert_query.count()
        logs_to_delete = log_query.count()

        # Also clean up excessive alerts if there are too many (keep only latest 100)
        total_alerts = IDSAlert.objects.count()
        excess_alerts_query = None
        excess_alerts_count = 0

        if total_alerts > 100:
            # Get IDs of alerts to keep (latest 100)
            keep_ids = list(IDSAlert.objects.order_by('-timestamp')[:100].values_list('id', flat=True))
            excess_alerts_query = IDSAlert.objects.exclude(id__in=keep_ids)
            excess_alerts_count = excess_alerts_query.count()

        self.stdout.write(f'Cleanup criteria:')
        self.stdout.write(f'  - Alerts older than {alerts_days} days: {alerts_to_delete} found')
        if excess_alerts_count > 0:
            self.stdout.write(f'  - Excess alerts (keeping latest 100): {excess_alerts_count} found')
        # Note: severity filter removed as severity field no longer exists
        # if severity_filter:
        #     self.stdout.write(f'  - Severity filter: {severity_filter}')
        if attack_type_filter:
            self.stdout.write(f'  - Attack type filter: {attack_type_filter}')
        self.stdout.write(f'  - Logs older than {logs_days} days: {logs_to_delete} found')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN: No data was actually deleted')
            )
            return

        if alerts_to_delete == 0 and logs_to_delete == 0 and excess_alerts_count == 0:
            self.stdout.write('No data to clean')
            return

        # Delete old alerts
        if alerts_to_delete > 0:
            alert_query.delete()
            self.stdout.write(
                self.style.SUCCESS(f'Deleted {alerts_to_delete} old alerts')
            )

        # Delete excess alerts
        if excess_alerts_count > 0:
            excess_alerts_query.delete()
            self.stdout.write(
                self.style.SUCCESS(f'Deleted {excess_alerts_count} excess alerts (keeping latest 100)')
            )

        # Delete old logs
        if logs_to_delete > 0:
            log_query.delete()
            self.stdout.write(
                self.style.SUCCESS(f'Deleted {logs_to_delete} old logs')
            )

        # Show remaining data
        remaining_alerts = IDSAlert.objects.count()
        remaining_logs = IDSLog.objects.count()

        self.stdout.write(f'Remaining data:')
        self.stdout.write(f'  - Alerts: {remaining_alerts}')
        self.stdout.write(f'  - Logs: {remaining_logs}')

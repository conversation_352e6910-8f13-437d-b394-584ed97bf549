import { useState, useEffect, useRef, useCallback, memo } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from "recharts";
import { Activity, Clock, TrendingUp } from "lucide-react";
import { fetchSystemResources, SystemResourceHistoryData, formatBytes } from "@/services/systemResourceService";
import { useToast } from "@/hooks/use-toast";

// Memoized chart components for performance
const CPUChart = memo(({ dataPoints }: { dataPoints: Array<any> }) => (
  <ResponsiveContainer width="100%" height="100%" style={{ zIndex: 2, position: 'relative' }}>
    <AreaChart
      data={dataPoints}
      margin={{ top: 8, right: 8, left: 8, bottom: 8 }}
    >
      <defs>
        <linearGradient id="cpuTaskManagerFill" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stopColor="#00d4aa" stopOpacity={0.9}/>
          <stop offset="50%" stopColor="#00d4aa" stopOpacity={0.6}/>
          <stop offset="100%" stopColor="#00d4aa" stopOpacity={0.2}/>
        </linearGradient>
      </defs>
      <XAxis
        dataKey="timestamp"
        type="number"
        domain={['dataMin', 'dataMax']}
        hide
      />
      <YAxis
        domain={[0, 100]}
        hide
      />
      <Tooltip
        content={({ active, payload }) => {
          if (active && payload && payload.length) {
            return (
              <div className="bg-gray-800 text-white border border-gray-500 rounded px-2 py-1 text-xs shadow-lg">
                {Math.round(payload[0].value as number)}%
              </div>
            );
          }
          return null;
        }}
      />
      <Area
        type="monotone"
        dataKey="cpu_usage"
        stroke="#00d4aa"
        strokeWidth={2}
        fill="url(#cpuTaskManagerFill)"
        dot={{ fill: "#00d4aa", strokeWidth: 0, r: 2 }}
        connectNulls={true}
        isAnimationActive={false}
      />
    </AreaChart>
  </ResponsiveContainer>
));

const MemoryChart = memo(({ dataPoints }: { dataPoints: Array<any> }) => (
  <ResponsiveContainer width="100%" height="100%" style={{ zIndex: 2, position: 'relative' }}>
    <AreaChart
      data={dataPoints}
      margin={{ top: 8, right: 8, left: 8, bottom: 8 }}
    >
      <defs>
        <linearGradient id="memoryTaskManagerFill" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stopColor="#0078d4" stopOpacity={0.9}/>
          <stop offset="50%" stopColor="#0078d4" stopOpacity={0.6}/>
          <stop offset="100%" stopColor="#0078d4" stopOpacity={0.2}/>
        </linearGradient>
      </defs>
      <XAxis
        dataKey="timestamp"
        type="number"
        domain={['dataMin', 'dataMax']}
        hide
      />
      <YAxis
        domain={[0, 100]}
        hide
      />
      <Tooltip
        content={({ active, payload }) => {
          if (active && payload && payload.length) {
            return (
              <div className="bg-gray-800 text-white border border-gray-500 rounded px-2 py-1 text-xs shadow-lg">
                {Math.round(payload[0].value as number)}%
              </div>
            );
          }
          return null;
        }}
      />
      <Area
        type="monotone"
        dataKey="memory_usage"
        stroke="#0078d4"
        strokeWidth={2}
        fill="url(#memoryTaskManagerFill)"
        dot={{ fill: "#0078d4", strokeWidth: 0, r: 2 }}
        connectNulls={true}
        isAnimationActive={false}
      />
    </AreaChart>
  </ResponsiveContainer>
));

const NetworkChart = memo(({ dataPoints }: { dataPoints: Array<any> }) => (
  <ResponsiveContainer width="100%" height="100%" style={{ zIndex: 2, position: 'relative' }}>
    <AreaChart
      data={dataPoints}
      margin={{ top: 8, right: 8, left: 8, bottom: 8 }}
    >
      <defs>
        <linearGradient id="networkInTaskManagerFill" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.9}/>
          <stop offset="50%" stopColor="#3b82f6" stopOpacity={0.6}/>
          <stop offset="100%" stopColor="#3b82f6" stopOpacity={0.2}/>
        </linearGradient>
        <linearGradient id="networkOutTaskManagerFill" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.9}/>
          <stop offset="50%" stopColor="#f59e0b" stopOpacity={0.6}/>
          <stop offset="100%" stopColor="#f59e0b" stopOpacity={0.2}/>
        </linearGradient>
      </defs>
      <XAxis
        dataKey="timestamp"
        type="number"
        domain={['dataMin', 'dataMax']}
        hide
      />
      <YAxis hide />
      <Tooltip
        content={({ active, payload }) => {
          if (active && payload && payload.length) {
            return (
              <div className="bg-gray-800 text-white border border-gray-500 rounded px-2 py-1 text-xs shadow-lg">
                {payload.map((entry: any, index: number) => (
                  <div key={index} style={{ color: entry.color }}>
                    {entry.name}: {formatBytes(entry.value)}/s
                  </div>
                ))}
              </div>
            );
          }
          return null;
        }}
      />
      <Area
        type="monotone"
        dataKey="network_in_bytes"
        stackId="1"
        stroke="#3b82f6"
        strokeWidth={2}
        fill="url(#networkInTaskManagerFill)"
        dot={{ fill: "#3b82f6", strokeWidth: 0, r: 2 }}
        connectNulls={true}
        isAnimationActive={false}
        name="Download"
      />
      <Area
        type="monotone"
        dataKey="network_out_bytes"
        stackId="1"
        stroke="#f59e0b"
        strokeWidth={2}
        fill="url(#networkOutTaskManagerFill)"
        dot={{ fill: "#f59e0b", strokeWidth: 0, r: 2 }}
        connectNulls={true}
        isAnimationActive={false}
        name="Upload"
      />
    </AreaChart>
  </ResponsiveContainer>
));

export function ResourceCharts() {
  const { toast } = useToast();
  const [timeframe, setTimeframe] = useState("1h");
  const [loading, setLoading] = useState(true);
  const [historyData, setHistoryData] = useState<SystemResourceHistoryData | null>(null);
  const [dataPoints, setDataPoints] = useState<Array<{
    timestamp: number;
    cpu_usage: number;
    memory_usage: number;
    network_in_bytes: number;
    network_out_bytes: number;
  }>>([]);
  const [currentData, setCurrentData] = useState<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(0);
  const lastUpdateRef = useRef(0);
  const isUpdatingRef = useRef(false);

  const addDataPoint = useCallback(async () => {
    const now = Date.now();

    // Debouncing: Skip if last update was less than 2 seconds ago or if already updating
    if (now - lastUpdateRef.current < 2000 || isUpdatingRef.current) {
      console.warn('Skipping update: too frequent or already updating');
      return;
    }

    lastUpdateRef.current = now;
    isUpdatingRef.current = true;

    try {
      const newData = await fetchSystemResources();
      setCurrentData(newData);

      setDataPoints(prevPoints => {
        // Add new point with current real data and actual timestamp
        const newPoint = {
          timestamp: now,
          cpu_usage: newData.cpu?.usage_percent ?? 0,
          memory_usage: newData.memory?.usage_percent ?? 0,
          network_in_bytes: newData.network?.bytes_recv_per_sec ?? 0,
          network_out_bytes: newData.network?.bytes_sent_per_sec ?? 0
        };

        const updatedPoints = [...prevPoints, newPoint];

        // Keep only last 60 seconds of data (time-based sliding window)
        const cutoffTime = now - (60 * 1000); // 60 seconds ago
        const filteredPoints = updatedPoints.filter(point => point.timestamp >= cutoffTime);

        return filteredPoints;
      });

      setLastUpdateTime(now);
    } catch (error) {
      console.error('Error fetching real-time system data:', error);

      // Handle different types of errors
      if (error.name === 'AbortError') {
        console.warn('API timeout - backend is slow, but graphs will continue with cached data');
        // Don't show error toast for timeouts - the service handles fallback
      } else {
        // Only show toast for non-timeout errors and only on initial load
        if (!isInitialized) {
          toast({
            title: "Error",
            description: "Failed to load system data",
            variant: "destructive"
          });
        }
      }
    } finally {
      isUpdatingRef.current = false;
    }
  }, [isInitialized, toast]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Get initial data
      const initialData = await fetchSystemResources();
      setCurrentData(initialData);

      // Create multiple initial points to show a line immediately
      // This creates a flat line at the current values with proper timestamps
      const now = Date.now();
      const initialPoints = [];
      for (let i = 9; i >= 0; i--) {
        initialPoints.push({
          timestamp: now - (i * 1000), // Each point 1 second apart, ending at current time
          cpu_usage: initialData.cpu?.usage_percent ?? 0,
          memory_usage: initialData.memory?.usage_percent ?? 0,
          network_in_bytes: initialData.network?.bytes_recv_per_sec ?? 0,
          network_out_bytes: initialData.network?.bytes_sent_per_sec ?? 0
        });
      }

      setDataPoints(initialPoints);
      setHistoryData({
        timeframe: timeframe,
        data_points: initialPoints,
        total_points: initialPoints.length
      });

      setIsInitialized(true);
      setLastUpdateTime(Date.now());
    } catch (error) {
      console.error('Error loading initial system data:', error);
      toast({
        title: "Error",
        description: "Failed to load system data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Initialize and set up real-time updates
  useEffect(() => {
    loadInitialData();

    // Set up interval for real-time updates every 2.5 seconds (staggered with SystemResources)
    const interval = setInterval(() => {
      if (isInitialized) {
        addDataPoint();
      }
    }, 2500);

    // Handle browser throttling - force update when tab becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isInitialized) {
        console.log('Tab became visible, forcing update');
        addDataPoint();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [timeframe, isInitialized, addDataPoint]);

  // Update historyData when dataPoints change
  useEffect(() => {
    if (dataPoints.length > 0) {
      setHistoryData({
        timeframe: timeframe,
        data_points: dataPoints,
        total_points: dataPoints.length
      });
    }
  }, [dataPoints, timeframe]);

  const formatXAxisLabel = (tickItem: string) => {
    const date = new Date(tickItem);
    if (timeframe === '1h') {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (timeframe === '6h' || timeframe === '24h') {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium mb-2">
            {new Date(label).toLocaleString()}
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
              {entry.dataKey.includes('bytes') ? '' : '%'}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full"></div>
          <span className="ml-3 text-sm text-gray-500">Loading system data...</span>
        </div>
      </div>
    );
  }

  if (!isInitialized || !currentData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-sm text-gray-500 mb-2">No data available</div>
            <button
              onClick={loadInitialData}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Timeframe Selector */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Resource Usage Trends
          {isInitialized && (
            <span className="text-xs flex items-center gap-1">
              {(currentData as any)?._internal_status?.using_cached_data ? (
                <>
                  <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                  <span className="text-yellow-500">Cached</span>
                </>
              ) : (
                <>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-500">Live</span>
                </>
              )}
            </span>
          )}
        </h3>
        <div className="flex items-center gap-3">
          {lastUpdateTime > 0 && (
            <span className="text-xs text-gray-500">
              Updated {Math.round((Date.now() - lastUpdateTime) / 1000)}s ago
            </span>
          )}
        </div>
      </div>

      {/* CPU and Memory Usage Charts - Exact Task Manager Style */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* CPU Usage Chart - Exact Task Manager Style */}
        <Card className="bg-gray-900 text-white border-gray-600 overflow-hidden">
          <CardHeader className="pb-2 px-4 pt-3">
            <CardTitle className="flex items-center justify-between text-white">
              <div>
                <div className="text-lg font-normal flex items-center gap-2">
                  CPU
                  {currentData?.cpu?.monitoring_method === 'linux_proc' && (
                    <span className="text-xs bg-green-600 text-white px-1 py-0.5 rounded">
                      Enhanced
                    </span>
                  )}
                </div>
                <div className="text-xs text-gray-400">% Utilization</div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-light">
                  {currentData?.cpu?.usage_percent
                    ? `${Math.round(currentData.cpu.usage_percent)}%`
                    : '0%'
                  }
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="h-64 bg-gray-900 relative">
              {/* Grid Background */}
              <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 1 }}>
                <defs>
                  <pattern id="cpuTaskManagerGrid" width="40" height="32" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 32" fill="none" stroke="#404040" strokeWidth="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#cpuTaskManagerGrid)" />
              </svg>

              <CPUChart dataPoints={dataPoints} />
            </div>
            {/* Task Manager style bottom info */}
            <div className="px-4 py-2 text-xs text-gray-400 bg-gray-800 flex justify-between">
              <span>60 seconds</span>
              <span className="text-right">0</span>
            </div>
          </CardContent>
        </Card>

        {/* Memory Usage Chart - Exact Task Manager Style */}
        <Card className="bg-gray-900 text-white border-gray-600 overflow-hidden">
          <CardHeader className="pb-2 px-4 pt-3">
            <CardTitle className="flex items-center justify-between text-white">
              <div>
                <div className="text-lg font-normal">Memory</div>
                <div className="text-xs text-gray-400">% Utilization</div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-light">
                  {currentData?.memory?.usage_percent
                    ? `${Math.round(currentData.memory.usage_percent)}%`
                    : '0%'
                  }
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="h-64 bg-gray-900 relative">
              {/* Grid Background */}
              <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 1 }}>
                <defs>
                  <pattern id="memoryTaskManagerGrid" width="40" height="32" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 32" fill="none" stroke="#404040" strokeWidth="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#memoryTaskManagerGrid)" />
              </svg>

              <MemoryChart dataPoints={dataPoints} />
            </div>
            {/* Task Manager style bottom info */}
            <div className="px-4 py-2 text-xs text-gray-400 bg-gray-800 flex justify-between">
              <span>60 seconds</span>
              <span className="text-right">0</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Network Usage Chart - Task Manager Style */}
      <Card className="bg-gray-900 text-white border-gray-600 overflow-hidden">
        <CardHeader className="pb-2 px-4 pt-3">
          <CardTitle className="flex items-center justify-between text-white">
            <div>
              <div className="text-lg font-normal">Network</div>
              <div className="text-xs text-gray-400">Throughput</div>
            </div>
            <div className="text-right">
              <div className="text-sm font-light">
                {currentData?.network
                  ? `${formatBytes((currentData.network.bytes_recv_per_sec || 0) + (currentData.network.bytes_sent_per_sec || 0))}/s`
                  : '0 B/s'
                }
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="h-64 bg-gray-900 relative">
            {/* Grid Background */}
            <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 1 }}>
              <defs>
                <pattern id="networkTaskManagerGrid" width="40" height="32" patternUnits="userSpaceOnUse">
                  <path d="M 40 0 L 0 0 0 32" fill="none" stroke="#404040" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#networkTaskManagerGrid)" />
            </svg>

            <NetworkChart dataPoints={dataPoints} />
          </div>
          {/* Task Manager style bottom info */}
          <div className="px-4 py-2 text-xs text-gray-400 bg-gray-800 flex justify-between">
            <span>60 seconds</span>
            <span className="text-right">0</span>
          </div>
        </CardContent>
      </Card>


    </div>
  );
}


import { Navbar } from "@/components/layout/Navbar";
import { Sidebar } from "@/components/layout/Sidebar";
import { DashboardStats } from "@/components/dashboard/DashboardStats";
import { AlertsPanel } from "@/components/dashboard/AlertsPanel";
import { SecurityStatusCard } from "@/components/dashboard/SecurityStatusCard";
import { ThreatDetection } from "@/components/dashboard/ThreatDetection";
import { Terminal } from "@/components/dashboard/Terminal";
import { SetupGuide } from "@/components/dashboard/SetupGuide";
import { Card } from "@/components/ui/card";
import { ShieldCheck, ShieldAlert, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";

const Index = () => {
  const [systemActive, setSystemActive] = useState(false);
  const [backendConnected, setBackendConnected] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Check if the backend is available
    const checkBackend = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/ids/status', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setBackendConnected(true);
          setSystemActive(data.running);
        } else {
          setBackendConnected(false);
        }
      } catch (error) {
        console.error("Backend connection error:", error);
        setBackendConnected(false);
      }
    };

    // Check initially
    checkBackend();

    // Check more frequently (every 5 seconds) for real-time status updates
    const interval = setInterval(checkBackend, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleSystemToggle = async () => {
    if (!backendConnected) {
      toast({
        title: "Backend Unavailable",
        description: "Cannot toggle system state as the IDS backend is not connected.",
        variant: "destructive"
      });
      return;
    }

    try {
      const response = await fetch(`http://localhost:8000/api/ids/${systemActive ? 'stop' : 'start'}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          interface: 'wlan0',
          interval: 2.0
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to ${systemActive ? 'stop' : 'start'} IDS: ${response.statusText}`);
      }

      const result = await response.json();
      const newState = !systemActive;
      setSystemActive(newState);

      toast({
        title: newState ? "System Activated" : "System Deactivated",
        description: result.message || (newState
          ? "The intrusion detection system is now monitoring your network."
          : "The intrusion detection system has been stopped.")
      });

    } catch (error) {
      console.error(error);
      toast({
        title: "Operation Failed",
        description: String(error),
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen flex">
      <Sidebar />

      <div className="flex-1 flex flex-col min-h-screen md:pl-64">
        <Navbar />

        <main className="flex-1 p-4 md:p-6 overflow-auto">
          {!backendConnected && (
            <Alert variant="destructive" className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Backend Not Connected</AlertTitle>
              <AlertDescription className="flex flex-col gap-2">
                <p>The IDS backend service is not available. Please ensure the Django backend is running at http://localhost:8000</p>
                <div className="flex justify-end">
                  <SetupGuide />
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">Security Dashboard</h1>
              <p className="text-muted-foreground">
                Real-time network monitoring and threat detection
              </p>
            </div>

            <div className="flex gap-2 items-center">
              {!backendConnected && <SetupGuide />}
              <Button
                size="lg"
                className={`gap-2 ${systemActive ? 'bg-security-safe hover:bg-security-safe/90' : 'bg-destructive hover:bg-destructive/90'}`}
                onClick={handleSystemToggle}
                disabled={!backendConnected}
              >
                {systemActive ? <ShieldCheck className="h-5 w-5" /> : <ShieldAlert className="h-5 w-5" />}
                {systemActive ? 'System Active' : 'System Inactive'}
              </Button>
            </div>
          </div>

          <div className="space-y-6">
            <DashboardStats />

            <SecurityStatusCard />

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
              <AlertsPanel className="lg:col-span-2" />
              <div className="lg:col-span-3 space-y-6">

                <Terminal />
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Index;

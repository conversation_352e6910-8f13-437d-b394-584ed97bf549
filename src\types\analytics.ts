
export interface ThreatStat {
  name: string;
  value: number;
  percent: number;
}

export interface TrendData {
  date: string;
  attacks: number;
  traffic: number;
}

export interface AttackHourData {
  hour: string;
  attacks: number;
}

export interface GeographicData {
  country: string;
  value: number;
  attackCount: number;
}

export interface AnalyticsData {
  threatStats: ThreatStat[];
  attackTrends: TrendData[];
  attackHours: AttackHourData[];
  geographicData: GeographicData[];
  totalAttacks: number;
  changePercent: number;
}

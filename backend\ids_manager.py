
import threading
import json
import asyncio
import logging
import time
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from .models import ID<PERSON>lert, IDSLog
from .realtime_ids import RealTimeIDS

logger = logging.getLogger('django')

class IDSManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(IDSManager, cls).__new__(cls)
            cls._instance.ids = None
            cls._instance.is_running = False
            cls._instance.thread = None
            cls._instance.channel_layer = get_channel_layer()
        return cls._instance
    
    def start_ids(self, interface="wlan0", interval=2.0):
        if self.is_running:
            return {'status': 'error', 'message': 'IDS is already running'}
        
        try:
            # Initialize the IDS instance with paths to model files
            self.ids = RealTimeIDS(
                model_path='./ids/model_files/DecisionTree_model.pkl',
                label_array_path='./ids/model_files/label_array.npy',
                feature_columns_path='./ids/model_files/DecisionTree_feature_columns.txt',
                capture_interval=interval
            )
            
            # Hook into the IDS log and alert system
            self._hook_logging()
            self._hook_alerts()
            
            # Start the IDS in a separate thread
            self.thread = threading.Thread(target=self._run_ids, args=(interface,))
            self.thread.daemon = True
            self.thread.start()
            
            self.is_running = True
            return {'status': 'success', 'message': 'IDS started successfully'}
        
        except Exception as e:
            logger.error(f"Error starting IDS: {str(e)}")
            return {'status': 'error', 'message': f'Failed to start IDS: {str(e)}'}
    
    def stop_ids(self):
        if not self.is_running:
            return {'status': 'error', 'message': 'IDS is not running'}
        
        try:
            if self.ids:
                self.ids.stop()
            
            self.is_running = False
            self.ids = None
            return {'status': 'success', 'message': 'IDS stopped successfully'}
        
        except Exception as e:
            logger.error(f"Error stopping IDS: {str(e)}")
            return {'status': 'error', 'message': f'Failed to stop IDS: {str(e)}'}
    
    def get_status(self):
        return {
            'running': self.is_running,
            'stats': self.ids.stats if self.ids else None
        }
    
    def _run_ids(self, interface):
        try:
            # Don't use signal handlers in threaded code
            self.ids.start(interface)
        except Exception as e:
            logger.error(f"Error in IDS thread: {str(e)}")
            self.is_running = False
    
    def _hook_logging(self):
        # Replace the standard logger handler with one that sends logs to WebSocket
        original_warning = logging.Logger.warning
        original_info = logging.Logger.info
        original_error = logging.Logger.error
        
        def custom_warning(self, msg, *args, **kwargs):
            # Call original method
            original_warning(self, msg, *args, **kwargs)
            
            # Save to DB and send via WebSocket
            IDSManager._instance._handle_log('WARNING', msg)
        
        def custom_info(self, msg, *args, **kwargs):
            # Call original method
            original_info(self, msg, *args, **kwargs)
            
            # Save to DB and send via WebSocket
            IDSManager._instance._handle_log('INFO', msg)
        
        def custom_error(self, msg, *args, **kwargs):
            # Call original method
            original_error(self, msg, *args, **kwargs)
            
            # Save to DB and send via WebSocket
            IDSManager._instance._handle_log('ERROR', msg)
        
        # Replace the methods
        logging.Logger.warning = custom_warning
        logging.Logger.info = custom_info
        logging.Logger.error = custom_error
    
    def _hook_alerts(self):
        # Patch the process_alerts method to send alerts to WebSocket
        original_process_alerts = self.ids.process_alerts
        
        def custom_process_alerts(results_df):
            # Call original method
            original_process_alerts(results_df)
            
            # Process the alerts we just generated
            if hasattr(self.ids, 'alert_history') and self.ids.alert_history:
                for alert in list(self.ids.alert_history)[-5:]:  # Get the 5 most recent alerts
                    self._handle_alert(alert)
        
        # Replace the method
        self.ids.process_alerts = custom_process_alerts
    
    def _handle_log(self, level, message):
        # Create log entry in DB
        log = IDSLog.objects.create(level=level, message=message)
        
        # Send via WebSocket
        try:
            async_to_sync(self.channel_layer.group_send)(
                'ids_logs',
                {
                    'type': 'log_message',
                    'message': message,
                    'level': level
                }
            )
        except Exception as e:
            logger.error(f"Error sending log to WebSocket: {str(e)}")
    
    def _handle_alert(self, alert):
        # Create alert entry in DB
        db_alert = IDSAlert.objects.create(
            src_ip=alert['src_ip'],
            dst_ip=alert['dst_ip'],
            src_port=alert['src_port'],
            dst_port=alert['dst_port'],
            attack_type=alert['attack_type']
        )
        
        # Format target string
        target = f"{alert['dst_ip']}:{alert['dst_port']}"
        
        # Send via WebSocket
        try:
            async_to_sync(self.channel_layer.group_send)(
                'ids_alerts',
                {
                    'type': 'alert_message',
                    'id': db_alert.id,
                    'timestamp': db_alert.timestamp.strftime('%H:%M:%S'),
                    'type': alert['attack_type'],
                    'source': f"{alert['src_ip']}:{alert['src_port']}",
                    'target': target
                }
            )
        except Exception as e:
            logger.error(f"Error sending alert to WebSocket: {str(e)}")

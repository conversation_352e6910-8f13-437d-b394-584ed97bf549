# Generated migration for consolidated CSV settings

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ids', '0004_idssettings'),
    ]

    operations = [
        migrations.AddField(
            model_name='idssettings',
            name='consolidated_csv',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='idssettings',
            name='csv_save_interval',
            field=models.FloatField(default=300.0),
        ),
    ]

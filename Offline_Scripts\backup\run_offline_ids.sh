#!/bin/bash

# Offline IDS Runner Script
# This script runs the complete offline IDS workflow: extract flows and evaluate them

set -e  # Exit on any error

# Default parameters
INTERFACE="wlan0"
DURATION=60
INTERVAL=10
FLOWS_FILE="extracted_flows.csv"
RESULTS_FILE="evaluation_results.csv"
VERBOSE=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -i, --interface INTERFACE    Network interface to capture from (default: wlan0)"
    echo "  -d, --duration SECONDS       Capture duration in seconds (default: 60)"
    echo "  -t, --interval SECONDS       Flow aggregation interval (default: 10)"
    echo "  -f, --flows-file FILE        Output file for extracted flows (default: extracted_flows.csv)"
    echo "  -r, --results-file FILE      Output file for evaluation results (default: evaluation_results.csv)"
    echo "  -v, --verbose                Enable verbose output"
    echo "  -h, --help                   Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Use default settings"
    echo "  $0 -i eth0 -d 300                   # Capture on eth0 for 5 minutes"
    echo "  $0 -d 120 -v                        # 2-minute capture with verbose output"
    echo "  $0 -f my_flows.csv -r my_results.csv # Custom output files"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--interface)
            INTERFACE="$2"
            shift 2
            ;;
        -d|--duration)
            DURATION="$2"
            shift 2
            ;;
        -t|--interval)
            INTERVAL="$2"
            shift 2
            ;;
        -f|--flows-file)
            FLOWS_FILE="$2"
            shift 2
            ;;
        -r|--results-file)
            RESULTS_FILE="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE="-v"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if running as root for packet capture
if [[ $EUID -ne 0 ]]; then
    print_error "This script must be run as root for packet capture."
    print_info "Please run: sudo $0 $@"
    exit 1
fi

# Check if required files exist
print_info "Checking setup..."
if [[ ! -f "flow_extractor.py" ]]; then
    print_error "flow_extractor.py not found in current directory"
    exit 1
fi

if [[ ! -f "flow_evaluator.py" ]]; then
    print_error "flow_evaluator.py not found in current directory"
    exit 1
fi

if [[ ! -f "DecisionTree_model.pkl" ]]; then
    print_error "DecisionTree_model.pkl not found in current directory"
    exit 1
fi

print_success "Setup check passed"

# Show configuration
print_info "Configuration:"
echo "  Interface: $INTERFACE"
echo "  Duration: $DURATION seconds"
echo "  Interval: $INTERVAL seconds"
echo "  Flows file: $FLOWS_FILE"
echo "  Results file: $RESULTS_FILE"
echo "  Verbose: $([ -n "$VERBOSE" ] && echo "enabled" || echo "disabled")"
echo ""

# Step 1: Extract flows
print_info "Step 1: Extracting network flows..."
print_info "Capturing packets on interface $INTERFACE for $DURATION seconds..."

if python3 flow_extractor.py -i "$INTERFACE" -d "$DURATION" -t "$INTERVAL" -o "$FLOWS_FILE" $VERBOSE; then
    print_success "Flow extraction completed successfully"
    
    # Check if flows were extracted
    if [[ -f "$FLOWS_FILE" ]]; then
        FLOW_COUNT=$(tail -n +2 "$FLOWS_FILE" | wc -l)
        print_info "Extracted $FLOW_COUNT flows to $FLOWS_FILE"
    else
        print_error "Flow file was not created"
        exit 1
    fi
else
    print_error "Flow extraction failed"
    exit 1
fi

# Step 2: Evaluate flows
print_info "Step 2: Evaluating flows for threats..."

if python3 flow_evaluator.py -i "$FLOWS_FILE" -o "$RESULTS_FILE" $VERBOSE; then
    print_success "Flow evaluation completed successfully"
    
    # Show results summary
    if [[ -f "$RESULTS_FILE" ]]; then
        print_info "Results saved to $RESULTS_FILE"
        
        # Count threats if possible
        if command -v python3 >/dev/null 2>&1; then
            THREAT_SUMMARY=$(python3 -c "
import pandas as pd
try:
    df = pd.read_csv('$RESULTS_FILE')
    if 'Threat' in df.columns:
        counts = df['Threat'].value_counts()
        print(f'Total flows: {len(df)}')
        for threat, count in counts.items():
            print(f'{threat}: {count}')
    else:
        print('Threat column not found')
except Exception as e:
    print(f'Error reading results: {e}')
")
            print_info "Threat analysis summary:"
            echo "$THREAT_SUMMARY" | sed 's/^/  /'
        fi
    else
        print_error "Results file was not created"
        exit 1
    fi
else
    print_error "Flow evaluation failed"
    exit 1
fi

# Final summary
print_success "Offline IDS analysis completed successfully!"
print_info "Files created:"
echo "  - $FLOWS_FILE (extracted flows)"
echo "  - $RESULTS_FILE (threat analysis results)"
print_info "You can now analyze the results or import them into other tools."


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta

from .models import ID<PERSON>lert, IDSLog
from .serializers import IDSLogSerializer
from .ids_manager import IDSManager

class IDSStartView(APIView):
    def post(self, request):
        interface = request.data.get('interface', None)
        interval = float(request.data.get('interval', 2.0))
        
        result = IDSManager().start_ids(interface, interval)
        
        if result['status'] == 'success':
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

class IDSStopView(APIView):
    def post(self, request):
        result = IDSManager().stop_ids()
        
        if result['status'] == 'success':
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

class IDSStatusView(APIView):
    def get(self, request):
        ids_manager = IDSManager()
        status_data = ids_manager.get_status()
        
        # Enhance stats with additional data needed by the frontend
        if status_data.get('stats'):
            stats = status_data['stats']

            # Calculate real change percent based on historical data
            stats['change_percent'] = self._calculate_change_percent()

        # Include attack session status if IDS is running
        if status_data['running'] and ids_manager.ids:
            status_data['attack_session'] = ids_manager.ids.get_attack_session_status()

        return Response(status_data, status=status.HTTP_200_OK)

    def _calculate_change_percent(self):
        """Calculate change percentage based on recent alert activity"""
        try:
            # Get alerts from last hour and previous hour
            now = datetime.now()
            current_hour_start = now.replace(minute=0, second=0, microsecond=0)
            previous_hour_start = current_hour_start - timedelta(hours=1)

            current_hour_alerts = IDSAlert.objects.filter(
                timestamp__gte=current_hour_start,
                timestamp__lt=current_hour_start + timedelta(hours=1)
            ).count()

            previous_hour_alerts = IDSAlert.objects.filter(
                timestamp__gte=previous_hour_start,
                timestamp__lt=current_hour_start
            ).count()

            if previous_hour_alerts > 0:
                change_percent = ((current_hour_alerts - previous_hour_alerts) / previous_hour_alerts) * 100
                return round(change_percent, 1)
            elif current_hour_alerts > 0:
                return 100.0  # New alerts when there were none before
            else:
                return 0.0  # No change

        except Exception:
            return 0.0

class IDSAlertsView(APIView):
    def get(self, request):
        alerts = IDSAlert.objects.all().order_by('-timestamp')[:30]  # Get the 30 most recent alerts
        
        # Transform to the format expected by the frontend
        alert_data = []
        for alert in alerts:
            alert_data.append({
                'id': alert.id,
                'timestamp': alert.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                'type': alert.attack_type,
                'source': f"{alert.src_ip}:{alert.src_port}",
                'target': f"{alert.dst_ip}:{alert.dst_port}",
                'status': alert.status
            })
        
        return Response({'alerts': alert_data}, status=status.HTTP_200_OK)

class IDSLogsView(APIView):
    def get(self, request):
        logs = IDSLog.objects.all().order_by('-timestamp')[:100]  # Get the 100 most recent logs
        serializer = IDSLogSerializer(logs, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

# IDSTrafficView moved to data_views.py to avoid duplication



class IDSAnalyticsView(APIView):
    def get(self, request):
        """Return analytics data for visualization"""
        # Get timeframe from query parameters (default to '7d')
        timeframe = request.query_params.get('timeframe', '7d')
        
        # Get alerts from database based on timeframe
        if timeframe == '24h':
            time_threshold = datetime.now() - timedelta(hours=24)
        elif timeframe == '7d':
            time_threshold = datetime.now() - timedelta(days=7)
        elif timeframe == '30d':
            time_threshold = datetime.now() - timedelta(days=30)
        elif timeframe == '90d':
            time_threshold = datetime.now() - timedelta(days=90)
        else:
            # Default to 7 days
            time_threshold = datetime.now() - timedelta(days=7)
        
        # Get alerts in the selected timeframe
        alerts = IDSAlert.objects.filter(timestamp__gte=time_threshold)
        
        # Calculate total attacks and change percentage
        total_attacks = alerts.count()
        
        # For comparison, get counts from previous period
        if timeframe == '24h':
            prev_time_threshold = time_threshold - timedelta(hours=24)
        elif timeframe == '7d':
            prev_time_threshold = time_threshold - timedelta(days=7)
        elif timeframe == '30d':
            prev_time_threshold = time_threshold - timedelta(days=30)
        elif timeframe == '90d':
            prev_time_threshold = time_threshold - timedelta(days=90)
        
        prev_alerts_count = IDSAlert.objects.filter(
            timestamp__gte=prev_time_threshold,
            timestamp__lt=time_threshold
        ).count()
        
        # Calculate change percentage
        if prev_alerts_count > 0:
            change_percent = ((total_attacks - prev_alerts_count) / prev_alerts_count) * 100
        else:
            change_percent = 100 if total_attacks > 0 else 0
        
        # Generate threat statistics using database aggregation (optimized)
        from django.db.models import Count

        attack_type_counts = alerts.values('attack_type').annotate(
            count=Count('attack_type')
        ).order_by('-count')

        # Format threat stats
        threat_stats = []
        for item in attack_type_counts:
            count = item['count']
            percent = (count / total_attacks) * 100 if total_attacks > 0 else 0
            threat_stats.append({
                'name': item['attack_type'],
                'value': count,
                'percent': round(percent)
            })
        
        # Generate attack trends based on timeframe
        attack_trends = []
        
        if timeframe == '24h':
            # Hourly data for 24 hours
            for hour in range(24):
                date_point = datetime.now() - timedelta(hours=23-hour)
                date_str = date_point.strftime('%H:00')
                
                # Get real counts for this hour
                hour_start = datetime(date_point.year, date_point.month, date_point.day, date_point.hour)
                hour_end = hour_start + timedelta(hours=1)
                hour_count = alerts.filter(timestamp__gte=hour_start, timestamp__lt=hour_end).count()

                # Estimate traffic based on alerts (each alert represents multiple flows)
                estimated_traffic = hour_count * 25 if hour_count > 0 else 0

                attack_trends.append({
                    'date': date_str,
                    'attacks': hour_count,
                    'traffic': estimated_traffic
                })
        
        elif timeframe == '7d':
            # Daily data for 7 days
            for day in range(7):
                date_point = datetime.now() - timedelta(days=6-day)
                date_str = date_point.strftime('%m/%d')
                
                # Get real counts for this day
                day_start = datetime(date_point.year, date_point.month, date_point.day)
                day_end = day_start + timedelta(days=1)
                day_count = alerts.filter(timestamp__gte=day_start, timestamp__lt=day_end).count()

                # Estimate traffic based on alerts
                estimated_traffic = day_count * 25 if day_count > 0 else 0

                attack_trends.append({
                    'date': date_str,
                    'attacks': day_count,
                    'traffic': estimated_traffic
                })
        
        elif timeframe in ['30d', '90d']:
            # Weekly data for 30 or 90 days
            weeks = 4 if timeframe == '30d' else 13
            for week in range(weeks):
                week_ago = weeks - 1 - week
                date_point = datetime.now() - timedelta(days=week_ago*7)
                date_str = date_point.strftime('%m/%d')
                
                # Get real counts for this week
                week_start = datetime.now() - timedelta(days=(week_ago+1)*7)
                week_end = datetime.now() - timedelta(days=week_ago*7)
                week_count = alerts.filter(timestamp__gte=week_start, timestamp__lt=week_end).count()

                # Estimate traffic based on alerts
                estimated_traffic = week_count * 25 if week_count > 0 else 0

                attack_trends.append({
                    'date': date_str,
                    'attacks': week_count,
                    'traffic': estimated_traffic
                })
        
        # Generate attack hour distribution (when attacks occur)
        attack_hours = []
        for hour in range(24):
            hour_str = f"{hour:02d}:00"
            
            # Get real counts for this hour of day across timeframe
            hour_count = alerts.filter(timestamp__hour=hour).count()
            
            attack_hours.append({
                'hour': hour_str,
                'attacks': hour_count
            })
        
        # Get real geographic data from alerts
        geographic_data = self._get_real_geographic_data(alerts)
        
        return Response({
            'threatStats': threat_stats,
            'attackTrends': attack_trends,
            'attackHours': attack_hours,
            'geographicData': geographic_data,
            'totalAttacks': total_attacks,
            'changePercent': round(change_percent, 1)
        }, status=status.HTTP_200_OK)

    def _get_real_geographic_data(self, alerts):
        """Generate geographic attack distribution data based on real IP addresses"""
        try:
            # Simple IP prefix to country mapping for demonstration
            # In a real system, you would use a GeoIP database
            country_mapping = {
                '10.': 'Local Network',
                '192.168.': 'Local Network',
                '172.': 'Local Network',
                '45.33': 'United States',
                '61.177': 'China',
                '91.197': 'Russia',
                '181.49': 'Brazil',
                '116.12': 'India',
                '195.176': 'Germany',
                '212.98': 'France',
                '194.54': 'Ukraine'
            }

            country_counts = {}

            # Analyze source IPs from real alerts (optimized to use values_list)
            src_ips = alerts.values_list('src_ip', flat=True)

            for src_ip in src_ips:
                country = 'Unknown'

                # Find matching country based on IP prefix
                for prefix, mapped_country in country_mapping.items():
                    if src_ip.startswith(prefix):
                        country = mapped_country
                        break

                if country in country_counts:
                    country_counts[country] += 1
                else:
                    country_counts[country] = 1

            # Convert to the expected format
            geographic_data = []
            for country, count in country_counts.items():
                geographic_data.append({
                    'country': country,
                    'value': count,
                    'attackCount': count
                })

            # Sort by attack count
            geographic_data.sort(key=lambda x: x['attackCount'], reverse=True)

            return geographic_data

        except Exception as e:
            # Return empty data if there's an error
            return []




import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ThreatStat } from "@/types/analytics";

interface AnalyticsSummaryProps {
  loading: boolean;
  isUpdating?: boolean;
  threatStats: ThreatStat[];
  totalAttacks: number;
  changePercent: number;
}

export function AnalyticsSummary({ loading, isUpdating = false, threatStats, totalAttacks, changePercent }: AnalyticsSummaryProps) {
  const COLORS = ['#FF4842', '#FFAE06', '#0088FE', '#00C49F', '#AAAAAA'];

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Total Attacks</CardTitle>
          <Badge
            variant="destructive"
            className="h-5 w-5 p-0 flex items-center justify-center rounded-full"
          >
            !
          </Badge>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
          ) : (
            <>
              <div className="text-2xl font-bold">{totalAttacks}</div>
              <p className="text-xs text-muted-foreground">
                {changePercent >= 0 ? "+" : ""}{changePercent}% from previous period
              </p>
            </>
          )}
        </CardContent>
      </Card>
      
      <Card className="col-span-3">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Attack Distribution by Type</CardTitle>
            <Badge variant="outline" className="gap-1 px-2 py-1 text-xs">
              <span className="h-3 w-3">📊</span>
              Threat Stats
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="h-8 w-full bg-muted animate-pulse rounded"></div>
          ) : (
            <div className="flex flex-wrap items-center gap-2">
              {threatStats.map((item, index) => (
                <Badge 
                  key={index} 
                  variant="outline"
                  className="flex items-center gap-1 px-2 py-1"
                >
                  <div 
                    className="h-2 w-2 rounded-full" 
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  ></div>
                  {item.name}: {item.value} ({item.percent}%)
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

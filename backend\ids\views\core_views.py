
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import logging

from ..models import IDSSettings, IDSAlert
from ..ids_manager import IDSManager
from ..utils.cache_utils import PerformanceCache, QueryOptimizer

logger = logging.getLogger(__name__)

class IDSStartView(APIView):
    def post(self, request):
        # Get parameters from request or use defaults
        interface = request.data.get('interface', None)
        interval = float(request.data.get('interval', 2.0))

        # Get saved settings from database
        settings = IDSSettings.get_settings()

        # If interface is not specified in the request, use the one from settings
        if interface is None:
            interface = settings.capture_interface

        # If interval is default, use the one from settings
        if interval == 2.0:
            interval = settings.capture_interval

        # Debug logging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Starting IDS with settings:")
        logger.info(f"  - Interface: {interface}")
        logger.info(f"  - Capture Interval: {interval}")
        logger.info(f"  - Max Idle Time: {settings.max_idle_time}")
        logger.info(f"  - PCAP Capture: {settings.pcap_capture}")

        # Start IDS with settings from database
        result = IDSManager().start_ids_with_settings(settings, interface, interval)

        if result['status'] == 'success':
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

class IDSStopView(APIView):
    def post(self, request):
        result = IDSManager().stop_ids()

        if result['status'] == 'success':
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

class IDSStatusView(APIView):
    def get(self, request):
        import time
        import logging

        logger = logging.getLogger('django')
        start_time = time.time()

        ids_manager = IDSManager()
        status_data = ids_manager.get_status()

        # Enhance stats with additional data needed by the frontend
        if status_data.get('stats'):
            stats = status_data['stats']

            # Add database-based threat counts
            db_start = time.time()
            stats['database_threats'] = self._get_database_threat_stats()
            db_time = time.time() - db_start

            # Calculate real change percent based on historical data
            change_start = time.time()
            stats['change_percent'] = self._calculate_change_percent()
            change_time = time.time() - change_start

            # Calculate real network stats based on active flows
            network_start = time.time()
            stats['network_stats'] = self._calculate_network_stats(ids_manager)
            network_time = time.time() - network_start

            # Log performance metrics
            total_time = time.time() - start_time
            if total_time > 0.1:  # Log if response takes more than 100ms
                logger.info(f"Status API performance: total={total_time:.3f}s, "
                           f"db_threats={db_time:.3f}s, change={change_time:.3f}s, "
                           f"network={network_time:.3f}s")

        return Response(status_data, status=status.HTTP_200_OK)

    def _get_database_threat_stats(self):
        """Get threat statistics from the database using optimized queries"""
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count
        from django.core.cache import cache

        # Check cache first (30-second cache for status data)
        cache_key = 'threat_stats_cache'
        cached_stats = cache.get(cache_key)
        if cached_stats:
            return cached_stats

        # Get total threats and recent threats in a single optimized query
        last_24h = timezone.now() - timedelta(hours=24)

        # Use database aggregation instead of Python loops
        total_threats = IDSAlert.objects.count()
        recent_threats = IDSAlert.objects.filter(timestamp__gte=last_24h).count()

        # Get attack type distribution using SQL aggregation (much faster)
        attack_type_counts = IDSAlert.objects.values('attack_type').annotate(
            count=Count('attack_type')
        ).order_by('-count')

        # Convert to the expected format
        attack_types = {}
        for item in attack_type_counts:
            attack_types[item['attack_type']] = item['count']

        stats = {
            'total_threats': total_threats,
            'recent_threats_24h': recent_threats,
            'attack_types': attack_types
        }

        # Cache for 30 seconds to reduce database load
        cache.set(cache_key, stats, 30)

        return stats

    def _calculate_change_percent(self):
        """Calculate real change percentage based on historical alert data with caching"""
        try:
            from datetime import datetime, timedelta
            from ..models import IDSAlert
            from django.core.cache import cache

            # Check cache first (cache for 5 minutes since this is less critical)
            cache_key = 'change_percent_cache'
            cached_percent = cache.get(cache_key)
            if cached_percent is not None:
                return cached_percent

            now = datetime.now()
            current_hour_start = now.replace(minute=0, second=0, microsecond=0)
            previous_hour_start = current_hour_start - timedelta(hours=1)

            # Use a single query with conditional aggregation for better performance
            from django.db.models import Count, Q

            # Count alerts in both time periods in one query
            counts = IDSAlert.objects.aggregate(
                current_count=Count('id', filter=Q(
                    timestamp__gte=current_hour_start,
                    timestamp__lt=current_hour_start + timedelta(hours=1)
                )),
                previous_count=Count('id', filter=Q(
                    timestamp__gte=previous_hour_start,
                    timestamp__lt=current_hour_start
                ))
            )

            current_count = counts['current_count']
            previous_count = counts['previous_count']

            if previous_count == 0:
                change_percent = 100.0 if current_count > 0 else 0.0
            else:
                change_percent = ((current_count - previous_count) / previous_count) * 100

            result = round(change_percent, 1)

            # Cache for 5 minutes
            cache.set(cache_key, result, 300)

            return result

        except Exception:
            return 0.0

    def _calculate_network_stats(self, ids_manager):
        """Calculate real network statistics based on active flows and alerts with caching"""
        try:
            from datetime import datetime, timedelta
            from ..models import IDSAlert
            from django.core.cache import cache

            # Check cache first (cache for 2 minutes since network stats change frequently)
            cache_key = 'network_stats_cache'
            cached_stats = cache.get(cache_key)
            if cached_stats:
                return cached_stats

            # Get unique IP addresses from recent flows/alerts using database aggregation
            recent_time = datetime.now() - timedelta(minutes=30)

            # Use database aggregation to get unique IPs more efficiently
            recent_alerts = IDSAlert.objects.filter(timestamp__gte=recent_time).values('src_ip', 'dst_ip')

            # Collect unique IPs
            unique_ips = set()
            threat_ips = set()

            for alert in recent_alerts:
                unique_ips.add(alert['src_ip'])
                unique_ips.add(alert['dst_ip'])
                threat_ips.add(alert['src_ip'])  # Source IPs are threats

            # If IDS is running, get additional IPs from active flows
            if ids_manager.is_running and ids_manager.ids:
                try:
                    for flow_key in ids_manager.ids.flows.keys():
                        unique_ips.add(flow_key[0])  # src_ip
                        unique_ips.add(flow_key[1])  # dst_ip
                except (AttributeError, RuntimeError):
                    # Handle case where flows dict is being modified during iteration
                    pass

            total_nodes = len(unique_ips)
            threat_nodes = len(threat_ips)

            # Estimate secure and warning nodes
            # Warning nodes: IPs that appear in flows but not as threat sources
            warning_nodes = max(0, min(2, total_nodes - threat_nodes))
            secure_nodes = max(0, total_nodes - threat_nodes - warning_nodes)

            stats = {
                'secure_nodes': secure_nodes,
                'warning_nodes': warning_nodes,
                'threat_nodes': threat_nodes,
                'total_nodes': total_nodes
            }

            # Cache for 2 minutes
            cache.set(cache_key, stats, 120)

            return stats

        except Exception:
            # Fallback to minimal stats
            return {
                'secure_nodes': 0,
                'warning_nodes': 0,
                'threat_nodes': 0,
                'total_nodes': 0
            }




"""
Cache utilities for IDS performance optimization
"""
import time
import hashlib
from typing import Any, Optional, Callable
from django.core.cache import cache


class PerformanceCache:
    """
    High-performance caching utility for IDS operations
    """
    
    @staticmethod
    def get_or_compute(
        cache_key: str,
        compute_func: Callable[[], Any],
        timeout: int = 60,
        force_refresh: bool = False
    ) -> Any:
        """
        Get cached value or compute and cache it
        
        Args:
            cache_key: Unique cache key
            compute_func: Function to compute the value if not cached
            timeout: Cache timeout in seconds
            force_refresh: Force recomputation even if cached
            
        Returns:
            Cached or computed value
        """
        if not force_refresh:
            cached_value = cache.get(cache_key)
            if cached_value is not None:
                return cached_value
        
        # Compute new value
        start_time = time.time()
        value = compute_func()
        compute_time = time.time() - start_time
        
        # Cache the result
        cache.set(cache_key, value, timeout)
        
        # Log slow computations
        if compute_time > 0.1:  # Log if computation takes more than 100ms
            import logging
            logger = logging.getLogger('django')
            logger.info(f"Slow computation cached: {cache_key} took {compute_time:.3f}s")
        
        return value
    
    @staticmethod
    def generate_cache_key(*args, **kwargs) -> str:
        """
        Generate a consistent cache key from arguments
        
        Args:
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            MD5 hash of the arguments as cache key
        """
        # Create a string representation of all arguments
        key_data = str(args) + str(sorted(kwargs.items()))
        
        # Generate MD5 hash for consistent key
        return hashlib.md5(key_data.encode()).hexdigest()
    
    @staticmethod
    def invalidate_pattern(pattern: str) -> int:
        """
        Invalidate cache keys matching a pattern
        
        Args:
            pattern: Pattern to match (simple string matching)
            
        Returns:
            Number of keys invalidated
        """
        # Note: Django's default cache doesn't support pattern deletion
        # This is a placeholder for future Redis implementation
        # For now, we'll just clear specific known keys
        
        known_keys = [
            'threat_stats_cache',
            'change_percent_cache', 
            'network_stats_cache'
        ]
        
        invalidated = 0
        for key in known_keys:
            if pattern in key:
                cache.delete(key)
                invalidated += 1
        
        return invalidated
    
    @staticmethod
    def clear_all_ids_cache():
        """Clear all IDS-related cache entries"""
        keys_to_clear = [
            'threat_stats_cache',
            'change_percent_cache',
            'network_stats_cache'
        ]
        
        for key in keys_to_clear:
            cache.delete(key)


class QueryOptimizer:
    """
    Database query optimization utilities
    """
    
    @staticmethod
    def get_alert_stats_optimized(timeframe_hours: int = 24):
        """
        Get optimized alert statistics using database aggregation
        
        Args:
            timeframe_hours: Hours to look back for recent stats
            
        Returns:
            Dictionary with optimized alert statistics
        """
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count, Q
        from ..models import IDSAlert
        
        cache_key = f"alert_stats_{timeframe_hours}h"
        
        def compute_stats():
            cutoff_time = timezone.now() - timedelta(hours=timeframe_hours)
            
            # Single query to get all needed statistics
            stats = IDSAlert.objects.aggregate(
                total_alerts=Count('id'),
                recent_alerts=Count('id', filter=Q(timestamp__gte=cutoff_time))
            )
            
            # Get attack type distribution in a separate optimized query
            attack_types = dict(
                IDSAlert.objects.values('attack_type')
                .annotate(count=Count('attack_type'))
                .values_list('attack_type', 'count')
            )
            
            return {
                'total_alerts': stats['total_alerts'],
                'recent_alerts': stats['recent_alerts'],
                'attack_types': attack_types,
                'computed_at': timezone.now().isoformat()
            }
        
        return PerformanceCache.get_or_compute(
            cache_key, 
            compute_stats, 
            timeout=30  # 30-second cache
        )


# Decorator for caching view methods
def cache_view_result(timeout: int = 60, key_prefix: str = "view"):
    """
    Decorator to cache view method results
    
    Args:
        timeout: Cache timeout in seconds
        key_prefix: Prefix for cache key
    """
    def decorator(func):
        def wrapper(self, *args, **kwargs):
            # Generate cache key from function name and arguments
            cache_key = f"{key_prefix}_{func.__name__}_{PerformanceCache.generate_cache_key(*args, **kwargs)}"
            
            def compute_result():
                return func(self, *args, **kwargs)
            
            return PerformanceCache.get_or_compute(
                cache_key,
                compute_result,
                timeout
            )
        
        return wrapper
    return decorator

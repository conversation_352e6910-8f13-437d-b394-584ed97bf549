# Generated migration to update max_idle_time default value

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ids', '0012_add_performance_indexes'),
    ]

    operations = [
        migrations.AlterField(
            model_name='idssettings',
            name='max_idle_time',
            field=models.FloatField(default=1.0),
        ),
        # Update existing records to use the new default if they're still using the old default
        migrations.RunSQL(
            "UPDATE ids_idssettings SET max_idle_time = 1.0 WHERE max_idle_time = 2.0;",
            reverse_sql="UPDATE ids_idssettings SET max_idle_time = 2.0 WHERE max_idle_time = 1.0;"
        ),
    ]

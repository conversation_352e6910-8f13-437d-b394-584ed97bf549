# Generated by Django 4.2.10 on 2025-06-02 19:28

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("ids", "0006_remove_detection_fields"),
    ]

    operations = [
        migrations.AddField(
            model_name="idsalert",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="idsalert",
            name="status",
            field=models.CharField(
                choices=[
                    ("new", "New"),
                    ("acknowledged", "Acknowledged"),
                    ("resolved", "Resolved"),
                ],
                default="new",
                max_length=15,
            ),
        ),
        migrations.AddField(
            model_name="idsalert",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddIndex(
            model_name="idsalert",
            index=models.Index(
                fields=["status", "-timestamp"], name="ids_idsaler_status_8c3219_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="idsalert",
            index=models.Index(
                fields=["attack_type", "-timestamp"],
                name="ids_idsaler_attack__0a9376_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="idsalert",
            index=models.Index(
                fields=["-created_at"], name="ids_idsaler_created_06ee7d_idx"
            ),
        ),
    ]

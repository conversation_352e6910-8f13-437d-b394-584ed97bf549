#!/bin/bash

# Setup script to configure packet capture permissions for Flow Sentinel IDS
# This script sets up the necessary capabilities for packet capture without running as root

echo "=== Flow Sentinel IDS Packet Capture Setup ==="
echo "Configuring packet capture permissions..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "This setup script needs to be run with sudo."
    echo "Usage: sudo ./setup_packet_capture.sh"
    exit 1
fi

# Change to the backend directory
cd "$(dirname "$0")"

# Find the Python executable in the virtual environment
if [ -d "venv" ]; then
    PYTHON_EXEC="$(pwd)/venv/bin/python"
    DAPHNE_EXEC="$(pwd)/venv/bin/daphne"
    echo "Found virtual environment Python: $PYTHON_EXEC"
    echo "Found virtual environment Daphne: $DAPHNE_EXEC"
else
    PYTHON_EXEC=$(which python3)
    DAPHNE_EXEC=$(which daphne)
    echo "Using system Python: $PYTHON_EXEC"
    echo "Using system Daphne: $DAPHNE_EXEC"
fi

# Check if the Python executable exists
if [ ! -f "$PYTHON_EXEC" ]; then
    echo "Error: Python executable not found at $PYTHON_EXEC"
    exit 1
fi

# Set capabilities for packet capture on Python
echo "Setting packet capture capabilities on Python executable..."
setcap cap_net_raw,cap_net_admin=eip "$PYTHON_EXEC"

# Also set capabilities on daphne if it exists
if [ -f "$DAPHNE_EXEC" ]; then
    echo "Setting packet capture capabilities on Daphne executable..."
    setcap cap_net_raw,cap_net_admin=eip "$DAPHNE_EXEC"
fi

# Verify capabilities were set
echo "Verifying capabilities..."
echo "Python capabilities:"
getcap "$PYTHON_EXEC"
if [ -f "$DAPHNE_EXEC" ]; then
    echo "Daphne capabilities:"
    getcap "$DAPHNE_EXEC"
fi

if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Packet capture capabilities successfully configured!"
    echo "✓ You can now run the server as a regular user:"
    echo "  cd backend"
    echo "  ./start_server_regular.sh"
    echo ""
    echo "Or manually:"
    echo "  source venv/bin/activate"
    echo "  ./venv/bin/daphne flow_sentinel.asgi:application -b 0.0.0.0 -p 8000"
    echo ""
    echo "Note: If you update Python or the virtual environment, you'll need to run this script again."
else
    echo "✗ Failed to set capabilities. Please check your system configuration."
    exit 1
fi

#!/usr/bin/env python
"""
Script to update existing IDS settings to use wlan0 interface for Kali Linux
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'flow_sentinel.settings')
django.setup()

from ids.models import IDSSettings

def update_interface_settings():
    """Update existing settings to use wlan0 instead of Wi-Fi or auto"""
    try:
        # Get all settings objects
        settings_objects = IDSSettings.objects.all()
        
        updated_count = 0
        for settings in settings_objects:
            if settings.capture_interface in ['auto', 'Wi-Fi']:
                print(f"Updating interface from '{settings.capture_interface}' to 'wlan0'")
                settings.capture_interface = 'wlan0'
                settings.save()
                updated_count += 1
        
        if updated_count > 0:
            print(f"✓ Updated {updated_count} settings object(s)")
        else:
            print("✓ No settings needed updating")
            
        # If no settings exist, create default settings with wlan0
        if not settings_objects.exists():
            print("Creating default settings with wlan0 interface...")
            IDSSettings.objects.create(capture_interface='wlan0')
            print("✓ Created default settings")
            
    except Exception as e:
        print(f"✗ Error updating settings: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=== Updating IDS Interface Settings for Kali Linux ===")
    success = update_interface_settings()
    if success:
        print("✓ Interface settings updated successfully!")
    else:
        print("✗ Failed to update interface settings")

# Generated by Django 4.2.10 on 2025-04-07 16:12

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="IDSAlert",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("src_ip", models.Char<PERSON>ield(max_length=39)),
                ("dst_ip", models.Char<PERSON>ield(max_length=39)),
                ("src_port", models.IntegerField()),
                ("dst_port", models.IntegerField()),
                ("attack_type", models.CharField(max_length=100)),
                ("severity", models.Char<PERSON>ield(max_length=20)),
            ],
            options={
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="IDSLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("level", models.CharField(max_length=10)),
                ("message", models.TextField()),
            ],
            options={
                "ordering": ["-timestamp"],
            },
        ),
    ]

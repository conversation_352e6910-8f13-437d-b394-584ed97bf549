
import { AnalyticsData } from "@/types/analytics";

export async function fetchAnalyticsData(timeframe: string, timestamp?: number): Promise<AnalyticsData> {
  try {
    // Add cache-busting parameter
    const cacheBuster = timestamp || Date.now();
    const response = await fetch(`http://localhost:8000/api/ids/analytics?timeframe=${timeframe}&t=${cacheBuster}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch analytics data: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error in fetchAnalyticsData:', error);
    throw error;
  }
}

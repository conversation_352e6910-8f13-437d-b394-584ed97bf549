
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON><PERSON>, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AnalyticsHeaderProps {
  timeframe: string;
  onTimeframeChange: (value: string) => void;
  onDownloadCSV?: () => void;
}

export function AnalyticsHeader({ timeframe, onTimeframeChange, onDownloadCSV }: AnalyticsHeaderProps) {
  const { toast } = useToast();

  const handleDownloadCSV = async () => {
    try {
      if (onDownloadCSV) {
        onDownloadCSV();
      } else {
        // Fallback: download from backend API
        const response = await fetch(`http://localhost:8000/api/ids/analytics/export?timeframe=${timeframe}&format=csv`);

        if (!response.ok) {
          throw new Error('Failed to download CSV');
        }

        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics_${timeframe}_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        toast({
          title: "CSV Downloaded",
          description: "Analytics data has been exported to CSV file."
        });
      }
    } catch (error) {
      console.error('Error downloading CSV:', error);
      toast({
        title: "Download Failed",
        description: "Could not download CSV file. Please try again.",
        variant: "destructive"
      });
    }
  };
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight flex items-center gap-2">
          <BarChart className="h-6 w-6 text-primary" />
          Security Analytics
        </h1>
        <p className="text-muted-foreground">
          Threat intelligence and attack pattern analysis
        </p>
      </div>
      
      <div className="flex gap-2 items-center">
        <Select 
          defaultValue={timeframe} 
          onValueChange={onTimeframeChange}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Time Period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="24h">Last 24 Hours</SelectItem>
            <SelectItem value="7d">Last 7 Days</SelectItem>
            <SelectItem value="30d">Last 30 Days</SelectItem>
            <SelectItem value="90d">Last 90 Days</SelectItem>
          </SelectContent>
        </Select>
        
        <Button
          variant="outline"
          size="icon"
          onClick={handleDownloadCSV}
          title="Download CSV"
        >
          <Download className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}


import { <PERSON><PERSON><PERSON><PERSON>gle, BarChart3, Bell, Globe, Home, Menu, Monitor, Settings, Shield, Wifi } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { useToast } from "@/hooks/use-toast";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useState } from "react";

export function Navbar() {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const showNotification = () => {
    toast({
      title: "New Alerts",
      description: "You have 3 new security alerts to review",
      variant: "default",
    });
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 max-w-full items-center">
        {isMobile && (
          <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="mr-2" aria-label="Menu">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-64 p-0 bg-sidebar">
              <div className="flex h-14 items-center border-b border-border/40 px-4">
                <Link to="/" className="flex items-center gap-2 font-semibold">
                  <Shield className="h-6 w-6 text-primary" />
                  <span>Flow Sentinel</span>
                </Link>
              </div>
              <div className="flex-1 overflow-auto py-2">
                <nav className="grid gap-1 px-2">
                  {[
                    { name: "Dashboard", href: "/", icon: Home },
                    { name: "Alerts", href: "/alerts", icon: AlertTriangle },
                    { name: "Traffic", href: "/traffic", icon: Wifi },
                    { name: "Analytics", href: "/analytics", icon: BarChart3 },
                    { name: "System Resources", href: "/system-resources", icon: Monitor },
                    { name: "Settings", href: "/settings", icon: Settings }
                  ].map((item) => (
                    <Link
                      key={item.href}
                      to={item.href}
                      className="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent/50 transition-colors"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <item.icon className="h-4 w-4" />
                      {item.name}
                    </Link>
                  ))}
                </nav>
              </div>
            </SheetContent>
          </Sheet>
        )}

        <div className="flex items-center gap-2 font-semibold mr-4">
          <Shield className="h-6 w-6 text-primary" />
          <span className="hidden md:inline-block">Flow Sentinel Protect</span>
        </div>

        <div className="flex-1"></div>

        <nav className="flex items-center gap-2">
          <ThemeToggle />

          <Button
            variant="ghost"
            size="icon"
            onClick={showNotification}
            aria-label="Notifications"
          >
            <Bell className="h-5 w-5" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            asChild
            aria-label="Settings"
          >
            <Link to="/settings">
              <Settings className="h-5 w-5" />
            </Link>
          </Button>
        </nav>
      </div>
    </header>
  );
}
